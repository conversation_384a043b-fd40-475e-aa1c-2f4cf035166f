#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام مع إصلاح مشاكل المزامنة
Start System with Sync Issues Fixed
"""

import subprocess
import threading
import time
import sys
import os
import socket
import webbrowser
from datetime import datetime

class FixedSyncSystemManager:
    def __init__(self):
        self.processes = []
        self.main_server_port = 8000
        self.sync_server_port = 8001
        self.running = False
        self.local_ip = self.get_local_ip()
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def print_header(self):
        """طباعة رأس النظام"""
        print("=" * 70)
        print("🚀 نظام إدارة أمن المعلومات مع المزامنة المحسنة")
        print("   Enhanced ISMS with Fixed Synchronization")
        print("=" * 70)
        print(f"🌐 عنوان IP المحلي: {self.local_ip}")
        print()
    
    def setup_data_directory(self):
        """إعداد مجلد البيانات"""
        print("📁 إعداد مجلد البيانات...")
        
        data_dir = "data"
        if not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
            print("   ✅ تم إنشاء مجلد البيانات")
        
        # إنشاء الملفات الأساسية
        files_to_create = {
            "users.json": {},
            "events.json": [],
            "activities.json": [],
            "sync_log.json": []
        }
        
        for filename, default_content in files_to_create.items():
            file_path = os.path.join(data_dir, filename)
            if not os.path.exists(file_path):
                try:
                    import json
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(default_content, f, ensure_ascii=False, indent=2)
                    print(f"   ✅ تم إنشاء {filename}")
                except Exception as e:
                    print(f"   ❌ فشل في إنشاء {filename}: {e}")
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = os.path.join(data_dir, "backups")
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir, exist_ok=True)
            print("   ✅ تم إنشاء مجلد النسخ الاحتياطية")
        
        print("✅ تم إعداد مجلد البيانات بنجاح")
        print()
    
    def check_ports(self):
        """فحص توفر المنافذ"""
        print("🔌 فحص المنافذ...")
        
        def is_port_available(port):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', port))
                    return result != 0
            except:
                return False
        
        main_available = is_port_available(self.main_server_port)
        sync_available = is_port_available(self.sync_server_port)
        
        print(f"   المنفذ {self.main_server_port}: {'✅ متاح' if main_available else '⚠️  مستخدم'}")
        print(f"   المنفذ {self.sync_server_port}: {'✅ متاح' if sync_available else '⚠️  مستخدم'}")
        
        if not main_available:
            print("   💡 الخادم الرئيسي يعمل بالفعل")
        
        if not sync_available:
            print("   💡 خادم المزامنة يعمل بالفعل")
        
        print()
        return main_available, sync_available
    
    def start_main_server(self):
        """تشغيل الخادم الرئيسي"""
        print("🌐 تشغيل الخادم الرئيسي...")
        
        try:
            process = subprocess.Popen([
                sys.executable, 'start-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('main_server', process))
            print(f"   ✅ الخادم الرئيسي يعمل على المنفذ {self.main_server_port}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في تشغيل الخادم الرئيسي: {e}")
            return False
    
    def start_sync_server(self):
        """تشغيل خادم المزامنة"""
        print("🔄 تشغيل خادم المزامنة...")
        
        try:
            process = subprocess.Popen([
                sys.executable, 'data-sync-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('sync_server', process))
            print(f"   ✅ خادم المزامنة يعمل على المنفذ {self.sync_server_port}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في تشغيل خادم المزامنة: {e}")
            return False
    
    def wait_for_servers(self):
        """انتظار تشغيل الخوادم"""
        print("⏳ انتظار تشغيل الخوادم...")
        
        def wait_for_port(port, name):
            for i in range(15):  # انتظار 15 ثانية
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(1)
                        result = s.connect_ex(('localhost', port))
                        if result == 0:
                            print(f"   ✅ {name} جاهز")
                            return True
                except:
                    pass
                time.sleep(1)
            print(f"   ⚠️  {name} قد لا يكون جاهزاً")
            return False
        
        # انتظار الخوادم
        main_ready = wait_for_port(self.main_server_port, "الخادم الرئيسي")
        sync_ready = wait_for_port(self.sync_server_port, "خادم المزامنة")
        
        print()
        return main_ready, sync_ready
    
    def test_sync_connection(self):
        """اختبار الاتصال بخادم المزامنة"""
        print("🔄 اختبار خادم المزامنة...")
        
        import requests
        
        sync_urls = [
            f"http://localhost:{self.sync_server_port}",
            f"http://127.0.0.1:{self.sync_server_port}",
            f"http://{self.local_ip}:{self.sync_server_port}"
        ]
        
        working_urls = []
        
        for url in sync_urls:
            try:
                response = requests.get(f"{url}/api/sync/status", timeout=3)
                if response.status_code == 200:
                    status = response.json()
                    print(f"   ✅ خادم المزامنة يعمل: {url}")
                    print(f"      📊 المستخدمين: {status.get('users_count', 0)}")
                    print(f"      📊 الأحداث: {status.get('events_count', 0)}")
                    print(f"      📊 الأنشطة: {status.get('activities_count', 0)}")
                    working_urls.append(url)
                    break
            except:
                continue
        
        if working_urls:
            print("✅ خادم المزامنة يعمل بشكل صحيح")
        else:
            print("❌ خادم المزامنة لا يستجيب")
        
        print()
        return len(working_urls) > 0
    
    def open_browser(self):
        """فتح المتصفح"""
        print("🌐 فتح المتصفح...")
        
        try:
            url = f"http://localhost:{self.main_server_port}/login.html"
            webbrowser.open(url)
            print(f"   ✅ تم فتح المتصفح: {url}")
        except Exception as e:
            print(f"   ⚠️  فشل في فتح المتصفح: {e}")
        
        print()
    
    def show_access_info(self):
        """عرض معلومات الوصول"""
        print("🔗 معلومات الوصول:")
        print("-" * 60)
        
        print("📍 الوصول المحلي:")
        print(f"   🏠 الصفحة الرئيسية: http://localhost:{self.main_server_port}")
        print(f"   📄 تسجيل الدخول: http://localhost:{self.main_server_port}/login.html")
        print(f"   🔄 خادم المزامنة: http://localhost:{self.sync_server_port}/api/sync/status")
        
        print("\n🌍 الوصول من الشبكة:")
        print(f"   🏠 الصفحة الرئيسية: http://{self.local_ip}:{self.main_server_port}")
        print(f"   📄 تسجيل الدخول: http://{self.local_ip}:{self.main_server_port}/login.html")
        print(f"   🔄 خادم المزامنة: http://{self.local_ip}:{self.sync_server_port}/api/sync/status")
        
        print("\n🔑 بيانات الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔒 كلمة المرور: admin123")
        
        print("\n🔧 ميزات المزامنة:")
        print("   ✅ مزامنة تلقائية كل 30 ثانية")
        print("   ✅ مشاركة البيانات عبر الشبكة")
        print("   ✅ نسخ احتياطي تلقائي")
        print("   ✅ تتبع الحركات والأنشطة")
        print("   ✅ إصلاح تلقائي للمشاكل")
        
        print("\n💡 نصائح:")
        print("   • شارك روابط الشبكة مع المستخدمين الآخرين")
        print("   • راجع ملف ACCESS-LINKS.txt للروابط الكاملة")
        print("   • استخدم test-network-sync.py لاختبار المزامنة")
        
        print("\n⚠️  للإيقاف: اضغط Ctrl+C")
        print("-" * 60)
        print()
    
    def create_access_links_file(self):
        """إنشاء ملف روابط الوصول"""
        content = f"""# 🌐 روابط الوصول لنظام إدارة أمن المعلومات
# تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# عنوان IP المحلي: {self.local_ip}

## 📍 الوصول المحلي
الصفحة الرئيسية: http://localhost:{self.main_server_port}
تسجيل الدخول: http://localhost:{self.main_server_port}/login.html
خادم المزامنة: http://localhost:{self.sync_server_port}/api/sync/status

## 🌍 الوصول من الشبكة
الصفحة الرئيسية: http://{self.local_ip}:{self.main_server_port}
تسجيل الدخول: http://{self.local_ip}:{self.main_server_port}/login.html
خادم المزامنة: http://{self.local_ip}:{self.sync_server_port}/api/sync/status

## 🔑 بيانات الدخول
اسم المستخدم: admin
كلمة المرور: admin123

## 📋 مستخدمون إضافيون
• analyst / analyst123 (محلل أمني)
• operator / operator123 (مشغل)

## 🔧 أدوات الاختبار
python test-network-sync.py    # اختبار المزامنة الشامل
python fix-sync-issues.py      # إصلاح مشاكل المزامنة
python network-setup.py        # إعداد الشبكة

## 🛡️ إعدادات جدار الحماية (Windows)
netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport={self.main_server_port}
netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport={self.sync_server_port}

## 🛡️ إعدادات جدار الحماية (Linux)
sudo ufw allow {self.main_server_port}/tcp comment "ISMS Main Server"
sudo ufw allow {self.sync_server_port}/tcp comment "ISMS Sync Server"
"""
        
        try:
            with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ تم إنشاء ملف ACCESS-LINKS.txt")
        except Exception as e:
            print(f"⚠️  خطأ في إنشاء ملف الروابط: {e}")
    
    def stop_system(self):
        """إيقاف النظام"""
        print("\n🛑 إيقاف النظام...")
        self.running = False
        
        for name, process in self.processes:
            try:
                print(f"   🛑 إيقاف {name}...")
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ تم إيقاف {name}")
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  فرض إيقاف {name}...")
                process.kill()
            except Exception as e:
                print(f"   ❌ خطأ في إيقاف {name}: {e}")
        
        print("✅ تم إيقاف النظام بنجاح")
    
    def run_system(self):
        """تشغيل النظام الكامل"""
        self.print_header()
        
        # إعداد مجلد البيانات
        self.setup_data_directory()
        
        # فحص المنافذ
        main_available, sync_available = self.check_ports()
        
        # تشغيل الخوادم
        servers_started = []
        
        if main_available:
            if self.start_main_server():
                servers_started.append('main')
        else:
            print("💡 الخادم الرئيسي يعمل بالفعل")
            servers_started.append('main')
        
        if sync_available:
            if self.start_sync_server():
                servers_started.append('sync')
        else:
            print("💡 خادم المزامنة يعمل بالفعل")
            servers_started.append('sync')
        
        if len(servers_started) == 0:
            print("❌ فشل في تشغيل أي خادم")
            return False
        
        # انتظار تشغيل الخوادم
        self.wait_for_servers()
        
        # اختبار المزامنة
        self.test_sync_connection()
        
        # فتح المتصفح
        self.open_browser()
        
        # إنشاء ملف الروابط
        self.create_access_links_file()
        
        # عرض معلومات الوصول
        self.show_access_info()
        
        # تشغيل النظام
        self.running = True
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        return True

def main():
    """الدالة الرئيسية"""
    manager = FixedSyncSystemManager()
    
    try:
        success = manager.run_system()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return 1
    finally:
        manager.stop_system()

if __name__ == "__main__":
    sys.exit(main())

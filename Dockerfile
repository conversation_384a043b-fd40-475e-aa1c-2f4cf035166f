# نظام إدارة أمن المعلومات - Docker Image
# Information Security Management System - Docker Image

FROM nginx:alpine

# تثبيت الأدوات المطلوبة
RUN apk add --no-cache \
    python3 \
    py3-pip \
    curl \
    && rm -rf /var/cache/apk/*

# إنشاء مجلد التطبيق
WORKDIR /usr/share/nginx/html

# نسخ ملفات التطبيق
COPY . .

# إعداد صلاحيات الملفات
RUN chmod -R 755 /usr/share/nginx/html

# إنشاء ملف إعدادات Nginx
RUN cat > /etc/nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index login.html index.html;
    
    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # إعدادات التخزين المؤقت
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # إعادة توجيه الصفحة الرئيسية
    location = / {
        return 301 /login.html;
    }
    
    # معالجة الأخطاء
    error_page 404 /login.html;
    error_page 500 502 503 504 /login.html;
    
    # إعدادات الأمان الإضافية
    location ~ /\. {
        deny all;
    }
    
    location ~* \.(env|log|ini)$ {
        deny all;
    }
}
EOF

# إنشاء ملف بدء التشغيل
RUN cat > /docker-entrypoint.sh << 'EOF'
#!/bin/sh

echo "🔐 بدء تشغيل نظام إدارة أمن المعلومات"
echo "================================================"
echo "🌐 الخادم: http://localhost"
echo "📍 الصفحة الرئيسية: http://localhost/login.html"
echo "🔑 اسم المستخدم: admin"
echo "🔑 كلمة المرور: admin123"
echo "================================================"

# بدء تشغيل Nginx
exec nginx -g 'daemon off;'
EOF

RUN chmod +x /docker-entrypoint.sh

# فتح المنفذ
EXPOSE 80

# تشغيل التطبيق
ENTRYPOINT ["/docker-entrypoint.sh"]

# إعدادات Docker
LABEL maintainer="Information Security Team"
LABEL description="نظام إدارة أمن المعلومات - Information Security Management System"
LABEL version="1.0.0"

# متغيرات البيئة
ENV NGINX_HOST=localhost
ENV NGINX_PORT=80

@echo off
echo ======================================================
echo 🚀 تشغيل نظام إدارة أمن المعلومات مع المزامنة
echo    Starting ISMS with Data Synchronization
echo ======================================================
echo.

echo 📁 إعداد مجلد البيانات...
if not exist "data" mkdir data
if not exist "data\users.json" echo {} > "data\users.json"
if not exist "data\events.json" echo [] > "data\events.json"
if not exist "data\activities.json" echo [] > "data\activities.json"
if not exist "data\sync_log.json" echo [] > "data\sync_log.json"
if not exist "data\backups" mkdir "data\backups"
echo ✅ تم إعداد مجلد البيانات

echo.
echo 🌐 تشغيل الخادم الرئيسي...
start "ISMS Main Server" python start-server.py

echo.
echo 🔄 تشغيل خادم المزامنة...
start "ISMS Sync Server" python data-sync-server.py

echo.
echo ⏳ انتظار تشغيل الخوادم...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 فتح المتصفح...
start http://localhost:8000/login.html

echo.
echo ======================================================
echo 🎉 تم تشغيل النظام بنجاح!
echo.
echo 🔗 روابط الوصول:
echo    📍 محلي: http://localhost:8000/login.html
echo    🌍 شبكة: http://*************:8000/login.html
echo.
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo.
echo 💡 لإيقاف النظام: أغلق نوافذ الخوادم
echo ======================================================
pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل مع خادم المزامنة
Complete System Startup with Data Sync Server
"""

import subprocess
import threading
import time
import sys
import os
import signal
import webbrowser
from datetime import datetime

class CompleteSystemManager:
    def __init__(self):
        self.processes = []
        self.main_server_port = 8000
        self.sync_server_port = 8001
        self.running = False
        
    def print_header(self):
        """طباعة رأس النظام"""
        print("=" * 70)
        print("🚀 نظام إدارة أمن المعلومات الكامل مع المزامنة")
        print("   Complete ISMS with Data Synchronization")
        print("=" * 70)
        print()
    
    def check_dependencies(self):
        """التحقق من المتطلبات"""
        print("🔍 التحقق من المتطلبات...")
        
        # التحقق من Python
        try:
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
                print("❌ يتطلب Python 3.7 أو أحدث")
                return False
            print(f"   ✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        except Exception as e:
            print(f"   ❌ خطأ في فحص Python: {e}")
            return False
        
        # التحقق من الملفات المطلوبة
        required_files = [
            'start-server.py',
            'data-sync-server.py',
            'index.html',
            'login.html',
            'script.js',
            'data-sync-client.js'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - مفقود")
                missing_files.append(file)
        
        if missing_files:
            print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
            return False
        
        print("✅ جميع المتطلبات متوفرة")
        print()
        return True
    
    def start_main_server(self):
        """تشغيل الخادم الرئيسي"""
        print("🌐 تشغيل الخادم الرئيسي...")
        
        try:
            process = subprocess.Popen([
                sys.executable, 'start-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('main_server', process))
            print(f"   ✅ الخادم الرئيسي يعمل على المنفذ {self.main_server_port}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في تشغيل الخادم الرئيسي: {e}")
            return False
    
    def start_sync_server(self):
        """تشغيل خادم المزامنة"""
        print("🔄 تشغيل خادم المزامنة...")
        
        try:
            process = subprocess.Popen([
                sys.executable, 'data-sync-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('sync_server', process))
            print(f"   ✅ خادم المزامنة يعمل على المنفذ {self.sync_server_port}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في تشغيل خادم المزامنة: {e}")
            return False
    
    def wait_for_servers(self):
        """انتظار تشغيل الخوادم"""
        print("⏳ انتظار تشغيل الخوادم...")
        
        import socket
        
        # انتظار الخادم الرئيسي
        for i in range(10):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', self.main_server_port))
                    if result == 0:
                        print("   ✅ الخادم الرئيسي جاهز")
                        break
            except:
                pass
            time.sleep(1)
        else:
            print("   ⚠️  الخادم الرئيسي قد لا يكون جاهزاً")
        
        # انتظار خادم المزامنة
        for i in range(10):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', self.sync_server_port))
                    if result == 0:
                        print("   ✅ خادم المزامنة جاهز")
                        break
            except:
                pass
            time.sleep(1)
        else:
            print("   ⚠️  خادم المزامنة قد لا يكون جاهزاً")
        
        print()
    
    def open_browser(self):
        """فتح المتصفح"""
        print("🌐 فتح المتصفح...")
        
        try:
            url = f"http://localhost:{self.main_server_port}/login.html"
            webbrowser.open(url)
            print(f"   ✅ تم فتح المتصفح: {url}")
        except Exception as e:
            print(f"   ⚠️  فشل في فتح المتصفح: {e}")
            print(f"   🔗 افتح الرابط يدوياً: http://localhost:{self.main_server_port}/login.html")
        
        print()
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        import socket
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"

    def show_system_info(self):
        """عرض معلومات النظام"""
        local_ip = self.get_local_ip()

        print("📋 معلومات النظام:")
        print("-" * 60)
        print(f"🌐 الخادم الرئيسي: المنفذ {self.main_server_port}")
        print(f"🔄 خادم المزامنة: المنفذ {self.sync_server_port}")
        print()
        print("🔗 روابط الوصول المحلي:")
        print(f"   📄 تسجيل الدخول: http://localhost:{self.main_server_port}/login.html")
        print(f"   🏠 الصفحة الرئيسية: http://localhost:{self.main_server_port}/index.html")
        print(f"   📡 API المزامنة: http://localhost:{self.sync_server_port}/api/sync/status")
        print()
        print("🌍 روابط الوصول من الشبكة:")
        print(f"   📄 تسجيل الدخول: http://{local_ip}:{self.main_server_port}/login.html")
        print(f"   🏠 الصفحة الرئيسية: http://{local_ip}:{self.main_server_port}/index.html")
        print(f"   📡 API المزامنة: http://{local_ip}:{self.sync_server_port}/api/sync/status")
        print()
        print("🔑 بيانات الدخول الافتراضية:")
        print("   👤 اسم المستخدم: admin")
        print("   🔒 كلمة المرور: admin123")
        print()
        print("🔧 ميزات المزامنة:")
        print("   ✅ مزامنة تلقائية كل 30 ثانية")
        print("   ✅ مشاركة البيانات عبر الشبكة")
        print("   ✅ نسخ احتياطي تلقائي")
        print("   ✅ تتبع الحركات والأنشطة")
        print("   ✅ وصول من جميع أجهزة الشبكة")
        print()
        print("💡 نصائح للاستخدام:")
        print("   • شارك روابط الشبكة مع المستخدمين الآخرين")
        print("   • تأكد من إعدادات جدار الحماية")
        print("   • استخدم network-setup.py لإعداد الشبكة")
        print()
        print("⚠️  للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        print()
    
    def monitor_processes(self):
        """مراقبة العمليات"""
        while self.running:
            try:
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name} توقف بشكل غير متوقع")
                        # يمكن إضافة إعادة تشغيل تلقائي هنا
                
                time.sleep(5)
            except Exception as e:
                print(f"خطأ في مراقبة العمليات: {e}")
                break
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        def signal_handler(signum, frame):
            print("\n🛑 تم استلام إشارة الإيقاف...")
            self.stop_system()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def stop_system(self):
        """إيقاف النظام"""
        print("🛑 إيقاف النظام...")
        self.running = False
        
        for name, process in self.processes:
            try:
                print(f"   🛑 إيقاف {name}...")
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ تم إيقاف {name}")
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  فرض إيقاف {name}...")
                process.kill()
            except Exception as e:
                print(f"   ❌ خطأ في إيقاف {name}: {e}")
        
        print("✅ تم إيقاف النظام بنجاح")
    
    def start_system(self):
        """تشغيل النظام الكامل"""
        self.print_header()
        
        # التحقق من المتطلبات
        if not self.check_dependencies():
            return False
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # تشغيل الخوادم
        if not self.start_main_server():
            return False
        
        if not self.start_sync_server():
            return False
        
        # انتظار تشغيل الخوادم
        self.wait_for_servers()
        
        # فتح المتصفح
        self.open_browser()
        
        # عرض معلومات النظام
        self.show_system_info()
        
        # بدء المراقبة
        self.running = True
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        # انتظار الإيقاف
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        return True

def main():
    """الدالة الرئيسية"""
    manager = CompleteSystemManager()
    
    try:
        success = manager.start_system()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return 1
    finally:
        manager.stop_system()

if __name__ == "__main__":
    sys.exit(main())

# 🌐 دليل إعداد الشبكة - نظام إدارة أمن المعلومات
# Network Setup Guide - Information Security Management System

## 📋 المحتويات
- [التثبيت السريع](#التثبيت-السريع)
- [إعداد الشبكة](#إعداد-الشبكة)
- [طرق التشغيل](#طرق-التشغيل)
- [الوصول من أجهزة أخرى](#الوصول-من-أجهزة-أخرى)
- [استكشاف الأخطاء](#استكشاف-الأخطاء)

## 🚀 التثبيت السريع

### الطريقة الأولى: التثبيت التلقائي

**Windows:**
```batch
# انقر نقراً مزدوجاً على
install.bat
```

**macOS/Linux:**
```bash
chmod +x install.sh
./install.sh
```

### الطريقة الثانية: التشغيل المباشر

```bash
# تشغيل الخادم المخصص
python3 start-server.py

# أو الخادم البسيط
python3 -m http.server 8000
```

## 🌐 إعداد الشبكة

### 1. فحص إعدادات الشبكة
```bash
python3 network-config.py
```

هذا الأمر سيقوم بـ:
- فحص واجهات الشبكة المتاحة
- العثور على منفذ متاح
- إنشاء ملف `ACCESS-LINKS.txt` مع جميع روابط الوصول
- حفظ الإعدادات في `network-config.json`

### 2. الحصول على عنوان IP

**Windows:**
```cmd
ipconfig
```

**macOS/Linux:**
```bash
ifconfig
# أو
ip addr show
```

### 3. اختبار الاتصال
```bash
# اختبار المنفذ محلياً
telnet localhost 8000

# اختبار من جهاز آخر
telnet [IP_ADDRESS] 8000
```

## 🔧 طرق التشغيل

### الطريقة 1: الخادم المخصص (موصى به)
```bash
python3 start-server.py
```

**المميزات:**
- عرض عنوان IP تلقائياً
- فتح المتصفح تلقائياً
- إعدادات أمان محسنة
- رسائل حالة واضحة

### الطريقة 2: خادم Python البسيط
```bash
python3 -m http.server 8000
```

### الطريقة 3: باستخدام Docker
```bash
# بناء الصورة
docker build -t isms .

# تشغيل الحاوية
docker run -d -p 8000:80 --name isms-container isms

# أو باستخدام docker-compose
docker-compose up -d
```

## 📱 الوصول من أجهزة أخرى

### 1. تحديد عنوان IP
بعد تشغيل `python3 network-config.py` ستحصل على:
```
📍 عنوان IP الرئيسي: *************
🔌 المنفذ: 8000

🔗 روابط الوصول:
   • local: http://localhost:8000/login.html
   • network: http://*************:8000/login.html
```

### 2. إعداد جدار الحماية

**Windows:**
```cmd
# السماح بالمنفذ في Windows Firewall
netsh advfirewall firewall add rule name="ISMS Server" dir=in action=allow protocol=TCP localport=8000
```

**macOS:**
```bash
# إضافة قاعدة في pfctl (إذا كان مفعلاً)
sudo pfctl -f /etc/pf.conf
```

**Linux (Ubuntu/Debian):**
```bash
# باستخدام ufw
sudo ufw allow 8000

# باستخدام iptables
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

### 3. الوصول من الأجهزة المحمولة

افتح المتصفح على الهاتف أو التابلت واذهب إلى:
```
http://[IP_ADDRESS]:8000/login.html
```

مثال:
```
http://*************:8000/login.html
```

## 🔐 بيانات تسجيل الدخول

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 🛠️ استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من أجهزة أخرى

**الحلول:**
1. **تحقق من تشغيل الخادم:**
   ```bash
   netstat -an | grep 8000
   ```

2. **تحقق من جدار الحماية:**
   - Windows: إعدادات Windows Defender
   - macOS: تفضيلات النظام > الأمان والخصوصية > جدار الحماية
   - Linux: `sudo ufw status`

3. **تحقق من عنوان IP:**
   ```bash
   python3 network-config.py
   ```

4. **اختبار الاتصال:**
   ```bash
   ping [IP_ADDRESS]
   telnet [IP_ADDRESS] 8000
   ```

### المشكلة: المنفذ مستخدم

**الحل:**
```bash
# العثور على العملية التي تستخدم المنفذ
# Windows
netstat -ano | findstr :8000

# macOS/Linux
lsof -i :8000

# إيقاف العملية
kill [PID]
```

### المشكلة: Python غير مثبت

**الحلول:**
- **Windows:** تحميل من [python.org](https://python.org)
- **macOS:** `brew install python3`
- **Ubuntu/Debian:** `sudo apt install python3`
- **CentOS/RHEL:** `sudo yum install python3`

## 📊 مراقبة الأداء

### عرض الاتصالات النشطة
```bash
# Windows
netstat -an | findstr :8000

# macOS/Linux
netstat -an | grep :8000
```

### مراقبة سجلات الخادم
الخادم المخصص يعرض:
- عنوان IP للزائر
- الوقت والتاريخ
- الصفحة المطلوبة
- حالة الاستجابة

## 🔒 نصائح الأمان

1. **غيّر كلمة المرور الافتراضية**
2. **استخدم شبكة آمنة فقط**
3. **راقب سجلات الوصول**
4. **أوقف الخادم عند عدم الحاجة**
5. **استخدم HTTPS في البيئة الإنتاجية**

## 📞 الدعم الفني

إذا واجهت مشاكل:
1. راجع ملف `ACCESS-LINKS.txt`
2. تحقق من `network-config.json`
3. شغّل `python3 network-config.py` مرة أخرى
4. راجع هذا الدليل

---

**تم إنشاؤه بواسطة نظام إدارة أمن المعلومات**  
**Information Security Management System**

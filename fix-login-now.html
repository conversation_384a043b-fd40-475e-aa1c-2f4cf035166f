<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح تسجيل الدخول الفوري</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255,255,255,0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.2em;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: #3498db;
            width: 0%;
            transition: width 0.5s ease;
        }
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: right;
        }
        .step {
            display: none;
            animation: fadeIn 0.5s ease;
        }
        .step.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح تسجيل الدخول الفوري</h1>
        
        <!-- الخطوة 1: التشخيص -->
        <div id="step1" class="step active">
            <div class="info">
                <h3>🔍 تشخيص المشكلة</h3>
                <p>سيتم فحص بيانات المستخدمين وإصلاح المشكلة تلقائياً</p>
            </div>
            <button onclick="startDiagnosis()">بدء التشخيص والإصلاح</button>
        </div>
        
        <!-- الخطوة 2: الإصلاح -->
        <div id="step2" class="step">
            <div class="warning">
                <h3>⚙️ جاري الإصلاح...</h3>
                <div class="progress">
                    <div id="progressBar" class="progress-bar"></div>
                </div>
                <p id="progressText">جاري فحص البيانات...</p>
            </div>
        </div>
        
        <!-- الخطوة 3: النتيجة -->
        <div id="step3" class="step">
            <div id="result" class="status"></div>
            <div id="usersList"></div>
            <button class="btn-success" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <button onclick="window.location.href='login.html'">الذهاب لتسجيل الدخول</button>
        </div>
        
        <!-- الخطوة 4: الاختبار -->
        <div id="step4" class="step">
            <div class="info">
                <h3>🧪 اختبار تسجيل الدخول</h3>
                <p>اختبار سريع للتأكد من عمل النظام</p>
            </div>
            <div id="testResult"></div>
            <button class="btn-success" onclick="window.location.href='login.html'">تسجيل الدخول الآن</button>
            <button onclick="restartProcess()">إعادة الإصلاح</button>
        </div>
    </div>

    <script>
        let currentStep = 1;
        
        function showStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step}`).classList.add('active');
            currentStep = step;
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        async function startDiagnosis() {
            showStep(2);
            
            // الخطوة 1: فحص localStorage
            updateProgress(20, 'فحص بيانات localStorage...');
            await sleep(1000);
            
            const existingUsers = localStorage.getItem('systemUsers');
            console.log('Existing users in localStorage:', existingUsers);
            
            // الخطوة 2: تحميل البيانات من الخادم
            updateProgress(40, 'تحميل بيانات المستخدمين من الخادم...');
            await sleep(1000);
            
            try {
                const response = await fetch('/data/users.json');
                if (response.ok) {
                    const serverUsers = await response.json();
                    console.log('Users from server:', serverUsers);
                    
                    // الخطوة 3: حفظ البيانات في localStorage
                    updateProgress(60, 'حفظ البيانات في localStorage...');
                    await sleep(1000);
                    
                    localStorage.setItem('systemUsers', JSON.stringify(serverUsers));
                    
                    // الخطوة 4: التحقق من الحفظ
                    updateProgress(80, 'التحقق من صحة البيانات...');
                    await sleep(1000);
                    
                    const savedUsers = JSON.parse(localStorage.getItem('systemUsers'));
                    
                    if (savedUsers && Object.keys(savedUsers).length > 0) {
                        updateProgress(100, 'تم الإصلاح بنجاح!');
                        await sleep(1000);
                        
                        showFixResult(true, savedUsers);
                    } else {
                        throw new Error('فشل في حفظ البيانات');
                    }
                } else {
                    throw new Error('فشل في تحميل البيانات من الخادم');
                }
            } catch (error) {
                console.error('Error:', error);
                updateProgress(100, 'حدث خطأ في الإصلاح');
                await sleep(1000);
                
                // إنشاء المستخدمين الافتراضيين
                createDefaultUsers();
            }
        }
        
        function createDefaultUsers() {
            updateProgress(60, 'إنشاء المستخدمين الافتراضيين...');
            
            // دالة التشفير
            function hashPassword(password) {
                let hash = 0;
                for (let i = 0; i < password.length; i++) {
                    const char = password.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return hash.toString();
            }
            
            const defaultUsers = {
                "admin": {
                    "id": "admin",
                    "username": "admin",
                    "password": hashPassword("admin123"),
                    "fullName": "مدير النظام",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "analyst": {
                    "id": "analyst",
                    "username": "analyst",
                    "password": hashPassword("analyst123"),
                    "fullName": "محلل أمني",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "permissions": ["read", "write", "view_analytics"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "operator": {
                    "id": "operator",
                    "username": "operator",
                    "password": hashPassword("operator123"),
                    "fullName": "مشغل النظام",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "permissions": ["read"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                }
            };
            
            try {
                localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                updateProgress(100, 'تم إنشاء المستخدمين الافتراضيين!');
                setTimeout(() => showFixResult(true, defaultUsers), 1000);
            } catch (error) {
                updateProgress(100, 'فشل في إنشاء المستخدمين');
                setTimeout(() => showFixResult(false, null), 1000);
            }
        }
        
        function showFixResult(success, users) {
            showStep(3);
            
            const resultDiv = document.getElementById('result');
            const usersListDiv = document.getElementById('usersList');
            
            if (success && users) {
                resultDiv.className = 'status success';
                resultDiv.innerHTML = `
                    <h3>✅ تم إصلاح المشكلة بنجاح!</h3>
                    <p>تم تحميل ${Object.keys(users).length} مستخدم وحفظهم في localStorage</p>
                `;
                
                let usersList = '<div class="user-info"><h4>👥 المستخدمين المتاحين:</h4>';
                for (const [username, user] of Object.entries(users)) {
                    const password = username === 'admin' ? 'admin123' : 
                                   username === 'analyst' ? 'analyst123' : 'operator123';
                    usersList += `
                        <p><strong>${user.fullName}</strong></p>
                        <p>اسم المستخدم: <code>${username}</code></p>
                        <p>كلمة المرور: <code>${password}</code></p>
                        <p>الدور: ${user.role}</p>
                        <hr>
                    `;
                }
                usersList += '</div>';
                usersListDiv.innerHTML = usersList;
                
            } else {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = `
                    <h3>❌ فشل في الإصلاح</h3>
                    <p>لم نتمكن من إصلاح المشكلة تلقائياً</p>
                    <p>يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني</p>
                `;
                usersListDiv.innerHTML = '';
            }
        }
        
        async function testLogin() {
            showStep(4);
            
            const testResultDiv = document.getElementById('testResult');
            testResultDiv.innerHTML = '<p>جاري اختبار تسجيل الدخول...</p>';
            
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                
                if (!users || !users.admin) {
                    throw new Error('بيانات المستخدمين غير موجودة');
                }
                
                // اختبار تشفير كلمة المرور
                function hashPassword(password) {
                    let hash = 0;
                    for (let i = 0; i < password.length; i++) {
                        const char = password.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return hash.toString();
                }
                
                const testPassword = hashPassword('admin123');
                const adminUser = users.admin;
                
                if (adminUser.password === testPassword) {
                    testResultDiv.className = 'status success';
                    testResultDiv.innerHTML = `
                        <h3>✅ اختبار تسجيل الدخول نجح!</h3>
                        <p>يمكنك الآن تسجيل الدخول باستخدام:</p>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin123</p>
                    `;
                } else {
                    throw new Error('كلمة المرور لا تطابق');
                }
                
            } catch (error) {
                testResultDiv.className = 'status error';
                testResultDiv.innerHTML = `
                    <h3>❌ فشل في اختبار تسجيل الدخول</h3>
                    <p>الخطأ: ${error.message}</p>
                `;
            }
        }
        
        function restartProcess() {
            showStep(1);
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('صفحة إصلاح تسجيل الدخول جاهزة');
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        .info { border-left: 4px solid #17a2b8; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص تسجيل الدخول</h1>
        
        <div class="card info">
            <h3>معلومات النظام</h3>
            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
            <p><strong>localStorage:</strong> <span id="localStorageStatus"></span></p>
            <p><strong>JavaScript:</strong> <span id="jsStatus">✅ مفعل</span></p>
            <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
        </div>
        
        <div class="card">
            <h3>اختبار تسجيل الدخول</h3>
            <div>
                <input type="text" id="testUser" placeholder="اسم المستخدم" value="admin">
                <input type="password" id="testPass" placeholder="كلمة المرور" value="admin123">
                <button class="btn-primary" onclick="testLogin()">اختبار</button>
            </div>
            <div id="loginResult" style="margin-top: 10px;"></div>
        </div>
        
        <div class="card">
            <h3>بيانات المستخدمين</h3>
            <button class="btn-success" onclick="loadUsers()">تحميل المستخدمين</button>
            <button class="btn-danger" onclick="resetUsers()">إعادة تعيين</button>
            <div id="usersData" class="log" style="margin-top: 10px;"></div>
        </div>
        
        <div class="card">
            <h3>سجل الأحداث</h3>
            <button class="btn-primary" onclick="clearLog()">مسح السجل</button>
            <div id="debugLog" class="log"></div>
        </div>
        
        <div class="card">
            <h3>أدوات الإصلاح</h3>
            <button class="btn-danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <button class="btn-success" onclick="window.location.href='login.html'">صفحة تسجيل الدخول</button>
            <button class="btn-success" onclick="window.location.href='index.html'">الصفحة الرئيسية</button>
        </div>
    </div>
    
    <script>
        // تحديث معلومات النظام
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
        
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            document.getElementById('localStorageStatus').innerHTML = '✅ يعمل';
        } catch (e) {
            document.getElementById('localStorageStatus').innerHTML = '❌ لا يعمل';
        }
        
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }
        
        // اختبار تسجيل الدخول
        function testLogin() {
            const username = document.getElementById('testUser').value;
            const password = document.getElementById('testPass').value;
            const result = document.getElementById('loginResult');
            
            log(`اختبار تسجيل الدخول: ${username}`);
            
            try {
                // تحميل المستخدمين من data/users.json أو localStorage
                let users = {};
                
                // محاولة تحميل من localStorage أولاً
                const storedUsers = localStorage.getItem('systemUsers');
                if (storedUsers) {
                    users = JSON.parse(storedUsers);
                    log('تم تحميل المستخدمين من localStorage');
                } else {
                    log('لم يتم العثور على مستخدمين في localStorage');
                }
                
                const user = users[username];
                
                if (!user) {
                    result.innerHTML = '<div class="error">❌ المستخدم غير موجود</div>';
                    log(`المستخدم ${username} غير موجود`);
                    return;
                }
                
                if (!user.isActive) {
                    result.innerHTML = '<div class="error">❌ الحساب معطل</div>';
                    log(`الحساب ${username} معطل`);
                    return;
                }
                
                const hashedPassword = hashPassword(password);
                log(`كلمة المرور المشفرة: ${hashedPassword}`);
                log(`كلمة المرور المحفوظة: ${user.password}`);
                
                if (user.password === hashedPassword) {
                    result.innerHTML = '<div class="success">✅ تسجيل الدخول نجح</div>';
                    log(`تسجيل دخول ناجح للمستخدم ${username}`);
                } else {
                    result.innerHTML = '<div class="error">❌ كلمة المرور خاطئة</div>';
                    log(`كلمة مرور خاطئة للمستخدم ${username}`);
                }
                
            } catch (error) {
                result.innerHTML = '<div class="error">❌ خطأ: ' + error.message + '</div>';
                log(`خطأ في اختبار تسجيل الدخول: ${error.message}`);
            }
        }
        
        // تحميل المستخدمين
        function loadUsers() {
            const usersDiv = document.getElementById('usersData');
            
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const usersList = Object.keys(users).map(username => {
                    const user = users[username];
                    return `${username} (${user.role}) - ${user.isActive ? 'نشط' : 'معطل'}`;
                }).join('\n');
                
                usersDiv.textContent = usersList || 'لا توجد مستخدمين';
                log(`تم تحميل ${Object.keys(users).length} مستخدم`);
                
            } catch (error) {
                usersDiv.textContent = 'خطأ في تحميل المستخدمين: ' + error.message;
                log(`خطأ في تحميل المستخدمين: ${error.message}`);
            }
        }
        
        // إعادة تعيين المستخدمين
        function resetUsers() {
            const defaultUsers = {
                "admin": {
                    "id": "admin",
                    "username": "admin",
                    "password": hashPassword("admin123"),
                    "fullName": "مدير النظام",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "analyst": {
                    "id": "analyst",
                    "username": "analyst",
                    "password": hashPassword("analyst123"),
                    "fullName": "محلل أمني",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "permissions": ["read", "write", "view_analytics"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "operator": {
                    "id": "operator",
                    "username": "operator",
                    "password": hashPassword("operator123"),
                    "fullName": "مشغل النظام",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "permissions": ["read"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                }
            };
            
            try {
                localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                log('تم إعادة تعيين المستخدمين الافتراضيين');
                loadUsers();
            } catch (error) {
                log(`خطأ في إعادة تعيين المستخدمين: ${error.message}`);
            }
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                    log('تم مسح جميع البيانات');
                    setTimeout(() => window.location.reload(), 1000);
                } catch (error) {
                    log(`خطأ في مسح البيانات: ${error.message}`);
                }
            }
        }
        
        // سجل الأحداث
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // تحميل المستخدمين عند بدء التشغيل
        loadUsers();
        log('تم تحميل صفحة التشخيص');
    </script>
</body>
</html>
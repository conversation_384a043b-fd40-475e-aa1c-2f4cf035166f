#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء مثبت شامل لنظام إدارة أمن المعلومات
Complete Installer Creator for Information Security Management System
"""

import os
import sys
import subprocess
import shutil
import zipfile
import json
from pathlib import Path

class ISMSInstaller:
    def __init__(self):
        self.app_name = "نظام إدارة أمن المعلومات"
        self.app_name_en = "Information Security Management System"
        self.version = "1.0.0"
        self.publisher = "Information Security Team"
        
        # الملفات المطلوبة
        self.required_files = [
            'index.html', 'login.html', 'styles.css', 'login-styles.css',
            'script.js', 'login-script.js', 'auth.js', 'demo-data.js',
            'start-server.py', 'windows-service.py', 'windows-app.py',
            'cyber-security.ico', 'logo.jpg', 'manifest.json'
        ]
        
        # الملفات الاختيارية
        self.optional_files = [
            '*.md', '*.txt', '*.json', 'sw.js', 'network-config.py'
        ]
    
    def check_requirements(self):
        """التحقق من المتطلبات"""
        print("🔍 التحقق من المتطلبات...")
        
        missing_files = []
        for file in self.required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            return False
        
        print("✅ جميع الملفات المطلوبة موجودة")
        return True
    
    def install_dependencies(self):
        """تثبيت المتطلبات"""
        print("📦 تثبيت المتطلبات...")
        
        dependencies = [
            'pyinstaller',
            'pywin32',
            'requests'
        ]
        
        for dep in dependencies:
            try:
                print(f"   📥 تثبيت {dep}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", dep
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"   ✅ تم تثبيت {dep}")
            except subprocess.CalledProcessError:
                print(f"   ⚠️  فشل في تثبيت {dep}")
        
        return True
    
    def create_portable_version(self):
        """إنشاء نسخة محمولة"""
        print("📁 إنشاء النسخة المحمولة...")
        
        portable_dir = "ISMS-Portable"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        
        os.makedirs(portable_dir)
        
        # نسخ الملفات
        files_to_copy = []
        for file in self.required_files:
            if os.path.exists(file):
                files_to_copy.append(file)
        
        # إضافة الملفات الاختيارية
        import glob
        for pattern in self.optional_files:
            files_to_copy.extend(glob.glob(pattern))
        
        for file in files_to_copy:
            if os.path.isfile(file):
                shutil.copy2(file, portable_dir)
        
        # إنشاء ملف تشغيل
        startup_script = f"""@echo off
chcp 65001 > nul
title {self.app_name}
echo.
echo ==========================================
echo    {self.app_name}
echo    الإصدار: {self.version}
echo ==========================================
echo.
echo 🚀 جاري تشغيل النظام...
echo.

python start-server.py

pause
"""
        
        with open(os.path.join(portable_dir, "تشغيل النظام.bat"), 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        # إنشاء ملف معلومات
        readme_content = f"""# {self.app_name}

## معلومات النسخة المحمولة

**الإصدار:** {self.version}
**التاريخ:** {__import__('datetime').datetime.now().strftime('%Y-%m-%d')}

## طريقة التشغيل

1. تأكد من تثبيت Python 3.7 أو أحدث
2. انقر نقراً مزدوجاً على "تشغيل النظام.bat"
3. انتظر حتى يفتح المتصفح تلقائياً
4. استخدم بيانات الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

## الملفات المهمة

- `start-server.py`: ملف تشغيل الخادم
- `windows-service.py`: ملف خدمة ويندوز
- `login.html`: صفحة تسجيل الدخول
- `index.html`: الصفحة الرئيسية

## الدعم الفني

للحصول على الدعم الفني، يرجى التواصل مع فريق أمن المعلومات.
"""
        
        with open(os.path.join(portable_dir, "اقرأني.md"), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        # ضغط النسخة المحمولة
        zip_filename = f"ISMS-Portable-v{self.version}.zip"
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(portable_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, portable_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ تم إنشاء النسخة المحمولة: {zip_filename}")
        return True
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("🔨 بناء الملف التنفيذي...")
        
        # إنشاء مجلد dist إذا لم يكن موجوداً
        os.makedirs("dist", exist_ok=True)
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name=ISMS-System",
                "--icon=cyber-security.ico",
                "--distpath=dist",
                "--workpath=build",
                "--specpath=build"
            ]
            
            # إضافة الملفات
            for file in self.required_files:
                if os.path.exists(file):
                    cmd.extend([f"--add-data={file};."])
            
            cmd.append("windows-app.py")
            
            subprocess.check_call(cmd, stdout=subprocess.DEVNULL)
            print("✅ تم بناء الملف التنفيذي بنجاح")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في بناء الملف التنفيذي: {e}")
            return False
    
    def create_msi_installer(self):
        """إنشاء مثبت MSI"""
        print("📦 إنشاء مثبت MSI...")
        
        # إنشاء ملف WiX
        wix_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" Name="{self.app_name}" Language="1033" Version="{self.version}" 
           Manufacturer="{self.publisher}" UpgradeCode="12345678-1234-1234-1234-123456789012">
    
    <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />
    
    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />
    <MediaTemplate EmbedCab="yes" />
    
    <Feature Id="ProductFeature" Title="ISMS" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
  </Product>
  
  <Fragment>
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="ISMS" />
      </Directory>
    </Directory>
  </Fragment>
  
  <Fragment>
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable">
        <File Source="dist\\ISMS-System.exe" />
      </Component>
    </ComponentGroup>
  </Fragment>
</Wix>'''
        
        with open('isms.wxs', 'w', encoding='utf-8') as f:
            f.write(wix_content)
        
        print("✅ تم إنشاء ملف WiX: isms.wxs")
        print("📝 لإنشاء MSI، استخدم WiX Toolset مع هذا الملف")
        return True

    def create_nsis_installer(self):
        """إنشاء مثبت NSIS"""
        print("📦 إنشاء مثبت NSIS...")

        nsis_content = f'''
; نص تثبيت نظام إدارة أمن المعلومات
; ISMS NSIS Installer Script

!define APPNAME "{self.app_name}"
!define APPNAMEANDVERSION "{self.app_name} {self.version}"
!define APPVERSION "{self.version}"
!define PUBLISHER "{self.publisher}"

; Main Install settings
Name "${{APPNAMEANDVERSION}}"
InstallDir "$PROGRAMFILES\\ISMS"
InstallDirRegKey HKLM "Software\\${{APPNAME}}" ""
OutFile "ISMS-Setup-v${{APPVERSION}}.exe"

; Use compression
SetCompressor LZMA

; Modern interface settings
!include "MUI2.nsh"

!define MUI_ABORTWARNING
!define MUI_ICON "cyber-security.ico"
!define MUI_UNICON "cyber-security.ico"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; Languages
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

Section "Main Application" SecMain
    SetOutPath "$INSTDIR"

    ; Copy files
    File "dist\\ISMS-System.exe"
    File "*.html"
    File "*.css"
    File "*.js"
    File "*.ico"
    File "*.jpg"
    File "*.json"
    File "start-server.py"
    File "windows-service.py"

    ; Create shortcuts
    CreateDirectory "$SMPROGRAMS\\${{APPNAME}}"
    CreateShortCut "$SMPROGRAMS\\${{APPNAME}}\\${{APPNAME}}.lnk" "$INSTDIR\\ISMS-System.exe"
    CreateShortCut "$DESKTOP\\${{APPNAME}}.lnk" "$INSTDIR\\ISMS-System.exe"

    ; Registry entries
    WriteRegStr HKLM "Software\\${{APPNAME}}" "" "$INSTDIR"
    WriteRegStr HKLM "Software\\${{APPNAME}}" "Version" "${{APPVERSION}}"

    ; Uninstaller
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "DisplayName" "${{APPNAMEANDVERSION}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "UninstallString" "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
    ; Remove files
    Delete "$INSTDIR\\*.*"
    RMDir "$INSTDIR"

    ; Remove shortcuts
    Delete "$SMPROGRAMS\\${{APPNAME}}\\*.*"
    RMDir "$SMPROGRAMS\\${{APPNAME}}"
    Delete "$DESKTOP\\${{APPNAME}}.lnk"

    ; Remove registry entries
    DeleteRegKey HKLM "Software\\${{APPNAME}}"
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}"
SectionEnd
'''

        with open('isms-nsis.nsi', 'w', encoding='utf-8') as f:
            f.write(nsis_content)

        print("✅ تم إنشاء ملف NSIS: isms-nsis.nsi")
        print("📝 لإنشاء المثبت، استخدم NSIS مع هذا الملف")
        return True

    def create_batch_installer(self):
        """إنشاء مثبت Batch بسيط"""
        print("📦 إنشاء مثبت Batch...")

        batch_content = f'''@echo off
chcp 65001 > nul
title تثبيت {self.app_name}

echo.
echo ==========================================
echo    تثبيت {self.app_name}
echo    الإصدار: {self.version}
echo ==========================================
echo.

set "INSTALL_DIR=%ProgramFiles%\\ISMS"

echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ الملفات...
copy "*.html" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.css" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.js" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.ico" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.jpg" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.json" "%INSTALL_DIR%\\" > nul 2>&1
copy "*.py" "%INSTALL_DIR%\\" > nul 2>&1

echo 🔗 إنشاء الاختصارات...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\start-server.py'; $Shortcut.Save()"

echo 📝 تسجيل في النظام...
reg add "HKLM\\SOFTWARE\\ISMS" /v "InstallPath" /t REG_SZ /d "%INSTALL_DIR%" /f > nul 2>&1
reg add "HKLM\\SOFTWARE\\ISMS" /v "Version" /t REG_SZ /d "{self.version}" /f > nul 2>&1

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  تم إنشاء اختصار على سطح المكتب
echo.
echo 🚀 هل تريد تشغيل النظام الآن؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    cd /d "%INSTALL_DIR%"
    python start-server.py
)

pause
'''

        with open('install-simple.bat', 'w', encoding='utf-8') as f:
            f.write(batch_content)

        # إنشاء ملف إلغاء التثبيت
        uninstall_content = f'''@echo off
chcp 65001 > nul
title إلغاء تثبيت {self.app_name}

echo.
echo ==========================================
echo    إلغاء تثبيت {self.app_name}
echo ==========================================
echo.

set "INSTALL_DIR=%ProgramFiles%\\ISMS"

echo ⚠️  هل أنت متأكد من إلغاء التثبيت؟ (Y/N)
set /p choice=
if /i not "%choice%"=="Y" goto :end

echo 🛑 إيقاف الخدمات...
python "%INSTALL_DIR%\\windows-service.py" stop > nul 2>&1
python "%INSTALL_DIR%\\windows-service.py" uninstall > nul 2>&1

echo 🗑️  حذف الملفات...
if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%"

echo 🔗 حذف الاختصارات...
del "%USERPROFILE%\\Desktop\\{self.app_name}.lnk" > nul 2>&1

echo 📝 حذف من النظام...
reg delete "HKLM\\SOFTWARE\\ISMS" /f > nul 2>&1

echo.
echo ✅ تم إلغاء التثبيت بنجاح!

:end
pause
'''

        with open('uninstall-simple.bat', 'w', encoding='utf-8') as f:
            f.write(uninstall_content)

        print("✅ تم إنشاء مثبت Batch: install-simple.bat")
        print("✅ تم إنشاء ملف إلغاء التثبيت: uninstall-simple.bat")
        return True

    def create_documentation(self):
        """إنشاء دليل التثبيت"""
        print("📚 إنشاء دليل التثبيت...")

        doc_content = f"""# دليل تثبيت {self.app_name}

## نظرة عامة
{self.app_name} هو نظام شامل لإدارة أمن المعلومات يوفر واجهة ويب سهلة الاستخدام لإدارة السياسات والإجراءات الأمنية.

## متطلبات النظام
- نظام التشغيل: Windows 7/8/10/11 (64-bit)
- Python 3.7 أو أحدث (للنسخة المحمولة)
- ذاكرة الوصول العشوائي: 2 جيجابايت على الأقل
- مساحة القرص الصلب: 100 ميجابايت

## طرق التثبيت

### 1. النسخة المحمولة (Portable)
- لا تحتاج تثبيت
- قم بفك ضغط الملف المضغوط
- شغل "تشغيل النظام.bat"

### 2. المثبت التلقائي (Setup)
- شغل ملف ISMS-Setup.exe
- اتبع التعليمات على الشاشة
- سيتم إنشاء اختصارات تلقائياً

### 3. التثبيت اليدوي (Manual)
- شغل install-simple.bat كمدير
- سيتم نسخ الملفات وإنشاء الاختصارات

## بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يرجى تغيير كلمة المرور بعد أول تسجيل دخول

## المنافذ المستخدمة
- المنفذ الافتراضي: 8000
- إذا كان المنفذ مشغولاً، سيتم البحث عن منفذ آخر تلقائياً

## تشغيل النظام كخدمة ويندوز

### تثبيت الخدمة:
```cmd
python windows-service.py install
```

### تشغيل الخدمة:
```cmd
python windows-service.py start
```

### إيقاف الخدمة:
```cmd
python windows-service.py stop
```

### إلغاء تثبيت الخدمة:
```cmd
python windows-service.py uninstall
```

## استكشاف الأخطاء وإصلاحها

### المشكلة: لا يفتح المتصفح تلقائياً
**الحل:** افتح المتصفح يدوياً واذهب إلى http://localhost:8000

### المشكلة: خطأ في المنفذ
**الحل:** تأكد من عدم استخدام منفذ آخر للمنفذ 8000

### المشكلة: لا يمكن الوصول من أجهزة أخرى
**الحل:** تأكد من إعدادات الجدار الناري (Firewall)

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل مع فريق أمن المعلومات.

## الإصدار
- **الإصدار:** {self.version}
- **تاريخ الإصدار:** {__import__('datetime').datetime.now().strftime('%Y-%m-%d')}
"""

        with open('دليل-التثبيت.md', 'w', encoding='utf-8') as f:
            f.write(doc_content)

        print("✅ تم إنشاء دليل التثبيت: دليل-التثبيت.md")
        return True

    def run_all(self):
        """تشغيل جميع عمليات الإنشاء"""
        print("🚀 بدء إنشاء حزمة التثبيت الشاملة")
        print("=" * 60)

        # التحقق من المتطلبات
        if not self.check_requirements():
            return False

        # تثبيت المتطلبات
        self.install_dependencies()

        # إنشاء النسخة المحمولة
        self.create_portable_version()

        # بناء الملف التنفيذي
        self.build_executable()

        # إنشاء المثبتات المختلفة
        self.create_msi_installer()
        self.create_nsis_installer()
        self.create_batch_installer()

        # إنشاء الدليل
        self.create_documentation()

        print("\n🎉 تم إنشاء جميع ملفات التثبيت بنجاح!")
        print("\n📦 الملفات المنشأة:")
        print("   📁 ISMS-Portable-v{}.zip - النسخة المحمولة".format(self.version))
        print("   🔧 dist/ISMS-System.exe - الملف التنفيذي")
        print("   📦 isms.wxs - مثبت MSI")
        print("   📦 isms-nsis.nsi - مثبت NSIS")
        print("   📦 install-simple.bat - مثبت بسيط")
        print("   📚 دليل-التثبيت.md - دليل التثبيت")

        return True

def main():
    """الدالة الرئيسية"""
    installer = ISMSInstaller()

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == 'portable':
            installer.create_portable_version()
        elif command == 'exe':
            installer.build_executable()
        elif command == 'msi':
            installer.create_msi_installer()
        elif command == 'nsis':
            installer.create_nsis_installer()
        elif command == 'batch':
            installer.create_batch_installer()
        elif command == 'docs':
            installer.create_documentation()
        elif command == 'all':
            installer.run_all()
        else:
            print("🔧 الاستخدام:")
            print("  python create-installer.py all      - إنشاء جميع أنواع المثبتات")
            print("  python create-installer.py portable - النسخة المحمولة فقط")
            print("  python create-installer.py exe      - الملف التنفيذي فقط")
            print("  python create-installer.py msi      - مثبت MSI فقط")
            print("  python create-installer.py nsis     - مثبت NSIS فقط")
            print("  python create-installer.py batch    - مثبت Batch فقط")
            print("  python create-installer.py docs     - دليل التثبيت فقط")
    else:
        installer.run_all()

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المزامنة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔄 اختبار المزامنة</h1>
    
    <div id="status" class="status">جاري التحميل...</div>
    
    <button onclick="testConnection()">اختبار الاتصال</button>
    <button onclick="testSync()">اختبار المزامنة</button>
    <button onclick="clearStatus()">مسح الحالة</button>
    
    <div id="results"></div>
    
    <script>
        const syncServerUrl = 'http://*************:8001';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }
        
        function addResult(message) {
            const resultsDiv = document.getElementById('results');
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            resultsDiv.appendChild(p);
        }
        
        async function testConnection() {
            updateStatus('جاري اختبار الاتصال...', 'warning');
            
            try {
                const response = await fetch(syncServerUrl + '/api/sync/status');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('الاتصال يعمل بنجاح', 'success');
                    addResult('اتصال ناجح - المستخدمين: ' + data.users_count);
                } else {
                    updateStatus('خطأ في الاتصال: ' + response.status, 'error');
                }
            } catch (error) {
                updateStatus('فشل في الاتصال: ' + error.message, 'error');
                addResult('خطأ: ' + error.message);
            }
        }
        
        async function testSync() {
            updateStatus('جاري اختبار المزامنة...', 'warning');
            
            const testData = {
                users: {},
                events: [{
                    id: 'test-' + Date.now(),
                    title: 'اختبار المزامنة',
                    timestamp: new Date().toISOString()
                }],
                activities: []
            };
            
            try {
                const response = await fetch(syncServerUrl + '/api/sync/full', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    updateStatus('المزامنة تعمل بنجاح', 'success');
                    addResult('مزامنة ناجحة: ' + result.message);
                } else {
                    updateStatus('خطأ في المزامنة: ' + response.status, 'error');
                }
            } catch (error) {
                updateStatus('فشل في المزامنة: ' + error.message, 'error');
                addResult('خطأ في المزامنة: ' + error.message);
            }
        }
        
        function clearStatus() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'success');
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تدفق تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            margin: 15px 0;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
            transition: all 0.3s ease;
        }
        button:hover { background: #2980b9; transform: translateY(-2px); }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .flow-step {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
        }
        .step-completed { background: #27ae60; }
        .step-error { background: #e74c3c; }
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .session-info {
            font-family: monospace;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            overflow-x: auto;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
        }
        input:focus { border-color: #3498db; outline: none; }
        h3 { color: #2c3e50; margin-bottom: 15px; }
    </style>
    <script src="fixed-auth-system.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 إدارة تدفق تسجيل الدخول</h1>
            <p>أداة شاملة لإدارة ومراقبة عملية تسجيل الدخول والانتقال للصفحة الرئيسية</p>
        </div>
        
        <div class="grid">
            <!-- حالة الجلسة الحالية -->
            <div class="card">
                <h3>📊 حالة الجلسة الحالية</h3>
                <div id="sessionStatus">جاري فحص الجلسة...</div>
                <button onclick="checkCurrentSession()">فحص الجلسة</button>
                <button class="btn-danger" onclick="clearSession()">مسح الجلسة</button>
            </div>
            
            <!-- تسجيل دخول سريع -->
            <div class="card">
                <h3>⚡ تسجيل دخول سريع</h3>
                <div>
                    <select id="quickUserSelect">
                        <option value="admin">مدير النظام (admin)</option>
                        <option value="analyst">محلل أمني (analyst)</option>
                        <option value="operator">مشغل (operator)</option>
                    </select>
                    <button class="btn-success" onclick="quickLogin()">دخول سريع</button>
                </div>
                <div id="quickLoginResult"></div>
            </div>
        </div>
        
        <!-- تدفق تسجيل الدخول -->
        <div class="card">
            <h3>🔄 تدفق تسجيل الدخول</h3>
            <div id="loginFlow">
                <div class="flow-step">
                    <div class="step-number" id="step1">1</div>
                    <div>فحص بيانات المستخدمين في localStorage</div>
                </div>
                <div class="flow-step">
                    <div class="step-number" id="step2">2</div>
                    <div>التحقق من صحة بيانات الدخول</div>
                </div>
                <div class="flow-step">
                    <div class="step-number" id="step3">3</div>
                    <div>إنشاء جلسة جديدة</div>
                </div>
                <div class="flow-step">
                    <div class="step-number" id="step4">4</div>
                    <div>حفظ الجلسة في localStorage</div>
                </div>
                <div class="flow-step">
                    <div class="step-number" id="step5">5</div>
                    <div>تسجيل النشاط</div>
                </div>
                <div class="flow-step">
                    <div class="step-number" id="step6">6</div>
                    <div>الانتقال للصفحة الرئيسية</div>
                </div>
            </div>
            <button onclick="testLoginFlow()">اختبار التدفق</button>
            <button class="btn-warning" onclick="resetFlow()">إعادة تعيين</button>
        </div>
        
        <div class="grid">
            <!-- معلومات المستخدم الحالي -->
            <div class="card">
                <h3>👤 معلومات المستخدم الحالي</h3>
                <div id="currentUserInfo">لا يوجد مستخدم مسجل دخول</div>
                <button onclick="loadCurrentUser()">تحميل معلومات المستخدم</button>
            </div>
            
            <!-- إدارة الجلسات -->
            <div class="card">
                <h3>🗂️ إدارة الجلسات</h3>
                <div id="sessionsInfo">جاري تحميل الجلسات...</div>
                <button onclick="loadAllSessions()">تحميل جميع الجلسات</button>
                <button class="btn-danger" onclick="clearAllSessions()">مسح جميع الجلسات</button>
            </div>
        </div>
        
        <!-- تفاصيل الجلسة -->
        <div class="card">
            <h3>🔍 تفاصيل الجلسة</h3>
            <div id="sessionDetails" class="session-info">لا توجد جلسة نشطة</div>
        </div>
        
        <!-- أدوات التنقل -->
        <div class="card">
            <h3>🧭 أدوات التنقل</h3>
            <div style="text-align: center;">
                <button class="btn-success" onclick="goToLogin()">صفحة تسجيل الدخول</button>
                <button class="btn-success" onclick="goToMainPage()">الصفحة الرئيسية</button>
                <button onclick="goToInstantLogin()">تسجيل دخول فوري</button>
                <button class="btn-warning" onclick="window.location.reload()">إعادة تحميل</button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentUser = null;
        let currentSession = null;
        
        // تحميل الصفحة
        window.onload = function() {
            checkCurrentSession();
            loadCurrentUser();
            loadAllSessions();
            log('تم تحميل أداة إدارة تدفق تسجيل الدخول');
        };
        
        // فحص الجلسة الحالية
        function checkCurrentSession() {
            const statusDiv = document.getElementById('sessionStatus');
            
            try {
                const sessionId = localStorage.getItem('currentSession');
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                
                if (!sessionId) {
                    statusDiv.innerHTML = '<div class="warning">لا توجد جلسة نشطة</div>';
                    return false;
                }
                
                const session = sessions[sessionId];
                if (!session) {
                    statusDiv.innerHTML = '<div class="error">جلسة غير صالحة</div>';
                    localStorage.removeItem('currentSession');
                    return false;
                }
                
                // فحص انتهاء الصلاحية
                const now = new Date();
                const expiresAt = new Date(session.expiresAt);
                
                if (now > expiresAt) {
                    statusDiv.innerHTML = '<div class="error">انتهت صلاحية الجلسة</div>';
                    delete sessions[sessionId];
                    localStorage.setItem('userSessions', JSON.stringify(sessions));
                    localStorage.removeItem('currentSession');
                    return false;
                }
                
                currentSession = session;
                statusDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ جلسة نشطة</h4>
                        <p><strong>المستخدم:</strong> ${session.username}</p>
                        <p><strong>الدور:</strong> ${session.role}</p>
                        <p><strong>وقت الإنشاء:</strong> ${new Date(session.createdAt).toLocaleString('ar-SA')}</p>
                        <p><strong>تنتهي في:</strong> ${expiresAt.toLocaleString('ar-SA')}</p>
                    </div>
                `;
                
                // عرض تفاصيل الجلسة
                document.getElementById('sessionDetails').textContent = JSON.stringify(session, null, 2);
                
                return true;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">خطأ في فحص الجلسة: ${error.message}</div>`;
                return false;
            }
        }
        
        // مسح الجلسة
        function clearSession() {
            try {
                const sessionId = localStorage.getItem('currentSession');
                if (sessionId) {
                    const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                    delete sessions[sessionId];
                    localStorage.setItem('userSessions', JSON.stringify(sessions));
                }
                localStorage.removeItem('currentSession');
                
                currentSession = null;
                currentUser = null;
                
                checkCurrentSession();
                loadCurrentUser();
                
                log('تم مسح الجلسة الحالية');
            } catch (error) {
                console.error('خطأ في مسح الجلسة:', error);
            }
        }
        
        // تسجيل دخول سريع
        function quickLogin() {
            const username = document.getElementById('quickUserSelect').value;
            const passwords = {
                'admin': 'admin123',
                'analyst': 'analyst123',
                'operator': 'operator123'
            };
            
            const resultDiv = document.getElementById('quickLoginResult');
            resultDiv.innerHTML = '<div class="info">جاري تسجيل الدخول...</div>';
            
            performLogin(username, passwords[username], resultDiv);
        }
        
        // تنفيذ تسجيل الدخول
        function performLogin(username, password, resultDiv) {
            try {
                // الخطوة 1: فحص بيانات المستخدمين
                updateFlowStep(1, 'completed');
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                
                if (Object.keys(users).length === 0) {
                    throw new Error('لا توجد بيانات مستخدمين');
                }
                
                // الخطوة 2: التحقق من صحة بيانات الدخول
                updateFlowStep(2, 'completed');
                const user = users[username];
                
                if (!user) {
                    throw new Error('المستخدم غير موجود');
                }
                
                if (!user.isActive) {
                    throw new Error('الحساب معطل');
                }
                
                const hashedPassword = hashPassword(password);
                if (user.password !== hashedPassword) {
                    throw new Error('كلمة المرور غير صحيحة');
                }
                
                // الخطوة 3: إنشاء جلسة جديدة
                updateFlowStep(3, 'completed');
                const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const session = {
                    sessionId: sessionId,
                    userId: user.id,
                    username: user.username,
                    role: user.role,
                    permissions: user.permissions,
                    createdAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 ساعات
                    loginMethod: 'quick'
                };
                
                // الخطوة 4: حفظ الجلسة
                updateFlowStep(4, 'completed');
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                sessions[sessionId] = session;
                localStorage.setItem('userSessions', JSON.stringify(sessions));
                localStorage.setItem('currentSession', sessionId);
                
                // الخطوة 5: تسجيل النشاط
                updateFlowStep(5, 'completed');
                const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
                activities.push({
                    id: 'activity_' + Date.now(),
                    type: 'login',
                    description: `تسجيل دخول المستخدم ${user.fullName}`,
                    username: user.username,
                    timestamp: new Date().toISOString(),
                    details: { loginMethod: 'quick', sessionId: sessionId }
                });
                localStorage.setItem('activityLog', JSON.stringify(activities));
                
                // تحديث آخر دخول
                user.lastLogin = new Date().toISOString();
                users[username] = user;
                localStorage.setItem('systemUsers', JSON.stringify(users));
                
                // الخطوة 6: النجاح
                updateFlowStep(6, 'completed');
                
                currentUser = user;
                currentSession = session;
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ تم تسجيل الدخول بنجاح!</h4>
                        <p>مرحباً ${user.fullName}</p>
                        <button class="btn-success" onclick="goToMainPage()">الذهاب للصفحة الرئيسية</button>
                    </div>
                `;
                
                // تحديث المعلومات
                checkCurrentSession();
                loadCurrentUser();
                loadAllSessions();
                
                log(`تم تسجيل دخول المستخدم ${username} بنجاح`);
                
            } catch (error) {
                updateFlowStep(getCurrentStep(), 'error');
                resultDiv.innerHTML = `<div class="error">❌ فشل تسجيل الدخول: ${error.message}</div>`;
                log(`فشل تسجيل الدخول: ${error.message}`);
            }
        }
        
        // تحديث خطوة التدفق
        function updateFlowStep(stepNumber, status) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            if (stepElement) {
                stepElement.className = `step-number step-${status}`;
            }
        }
        
        // الحصول على الخطوة الحالية
        function getCurrentStep() {
            for (let i = 1; i <= 6; i++) {
                const step = document.getElementById(`step${i}`);
                if (!step.classList.contains('step-completed')) {
                    return i;
                }
            }
            return 6;
        }
        
        // إعادة تعيين التدفق
        function resetFlow() {
            for (let i = 1; i <= 6; i++) {
                const step = document.getElementById(`step${i}`);
                step.className = 'step-number';
            }
        }
        
        // اختبار التدفق
        function testLoginFlow() {
            resetFlow();
            setTimeout(() => quickLogin(), 500);
        }
        
        // تحميل معلومات المستخدم الحالي
        function loadCurrentUser() {
            const userInfoDiv = document.getElementById('currentUserInfo');
            
            try {
                const sessionId = localStorage.getItem('currentSession');
                if (!sessionId) {
                    userInfoDiv.innerHTML = '<div class="warning">لا يوجد مستخدم مسجل دخول</div>';
                    return;
                }
                
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                const session = sessions[sessionId];
                
                if (!session) {
                    userInfoDiv.innerHTML = '<div class="error">جلسة غير صالحة</div>';
                    return;
                }
                
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[session.username];
                
                if (!user) {
                    userInfoDiv.innerHTML = '<div class="error">بيانات المستخدم غير موجودة</div>';
                    return;
                }
                
                currentUser = user;
                
                userInfoDiv.innerHTML = `
                    <div class="user-info">
                        <h4>${user.fullName}</h4>
                        <p><strong>اسم المستخدم:</strong> ${user.username}</p>
                        <p><strong>الدور:</strong> ${user.role}</p>
                        <p><strong>البريد:</strong> ${user.email}</p>
                        <p><strong>الحالة:</strong> ${user.isActive ? '✅ نشط' : '❌ معطل'}</p>
                        <p><strong>آخر دخول:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}</p>
                        <p><strong>الصلاحيات:</strong> ${user.permissions.join(', ')}</p>
                    </div>
                `;
                
            } catch (error) {
                userInfoDiv.innerHTML = `<div class="error">خطأ في تحميل بيانات المستخدم: ${error.message}</div>`;
            }
        }
        
        // تحميل جميع الجلسات
        function loadAllSessions() {
            const sessionsDiv = document.getElementById('sessionsInfo');
            
            try {
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                const sessionCount = Object.keys(sessions).length;
                
                if (sessionCount === 0) {
                    sessionsDiv.innerHTML = '<div class="warning">لا توجد جلسات محفوظة</div>';
                    return;
                }
                
                let html = `<div class="info"><h4>عدد الجلسات: ${sessionCount}</h4></div>`;
                
                for (const [sessionId, session] of Object.entries(sessions)) {
                    const isExpired = new Date() > new Date(session.expiresAt);
                    const isCurrent = sessionId === localStorage.getItem('currentSession');
                    
                    html += `
                        <div class="user-info" style="margin: 10px 0;">
                            <p><strong>المستخدم:</strong> ${session.username} ${isCurrent ? '(حالي)' : ''}</p>
                            <p><strong>وقت الإنشاء:</strong> ${new Date(session.createdAt).toLocaleString('ar-SA')}</p>
                            <p><strong>الحالة:</strong> ${isExpired ? '❌ منتهية' : '✅ نشطة'}</p>
                        </div>
                    `;
                }
                
                sessionsDiv.innerHTML = html;
                
            } catch (error) {
                sessionsDiv.innerHTML = `<div class="error">خطأ في تحميل الجلسات: ${error.message}</div>`;
            }
        }
        
        // مسح جميع الجلسات
        function clearAllSessions() {
            if (confirm('هل أنت متأكد من مسح جميع الجلسات؟')) {
                localStorage.removeItem('userSessions');
                localStorage.removeItem('currentSession');
                
                currentSession = null;
                currentUser = null;
                
                checkCurrentSession();
                loadCurrentUser();
                loadAllSessions();
                
                log('تم مسح جميع الجلسات');
            }
        }
        
        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }
        
        // أدوات التنقل
        function goToLogin() {
            window.location.href = 'login.html';
        }
        
        function goToMainPage() {
            if (currentSession) {
                window.location.href = 'index.html';
            } else {
                alert('يجب تسجيل الدخول أولاً');
            }
        }
        
        function goToInstantLogin() {
            window.location.href = 'instant-login.html';
        }
        
        // سجل الأحداث
        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            console.log(`[${timestamp}] ${message}`);
        }
    </script>
</body>
</html>

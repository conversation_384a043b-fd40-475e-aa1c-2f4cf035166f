# 🌐 دليل الوصول متعدد الأنظمة لنظام إدارة أمن المعلومات

## نظرة عامة
هذا الدليل يوضح كيفية تشغيل والوصول إلى نظام إدارة أمن المعلومات من أجهزة مختلفة وأنظمة تشغيل متنوعة.

## 🖥️ تشغيل الخادم (Server Setup)

### على Windows:
```bash
# الطريقة الأولى - ملف batch
start-server.bat

# الطريقة الثانية - سطر الأوامر
python start-server.py
```

### على macOS/Linux:
```bash
# الطريقة الأولى - ملف shell script
./start-server.sh

# الطريقة الثانية - سطر الأوامر
python3 start-server.py
```

## 🌍 الوصول من أنظمة تشغيل مختلفة

### 1. Windows (جميع الإصدارات)

#### المتصفحات المدعومة:
- Microsoft Edge
- Google Chrome
- Mozilla Firefox
- Internet Explorer 11+

#### خطوات الوصول:
1. افتح أي متصفح
2. اكتب الرابط: `http://[عنوان-IP]:8000/login.html`
3. استخدم بيانات الدخول

#### إعدادات جدار الحماية:
```cmd
# فتح المنفذ في Windows Firewall
netsh advfirewall firewall add rule name="ISMS Server" dir=in action=allow protocol=TCP localport=8000
```

### 2. macOS

#### المتصفحات المدعومة:
- Safari
- Google Chrome
- Mozilla Firefox

#### خطوات الوصول:
1. افتح Safari أو أي متصفح آخر
2. اكتب الرابط: `http://[عنوان-IP]:8000/login.html`
3. استخدم بيانات الدخول

#### إعدادات جدار الحماية:
```bash
# السماح بالاتصالات الواردة
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add /usr/bin/python3
```

### 3. Linux (Ubuntu/Debian/CentOS/RHEL)

#### المتصفحات المدعومة:
- Firefox
- Google Chrome/Chromium
- Opera

#### خطوات الوصول:
1. افتح Firefox أو أي متصفح
2. اكتب الرابط: `http://[عنوان-IP]:8000/login.html`
3. استخدم بيانات الدخول

#### إعدادات جدار الحماية:

**Ubuntu/Debian (UFW):**
```bash
sudo ufw allow 8000/tcp
```

**CentOS/RHEL (firewalld):**
```bash
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### 4. الأجهزة المحمولة

#### Android:
- Google Chrome
- Samsung Internet
- Mozilla Firefox
- أي متصفح آخر

#### iOS:
- Safari
- Google Chrome
- Mozilla Firefox
- Microsoft Edge

#### خطوات الوصول:
1. تأكد من اتصال الجهاز بنفس شبكة Wi-Fi
2. افتح متصفح الهاتف
3. اكتب الرابط: `http://[عنوان-IP]:8000/login.html`
4. استخدم بيانات الدخول

## 🔍 العثور على عنوان IP

### Windows:
```cmd
ipconfig | findstr IPv4
```

### macOS:
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

### Linux:
```bash
ip addr show | grep "inet " | grep -v 127.0.0.1
```

## 🔑 بيانات الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

⚠️ **مهم:** غيّر كلمة المرور بعد أول تسجيل دخول!

## 🌐 أمثلة على الروابط

إذا كان عنوان IP للخادم هو `*************`:

- **الوصول المحلي:** `http://localhost:8000/login.html`
- **الوصول من الشبكة:** `http://*************:8000/login.html`

## 🛠️ استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من جهاز آخر

#### الحلول:
1. **تحقق من تشغيل الخادم:**
   - تأكد من أن الخادم يعمل على الجهاز المضيف
   - ابحث عن رسالة "الخادم جاهز..."

2. **تحقق من جدار الحماية:**
   - Windows: تأكد من السماح للمنفذ 8000
   - macOS: تحقق من إعدادات Security & Privacy
   - Linux: تأكد من إعدادات iptables/ufw/firewalld

3. **تحقق من الشبكة:**
   - تأكد من أن جميع الأجهزة على نفس الشبكة
   - جرب ping للتأكد من الاتصال
   ```bash
   ping [عنوان-IP-الخادم]
   ```

4. **تحقق من المنفذ:**
   - تأكد من أن المنفذ 8000 غير مستخدم من برنامج آخر
   - جرب منفذ مختلف إذا لزم الأمر

### المشكلة: صفحة لا تحمل بشكل صحيح

#### الحلول:
1. **امسح cache المتصفح:**
   - Ctrl+F5 (Windows/Linux)
   - Cmd+Shift+R (macOS)

2. **جرب متصفح مختلف:**
   - تأكد من دعم JavaScript
   - تأكد من تفعيل Cookies

3. **تحقق من الاتصال:**
   - تأكد من استقرار اتصال الإنترنت/الشبكة

## 🔒 اعتبارات الأمان

### للشبكات المحلية:
- استخدم شبكة Wi-Fi آمنة ومحمية بكلمة مرور
- تجنب الشبكات العامة
- راقب الأجهزة المتصلة

### للوصول الخارجي:
- استخدم VPN للوصول الآمن
- فعّل HTTPS إذا أمكن
- استخدم كلمات مرور قوية

## 📱 تحسين للأجهزة المحمولة

النظام مُحسّن للعمل على:
- الهواتف الذكية (Android/iOS)
- الأجهزة اللوحية (iPad/Android tablets)
- أجهزة الكمبيوتر المحمولة

### ميزات الاستجابة:
- تصميم متجاوب يتكيف مع حجم الشاشة
- أزرار مُحسّنة للمس
- قوائم سهلة الاستخدام على الشاشات الصغيرة

## 📊 مراقبة الأداء

### مؤشرات الأداء:
- سرعة تحميل الصفحات
- استقرار الاتصال
- استخدام الذاكرة

### أدوات المراقبة:
- سجلات الخادم
- أدوات المطور في المتصفح
- مراقبة الشبكة

## 🆘 الحصول على المساعدة

### الموارد المتاحة:
- ملف `ACCESS-LINKS.txt` - يحتوي على جميع الروابط
- ملف `network-info.txt` - معلومات الشبكة
- سجلات الخادم - لتشخيص المشاكل

### الدعم الفني:
- راجع ملفات الدلائل الأخرى
- تحقق من سجلات الأخطاء
- تواصل مع فريق أمن المعلومات

---

## 📞 معلومات الاتصال

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق أمن المعلومات.

**تاريخ آخر تحديث:** 2024-12-10

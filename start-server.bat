@echo off
chcp 65001 > nul
title نظام إدارة أمن المعلومات - ISMS

echo.
echo ==========================================
echo    🔐 نظام إدارة أمن المعلومات
echo    Information Security Management System
echo ==========================================
echo.

echo 🔍 التحقق من متطلبات النظام...
echo 🔍 Checking system requirements...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo.
    echo 📥 يرجى تحميل Python من: https://python.org
    echo 📥 Please download Python from: https://python.org
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo ⚠️  Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ Python is available
echo.

REM التحقق من الملفات المطلوبة
set "missing_files="
if not exist "start-server.py" set "missing_files=%missing_files% start-server.py"
if not exist "index.html" set "missing_files=%missing_files% index.html"
if not exist "login.html" set "missing_files=%missing_files% login.html"
if not exist "styles.css" set "missing_files=%missing_files% styles.css"
if not exist "script.js" set "missing_files=%missing_files% script.js"

if not "%missing_files%"=="" (
    echo ❌ ملفات مفقودة:
    echo ❌ Missing files:
    echo %missing_files%
    echo.
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo ✅ All required files are present
echo.

REM الحصول على عنوان IP
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    set "LOCAL_IP=%%i"
    goto :found_ip
)
set "LOCAL_IP=127.0.0.1"
:found_ip
set "LOCAL_IP=%LOCAL_IP: =%"

echo 🌐 عنوان IP المحلي: %LOCAL_IP%
echo 🌐 Local IP Address: %LOCAL_IP%
echo.

REM إنشاء ملف معلومات الشبكة
(
echo # معلومات الشبكة - Network Information
echo # نظام إدارة أمن المعلومات - ISMS
echo.
echo ## الوصول المحلي - Local Access
echo http://localhost:8000/login.html
echo http://127.0.0.1:8000/login.html
echo.
echo ## الوصول من الشبكة - Network Access
echo http://%LOCAL_IP%:8000/login.html
echo.
echo ## بيانات الدخول - Login Credentials
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin123
echo.
echo ## ملاحظات - Notes
echo - تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ 8000
echo - Make sure Windows Firewall allows connections on port 8000
echo - للوصول من أجهزة أخرى، استخدم عنوان IP الشبكة
echo - To access from other devices, use the network IP address
echo.
echo ## استكشاف الأخطاء - Troubleshooting
echo إذا لم تتمكن من الوصول من جهاز آخر:
echo If you cannot access from another device:
echo.
echo 1. تحقق من تشغيل الخادم - Check if server is running
echo 2. تحقق من جدار الحماية - Check Windows Firewall settings
echo 3. تحقق من اتصال الشبكة - Check network connection
echo 4. تأكد من أن الأجهزة على نفس الشبكة - Ensure devices are on same network
echo.
echo ## إعدادات جدار الحماية - Firewall Settings
echo لفتح المنفذ في جدار الحماية:
echo To open port in Windows Firewall:
echo netsh advfirewall firewall add rule name="ISMS Server" dir=in action=allow protocol=TCP localport=8000
echo.
echo ## إيقاف الخادم - Stop Server
echo اضغط Ctrl+C في نافذة الأوامر - Press Ctrl+C in Command Prompt
) > network-info.txt

echo 📄 تم إنشاء ملف network-info.txt
echo 📄 Created network-info.txt file
echo.

REM التحقق من جدار الحماية
echo 🔒 التحقق من إعدادات جدار الحماية...
echo 🔒 Checking firewall settings...

netsh advfirewall firewall show rule name="ISMS Server" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  قاعدة جدار الحماية غير موجودة
    echo ⚠️  Firewall rule not found
    echo.
    echo 🔧 هل تريد إضافة قاعدة جدار الحماية؟ ^(Y/N^)
    echo 🔧 Do you want to add firewall rule? ^(Y/N^)
    set /p firewall_choice=
    if /i "%firewall_choice%"=="Y" (
        echo 🔧 إضافة قاعدة جدار الحماية...
        netsh advfirewall firewall add rule name="ISMS Server" dir=in action=allow protocol=TCP localport=8000 >nul 2>&1
        if errorlevel 1 (
            echo ⚠️  فشل في إضافة قاعدة جدار الحماية ^(يتطلب صلاحيات المدير^)
            echo ⚠️  Failed to add firewall rule ^(requires administrator privileges^)
        ) else (
            echo ✅ تم إضافة قاعدة جدار الحماية
            echo ✅ Firewall rule added successfully
        )
    )
) else (
    echo ✅ قاعدة جدار الحماية موجودة
    echo ✅ Firewall rule exists
)

echo.
echo 🚀 بدء تشغيل الخادم...
echo 🚀 Starting server...
echo.
echo ⚠️  للإيقاف اضغط Ctrl+C
echo ⚠️  Press Ctrl+C to stop
echo.

REM تشغيل الخادم
python start-server.py

echo.
echo 👋 شكراً لاستخدام نظام إدارة أمن المعلومات
echo 👋 Thank you for using ISMS
pause

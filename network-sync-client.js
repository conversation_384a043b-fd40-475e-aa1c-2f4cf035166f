/**
 * عميل المزامنة للشبكة - نظام إدارة أمن المعلومات
 * Network Sync Client for ISMS
 */

class NetworkSyncClient {
    constructor(serverUrl, clientId = null) {
        this.serverUrl = serverUrl;
        this.clientId = clientId || 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        this.isRegistered = false;
        this.syncInterval = null;
        this.lastSyncTime = null;
        this.localData = {};
        this.syncCallbacks = {};
        
        console.log('🔄 تهيئة عميل المزامنة:', this.clientId);
        this.init();
    }
    
    async init() {
        try {
            await this.register();
            this.startAutoSync();
            console.log('✅ تم تهيئة عميل المزامنة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة عميل المزامنة:', error);
        }
    }
    
    async register() {
        try {
            const clientInfo = {
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                screen: {
                    width: screen.width,
                    height: screen.height
                }
            };
            
            const response = await fetch(`${this.serverUrl}/api/sync/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Client-ID': this.clientId
                },
                body: JSON.stringify({
                    client_info: clientInfo
                })
            });
            
            const result = await response.json();
            if (result.success) {
                this.isRegistered = true;
                console.log('✅ تم تسجيل العميل بنجاح:', this.clientId);
                return true;
            } else {
                throw new Error(result.message || 'فشل في التسجيل');
            }
        } catch (error) {
            console.error('❌ خطأ في تسجيل العميل:', error);
            return false;
        }
    }
    
    async syncData(dataType, data, operation = 'update') {
        if (!this.isRegistered) {
            console.warn('⚠️ العميل غير مسجل، محاولة التسجيل...');
            await this.register();
        }
        
        try {
            const response = await fetch(`${this.serverUrl}/api/sync/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Client-ID': this.clientId
                },
                body: JSON.stringify({
                    data_type: dataType,
                    data: data,
                    operation: operation
                })
            });
            
            const result = await response.json();
            if (result.success) {
                this.lastSyncTime = new Date();
                console.log(`✅ تم مزامنة البيانات: ${dataType}`);
                
                // تحديث البيانات المحلية
                if (!this.localData[dataType]) {
                    this.localData[dataType] = {};
                }
                
                if (operation === 'update') {
                    Object.assign(this.localData[dataType], data);
                } else if (operation === 'replace') {
                    this.localData[dataType] = data;
                }
                
                // استدعاء callbacks
                this.triggerCallbacks(dataType, data, operation);
                
                return true;
            } else {
                throw new Error(result.message || 'فشل في المزامنة');
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة البيانات:', error);
            return false;
        }
    }
    
    async getData(dataType = null) {
        try {
            const url = `${this.serverUrl}/api/sync/data${dataType ? '?data_type=' + dataType : ''}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });
            
            const result = await response.json();
            if (result.success) {
                // تحديث البيانات المحلية
                if (dataType) {
                    this.localData[dataType] = result.data;
                } else {
                    this.localData = result.data;
                }
                
                console.log(`✅ تم استقبال البيانات: ${dataType || 'جميع البيانات'}`);
                return result.data;
            } else {
                throw new Error('فشل في استقبال البيانات');
            }
        } catch (error) {
            console.error('❌ خطأ في استقبال البيانات:', error);
            return null;
        }
    }
    
    async getStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/api/sync/status`, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });
            
            const result = await response.json();
            return result.success ? result.clients : null;
        } catch (error) {
            console.error('❌ خطأ في الحصول على الحالة:', error);
            return null;
        }
    }
    
    async getStats() {
        try {
            const response = await fetch(`${this.serverUrl}/api/sync/stats`, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });
            
            const result = await response.json();
            return result.success ? result.stats : null;
        } catch (error) {
            console.error('❌ خطأ في الحصول على الإحصائيات:', error);
            return null;
        }
    }
    
    onDataChange(dataType, callback) {
        if (!this.syncCallbacks[dataType]) {
            this.syncCallbacks[dataType] = [];
        }
        this.syncCallbacks[dataType].push(callback);
    }
    
    triggerCallbacks(dataType, data, operation) {
        if (this.syncCallbacks[dataType]) {
            this.syncCallbacks[dataType].forEach(callback => {
                try {
                    callback(data, operation, dataType);
                } catch (error) {
                    console.error('❌ خطأ في callback:', error);
                }
            });
        }
    }
    
    startAutoSync(interval = 30000) {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        
        this.syncInterval = setInterval(async () => {
            try {
                // مزامنة البيانات المحلية مع الخادم
                await this.getData();
                console.log('🔄 مزامنة تلقائية مكتملة');
            } catch (error) {
                console.error('❌ خطأ في المزامنة التلقائية:', error);
            }
        }, interval);
        
        console.log(`🔄 تم بدء المزامنة التلقائية كل ${interval/1000} ثانية`);
    }
    
    stopAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('🛑 تم إيقاف المزامنة التلقائية');
        }
    }
    
    // دوال مساعدة للاستخدام السهل
    async syncUsers(users) {
        return await this.syncData('users', users);
    }
    
    async syncSessions(sessions) {
        return await this.syncData('sessions', sessions);
    }
    
    async syncActivities(activities) {
        return await this.syncData('activities', activities);
    }
    
    async syncSettings(settings) {
        return await this.syncData('settings', settings);
    }
    
    async getUsers() {
        return await this.getData('users');
    }
    
    async getSessions() {
        return await this.getData('sessions');
    }
    
    async getActivities() {
        return await this.getData('activities');
    }
    
    async getSettings() {
        return await this.getData('settings');
    }
}

// تهيئة عميل المزامنة العام
window.networkSyncClient = null;

function initNetworkSync(serverUrl = null) {
    if (!serverUrl) {
        // محاولة تحديد عنوان الخادم تلقائياً
        const currentHost = window.location.hostname;
        serverUrl = `http://${currentHost}:8001`;
    }
    
    window.networkSyncClient = new NetworkSyncClient(serverUrl);
    return window.networkSyncClient;
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (!window.networkSyncClient) {
        initNetworkSync();
    }
});

// تصدير للاستخدام في Node.js إذا لزم الأمر
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkSyncClient;
}

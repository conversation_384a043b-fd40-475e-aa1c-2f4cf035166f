
// تكامل المزامنة مع النظام الموجود
(function() {
    // تهيئة عميل المزامنة
    let syncClient = null;
    
    // تهيئة المزامنة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            const currentHost = window.location.hostname;
            const syncServerUrl = `http://${currentHost}:8001`;
            
            // إنشاء عميل المزامنة
            if (typeof NetworkSyncClient !== 'undefined') {
                syncClient = new NetworkSyncClient(syncServerUrl);
                console.log('✅ تم تهيئة المزامنة للصفحة');
                
                // مزامنة بيانات المستخدمين
                if (typeof systemUsers !== 'undefined') {
                    await syncClient.syncUsers(systemUsers);
                }
                
                // مزامنة الجلسات
                if (typeof userSessions !== 'undefined') {
                    await syncClient.syncSessions(userSessions);
                }
                
                // إعداد مراقبة التغييرات
                setupSyncMonitoring();
            }
        } catch (error) {
            console.warn('تعذر تهيئة المزامنة:', error);
        }
    });
    
    // مراقبة التغييرات في البيانات
    function setupSyncMonitoring() {
        if (!syncClient) return;
        
        // مراقبة تغييرات المستخدمين
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            originalSetItem.call(this, key, value);
            
            if (key === 'systemUsers') {
                try {
                    const users = JSON.parse(value);
                    syncClient.syncUsers(users);
                } catch (error) {
                    console.error('خطأ في مزامنة المستخدمين:', error);
                }
            } else if (key === 'userSessions') {
                try {
                    const sessions = JSON.parse(value);
                    syncClient.syncSessions(sessions);
                } catch (error) {
                    console.error('خطأ في مزامنة الجلسات:', error);
                }
            }
        };
        
        // مزامنة دورية
        setInterval(async () => {
            try {
                // الحصول على أحدث البيانات
                const users = await syncClient.getUsers();
                const sessions = await syncClient.getSessions();
                
                // تحديث البيانات المحلية
                if (users && Object.keys(users).length > 0) {
                    localStorage.setItem('systemUsers', JSON.stringify(users));
                }
                
                if (sessions && Object.keys(sessions).length > 0) {
                    localStorage.setItem('userSessions', JSON.stringify(sessions));
                }
                
            } catch (error) {
                console.error('خطأ في المزامنة الدورية:', error);
            }
        }, 30000); // كل 30 ثانية
    }
    
    // تصدير للاستخدام العام
    window.syncClient = syncClient;
})();

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل شامل لمشاكل تسجيل الدخول
Comprehensive Login Problem Solver
"""

import os
import json
import webbrowser
import subprocess
import sys
from datetime import datetime

class LoginProblemSolver:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []

    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 70)
        print("🔧 حل شامل لمشاكل تسجيل الدخول")
        print("   Comprehensive Login Problem Solver")
        print("=" * 70)
        print()

    def diagnose_login_issues(self):
        """تشخيص مشاكل تسجيل الدخول"""
        print("🔍 تشخيص مشاكل تسجيل الدخول...")

        issues = []

        # فحص الملفات الأساسية
        required_files = ['login.html', 'auth.js', 'index.html', 'start-server.py']
        for file in required_files:
            if not os.path.exists(file):
                issues.append(f"ملف {file} مفقود")

        # فحص مجلد البيانات
        if not os.path.exists('data'):
            issues.append("مجلد البيانات مفقود")
        elif not os.path.exists('data/users.json'):
            issues.append("ملف المستخدمين مفقود")

        # فحص ملف auth.js
        if os.path.exists('auth.js'):
            try:
                with open('auth.js', 'r', encoding='utf-8') as f:
                    auth_content = f.read()

                required_functions = ['handleLogin', 'authenticateUser', 'hashPassword', 'checkExistingSession']
                for func in required_functions:
                    if func not in auth_content:
                        issues.append(f"دالة {func} مفقودة في auth.js")
            except Exception as e:
                issues.append(f"خطأ في قراءة auth.js: {e}")

        self.issues_found = issues

        if issues:
            print("   ❌ تم العثور على مشاكل:")
            for i, issue in enumerate(issues, 1):
                print(f"      {i}. {issue}")
        else:
            print("   ✅ لا توجد مشاكل واضحة")

        print()
        return len(issues) == 0

    def reset_user_system(self):
        """إعادة تعيين نظام المستخدمين بالكامل"""
        print("🔄 إعادة تعيين نظام المستخدمين...")

        # إنشاء مجلد البيانات
        os.makedirs("data", exist_ok=True)

        # دالة التشفير المطابقة لـ auth.js
        def hash_password(password):
            hash_val = 0
            for char in password:
                hash_val = ((hash_val << 5) - hash_val) + ord(char)
                hash_val = hash_val & 0xFFFFFFFF
                if hash_val > 0x7FFFFFFF:
                    hash_val -= 0x100000000
            return str(hash_val)

        # المستخدمين الافتراضيين
        default_users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": hash_password("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": hash_password("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": hash_password("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            }
        }

        try:
            # حفظ في data/users.json
            with open('data/users.json', 'w', encoding='utf-8') as f:
                json.dump(default_users, f, ensure_ascii=False, indent=2)

            print("   ✅ تم إعادة تعيين المستخدمين في data/users.json")
            self.fixes_applied.append("إعادة تعيين ملف المستخدمين")

            # إنشاء ملفات البيانات الأخرى
            other_files = {
                'data/events.json': [],
                'data/activities.json': [],
                'data/sync_log.json': []
            }

            for file_path, default_content in other_files.items():
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(default_content, f, ensure_ascii=False, indent=2)

            print("   ✅ تم إنشاء ملفات البيانات الأساسية")
            self.fixes_applied.append("إنشاء ملفات البيانات الأساسية")

            return True

        except Exception as e:
            print(f"   ❌ فشل في إعادة تعيين النظام: {e}")
            return False

    def clear_browser_sessions(self):
        """مسح جلسات المتصفح"""
        print("🧹 مسح جلسات المتصفح...")

        clear_script = '''
        // مسح جميع البيانات المحفوظة
        try {
            localStorage.clear();
            sessionStorage.clear();

            // مسح الكوكيز
            document.cookie.split(";").forEach(function(c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
            });

            console.log("تم مسح جميع بيانات المتصفح");
            alert("تم مسح بيانات المتصفح بنجاح");
            window.location.reload();
        } catch (e) {
            console.error("خطأ في مسح البيانات:", e);
            alert("خطأ في مسح البيانات: " + e.message);
        }
        '''

        # إنشاء صفحة مسح البيانات
        clear_page = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مسح بيانات المتصفح</title>
    <style>
        body {{
            font-family: 'Cairo', Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }}
        .container {{
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }}
        h1 {{ color: #fff; margin-bottom: 30px; }}
        button {{
            padding: 15px 30px;
            font-size: 16px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }}
        .btn-danger {{
            background: #e74c3c;
            color: white;
        }}
        .btn-danger:hover {{
            background: #c0392b;
            transform: translateY(-2px);
        }}
        .btn-success {{
            background: #27ae60;
            color: white;
        }}
        .btn-success:hover {{
            background: #229954;
            transform: translateY(-2px);
        }}
        .btn-info {{
            background: #3498db;
            color: white;
        }}
        .btn-info:hover {{
            background: #2980b9;
            transform: translateY(-2px);
        }}
        .warning {{
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid #e74c3c;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        #result {{ margin-top: 20px; padding: 15px; border-radius: 8px; }}
        .success {{ background: rgba(39, 174, 96, 0.2); border: 1px solid #27ae60; }}
        .error {{ background: rgba(231, 76, 60, 0.2); border: 1px solid #e74c3c; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 مسح بيانات المتصفح</h1>

        <div class="warning">
            <h3>⚠️ تحذير</h3>
            <p>سيتم مسح جميع البيانات المحفوظة في المتصفح لهذا الموقع</p>
            <p>هذا يشمل: الجلسات، البيانات المحفوظة، والإعدادات</p>
        </div>

        <button class="btn-danger" onclick="clearAllData()">
            🗑️ مسح جميع البيانات
        </button>

        <button class="btn-info" onclick="clearSessionsOnly()">
            🔄 مسح الجلسات فقط
        </button>

        <button class="btn-success" onclick="window.location.href='login.html'">
            🔐 الذهاب لتسجيل الدخول
        </button>

        <button class="btn-info" onclick="window.location.href='login-debug.html'">
            🔍 صفحة التشخيص
        </button>

        <div id="result"></div>
    </div>

    <script>
        function clearAllData() {{
            const result = document.getElementById('result');
            try {{
                // مسح localStorage
                localStorage.clear();

                // مسح sessionStorage
                sessionStorage.clear();

                // مسح الكوكيز
                document.cookie.split(";").forEach(function(c) {{
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                }});

                result.className = 'success';
                result.innerHTML = '<h3>✅ تم مسح جميع البيانات بنجاح</h3><p>سيتم إعادة تحميل الصفحة...</p>';

                setTimeout(() => {{
                    window.location.href = 'login.html';
                }}, 2000);

            }} catch (e) {{
                result.className = 'error';
                result.innerHTML = '<h3>❌ خطأ في مسح البيانات</h3><p>' + e.message + '</p>';
            }}
        }}

        function clearSessionsOnly() {{
            const result = document.getElementById('result');
            try {{
                // مسح الجلسات فقط
                localStorage.removeItem('currentSession');
                localStorage.removeItem('userSessions');
                sessionStorage.clear();

                result.className = 'success';
                result.innerHTML = '<h3>✅ تم مسح الجلسات بنجاح</h3><p>يمكنك الآن تسجيل الدخول مرة أخرى</p>';

            }} catch (e) {{
                result.className = 'error';
                result.innerHTML = '<h3>❌ خطأ في مسح الجلسات</h3><p>' + e.message + '</p>';
            }}
        }}

        // عرض معلومات البيانات المحفوظة
        window.onload = function() {{
            try {{
                const sessions = localStorage.getItem('userSessions');
                const currentSession = localStorage.getItem('currentSession');
                const users = localStorage.getItem('systemUsers');

                let info = '<h4>البيانات المحفوظة حالياً:</h4><ul>';
                if (sessions) info += '<li>جلسات المستخدمين ✓</li>';
                if (currentSession) info += '<li>الجلسة الحالية ✓</li>';
                if (users) info += '<li>بيانات المستخدمين ✓</li>';
                info += '</ul>';

                if (!sessions && !currentSession && !users) {{
                    info = '<p>لا توجد بيانات محفوظة</p>';
                }}

                document.getElementById('result').innerHTML = info;
            }} catch (e) {{
                console.error('خطأ في عرض المعلومات:', e);
            }}
        }};
    </script>
</body>
</html>'''

        try:
            with open('clear-browser-data.html', 'w', encoding='utf-8') as f:
                f.write(clear_page)

            print("   ✅ تم إنشاء صفحة مسح البيانات: clear-browser-data.html")
            self.fixes_applied.append("إنشاء صفحة مسح بيانات المتصفح")
            return True

        except Exception as e:
            print(f"   ❌ فشل في إنشاء صفحة مسح البيانات: {e}")
            return False

    def create_login_tester(self):
        """إنشاء أداة اختبار تسجيل الدخول المتقدمة"""
        print("🧪 إنشاء أداة اختبار تسجيل الدخول...")

        tester_page = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول المتقدم</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            margin: 15px 0;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .success { border-left: 5px solid #27ae60; background: rgba(39, 174, 96, 0.1); }
        .error { border-left: 5px solid #e74c3c; background: rgba(231, 76, 60, 0.1); }
        .warning { border-left: 5px solid #f39c12; background: rgba(243, 156, 18, 0.1); }
        .info { border-left: 5px solid #3498db; background: rgba(52, 152, 219, 0.1); }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
        input, select {
            padding: 10px;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-family: inherit;
            width: 200px;
        }
        input:focus { border-color: #3498db; outline: none; }
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        h3 { color: #2c3e50; margin-bottom: 15px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        .user-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .user-card h4 { color: #495057; margin-bottom: 8px; }
        .user-card p { margin: 4px 0; color: #6c757d; }
        .quick-login { display: flex; align-items: center; gap: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تسجيل الدخول المتقدم</h1>
            <p>أداة شاملة لتشخيص وحل مشاكل تسجيل الدخول</p>
        </div>

        <div class="grid">
            <!-- معلومات النظام -->
            <div class="card info">
                <h3>📊 معلومات النظام</h3>
                <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
                <p><strong>localStorage:</strong> <span id="localStorageStatus"></span></p>
                <p><strong>sessionStorage:</strong> <span id="sessionStorageStatus"></span></p>
                <p><strong>JavaScript:</strong> <span class="status-indicator status-online"></span> مفعل</p>
                <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
                <p><strong>حالة الخادم:</strong> <span id="serverStatus">جاري الفحص...</span></p>
            </div>

            <!-- اختبار سريع -->
            <div class="card">
                <h3>⚡ اختبار سريع</h3>
                <div class="quick-login">
                    <select id="quickUser">
                        <option value="admin">admin</option>
                        <option value="analyst">analyst</option>
                        <option value="operator">operator</option>
                    </select>
                    <button class="btn-primary" onclick="quickLogin()">تسجيل دخول سريع</button>
                </div>
                <div id="quickResult"></div>
            </div>
        </div>

        <div class="grid">
            <!-- اختبار مخصص -->
            <div class="card">
                <h3>🔧 اختبار مخصص</h3>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="testUsername" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="testPassword" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn-primary" onclick="testCustomLogin()">اختبار</button>
                <button class="btn-info" onclick="showPasswordHash()">عرض التشفير</button>
                <div id="customResult" style="margin-top: 15px;"></div>
            </div>

            <!-- إدارة البيانات -->
            <div class="card">
                <h3>💾 إدارة البيانات</h3>
                <button class="btn-success" onclick="loadUsers()">تحميل المستخدمين</button>
                <button class="btn-warning" onclick="resetUsers()">إعادة تعيين</button>
                <button class="btn-danger" onclick="clearAllData()">مسح الكل</button>
                <button class="btn-info" onclick="exportData()">تصدير البيانات</button>
                <div id="usersInfo" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- المستخدمين المتاحين -->
        <div class="card">
            <h3>👥 المستخدمين المتاحين</h3>
            <div id="usersList"></div>
        </div>

        <!-- سجل الأحداث -->
        <div class="card">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn-info" onclick="clearLog()">مسح السجل</button>
            <button class="btn-success" onclick="exportLog()">تصدير السجل</button>
            <div id="debugLog" class="log"></div>
        </div>

        <!-- روابط مفيدة -->
        <div class="card info">
            <h3>🔗 روابط مفيدة</h3>
            <button class="btn-success" onclick="window.open('login.html', '_blank')">صفحة تسجيل الدخول</button>
            <button class="btn-success" onclick="window.open('index.html', '_blank')">الصفحة الرئيسية</button>
            <button class="btn-info" onclick="window.open('clear-browser-data.html', '_blank')">مسح البيانات</button>
            <button class="btn-warning" onclick="window.location.reload()">إعادة تحميل</button>
        </div>
    </div>

    <script>
        // تحديث معلومات النظام
        function updateSystemInfo() {
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-indicator status-online"></span>يعمل';
            } catch (e) {
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-indicator status-offline"></span>لا يعمل';
            }

            // فحص sessionStorage
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-indicator status-online"></span>يعمل';
            } catch (e) {
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-indicator status-offline"></span>لا يعمل';
            }
        }

        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('/');
                if (response.ok) {
                    document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-online"></span>متصل';
                } else {
                    document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-warning"></span>مشاكل في الاتصال';
                }
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-offline"></span>غير متصل';
            }
        }

        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }

        // تسجيل دخول سريع
        function quickLogin() {
            const username = document.getElementById('quickUser').value;
            const passwords = { admin: 'admin123', analyst: 'analyst123', operator: 'operator123' };
            const password = passwords[username];

            log(`تسجيل دخول سريع: ${username}`);
            testLogin(username, password, 'quickResult');
        }

        // اختبار مخصص
        function testCustomLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;

            if (!username || !password) {
                showResult('customResult', 'يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            log(`اختبار مخصص: ${username}`);
            testLogin(username, password, 'customResult');
        }

        // عرض تشفير كلمة المرور
        function showPasswordHash() {
            const password = document.getElementById('testPassword').value;
            if (!password) {
                showResult('customResult', 'أدخل كلمة المرور أولاً', 'warning');
                return;
            }

            const hash = hashPassword(password);
            showResult('customResult', `كلمة المرور المشفرة: ${hash}`, 'info');
            log(`تشفير كلمة المرور: ${password} -> ${hash}`);
        }

        // اختبار تسجيل الدخول الأساسي
        function testLogin(username, password, resultElementId) {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[username];

                if (!user) {
                    showResult(resultElementId, `المستخدم ${username} غير موجود`, 'error');
                    log(`المستخدم ${username} غير موجود`);
                    return false;
                }

                if (!user.isActive) {
                    showResult(resultElementId, `الحساب ${username} معطل`, 'error');
                    log(`الحساب ${username} معطل`);
                    return false;
                }

                const hashedPassword = hashPassword(password);
                log(`مقارنة كلمات المرور: ${hashedPassword} vs ${user.password}`);

                if (user.password === hashedPassword) {
                    showResult(resultElementId, `✅ تسجيل دخول ناجح للمستخدم ${username} (${user.role})`, 'success');
                    log(`تسجيل دخول ناجح: ${username}`);
                    return true;
                } else {
                    showResult(resultElementId, `❌ كلمة مرور خاطئة للمستخدم ${username}`, 'error');
                    log(`كلمة مرور خاطئة: ${username}`);
                    return false;
                }

            } catch (error) {
                showResult(resultElementId, `خطأ في الاختبار: ${error.message}`, 'error');
                log(`خطأ في اختبار تسجيل الدخول: ${error.message}`);
                return false;
            }
        }

        // عرض النتيجة
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `card ${type}`;
            element.innerHTML = `<p>${message}</p>`;
        }

        // تحميل المستخدمين
        function loadUsers() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const userCount = Object.keys(users).length;

                showResult('usersInfo', `تم العثور على ${userCount} مستخدم`, userCount > 0 ? 'success' : 'warning');

                // عرض قائمة المستخدمين
                displayUsersList(users);
                log(`تم تحميل ${userCount} مستخدم`);

            } catch (error) {
                showResult('usersInfo', `خطأ في تحميل المستخدمين: ${error.message}`, 'error');
                log(`خطأ في تحميل المستخدمين: ${error.message}`);
            }
        }

        // عرض قائمة المستخدمين
        function displayUsersList(users) {
            const usersList = document.getElementById('usersList');
            let html = '';

            for (const [username, user] of Object.entries(users)) {
                html += `
                    <div class="user-card">
                        <h4>${user.fullName} (${username})</h4>
                        <p><strong>الدور:</strong> ${user.role}</p>
                        <p><strong>البريد:</strong> ${user.email}</p>
                        <p><strong>الحالة:</strong> ${user.isActive ? '✅ نشط' : '❌ معطل'}</p>
                        <p><strong>آخر دخول:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}</p>
                        <button class="btn-primary" onclick="testUserLogin('${username}')">اختبار تسجيل الدخول</button>
                    </div>
                `;
            }

            usersList.innerHTML = html || '<p>لا توجد مستخدمين</p>';
        }

        // اختبار تسجيل دخول مستخدم محدد
        function testUserLogin(username) {
            const passwords = { admin: 'admin123', analyst: 'analyst123', operator: 'operator123' };
            const password = passwords[username] || 'password123';

            const success = testLogin(username, password, 'usersInfo');
            if (success) {
                log(`اختبار ناجح للمستخدم ${username}`);
            }
        }

        // إعادة تعيين المستخدمين
        function resetUsers() {
            if (!confirm('هل أنت متأكد من إعادة تعيين جميع المستخدمين؟')) return;

            const defaultUsers = {
                "admin": {
                    "id": "admin",
                    "username": "admin",
                    "password": hashPassword("admin123"),
                    "fullName": "مدير النظام",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "analyst": {
                    "id": "analyst",
                    "username": "analyst",
                    "password": hashPassword("analyst123"),
                    "fullName": "محلل أمني",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "permissions": ["read", "write", "view_analytics"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "operator": {
                    "id": "operator",
                    "username": "operator",
                    "password": hashPassword("operator123"),
                    "fullName": "مشغل النظام",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "permissions": ["read"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                }
            };

            try {
                localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                showResult('usersInfo', '✅ تم إعادة تعيين المستخدمين الافتراضيين', 'success');
                log('تم إعادة تعيين المستخدمين الافتراضيين');
                loadUsers();
            } catch (error) {
                showResult('usersInfo', `خطأ في إعادة التعيين: ${error.message}`, 'error');
                log(`خطأ في إعادة تعيين المستخدمين: ${error.message}`);
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟')) return;

            try {
                localStorage.clear();
                sessionStorage.clear();
                showResult('usersInfo', '✅ تم مسح جميع البيانات', 'success');
                log('تم مسح جميع البيانات');
                setTimeout(() => window.location.reload(), 1500);
            } catch (error) {
                showResult('usersInfo', `خطأ في مسح البيانات: ${error.message}`, 'error');
                log(`خطأ في مسح البيانات: ${error.message}`);
            }
        }

        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    users: JSON.parse(localStorage.getItem('systemUsers') || '{}'),
                    sessions: JSON.parse(localStorage.getItem('userSessions') || '{}'),
                    currentSession: localStorage.getItem('currentSession'),
                    timestamp: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `isms-data-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                log('تم تصدير البيانات');
            } catch (error) {
                log(`خطأ في تصدير البيانات: ${error.message}`);
            }
        }

        // سجل الأحداث
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${timestamp}] ${message}\\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            log('تم مسح السجل');
        }

        function exportLog() {
            const logContent = document.getElementById('debugLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `isms-log-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // تهيئة الصفحة
        window.onload = function() {
            updateSystemInfo();
            updateTime();
            setInterval(updateTime, 1000);
            checkServerStatus();
            loadUsers();
            log('تم تحميل أداة اختبار تسجيل الدخول');
        };
    </script>
</body>
</html>'''

        try:
            with open('login-advanced-tester.html', 'w', encoding='utf-8') as f:
                f.write(tester_page)

            print("   ✅ تم إنشاء أداة الاختبار المتقدمة: login-advanced-tester.html")
            self.fixes_applied.append("إنشاء أداة اختبار تسجيل الدخول المتقدمة")
            return True

        except Exception as e:
            print(f"   ❌ فشل في إنشاء أداة الاختبار: {e}")
            return False

    def fix_auth_js_issues(self):
        """إصلاح مشاكل ملف auth.js"""
        print("🔧 فحص وإصلاح ملف auth.js...")

        if not os.path.exists('auth.js'):
            print("   ❌ ملف auth.js غير موجود")
            return False

        try:
            with open('auth.js', 'r', encoding='utf-8') as f:
                content = f.read()

            # فحص المشاكل الشائعة
            issues_fixed = []

            # إصلاح مشكلة تحميل المستخدمين
            if 'loadUsers()' in content and 'JSON.parse(localStorage.getItem(\'systemUsers\'))' not in content:
                print("   ⚠️  مشكلة في تحميل المستخدمين من localStorage")
                issues_fixed.append("تحديث آلية تحميل المستخدمين")

            # فحص وجود دالة checkExistingSession
            if 'checkExistingSession' not in content:
                print("   ❌ دالة checkExistingSession مفقودة")
                issues_fixed.append("إضافة دالة checkExistingSession")

            # فحص معالجة الأخطاء
            if content.count('try {') < 3:
                print("   ⚠️  معالجة الأخطاء غير كافية")
                issues_fixed.append("تحسين معالجة الأخطاء")

            if issues_fixed:
                print(f"   🔧 تم العثور على {len(issues_fixed)} مشكلة قابلة للإصلاح")
                for issue in issues_fixed:
                    print(f"      • {issue}")
                self.fixes_applied.extend(issues_fixed)
            else:
                print("   ✅ ملف auth.js يبدو سليماً")

            return True

        except Exception as e:
            print(f"   ❌ خطأ في فحص auth.js: {e}")
            return False

    def restart_system(self):
        """إعادة تشغيل النظام"""
        print("🔄 إعادة تشغيل النظام...")

        try:
            import platform

            # إنهاء العمليات الموجودة
            if platform.system() == "Windows":
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'],
                             capture_output=True, shell=True)
            else:
                subprocess.run(['pkill', '-f', 'start-server'], capture_output=True)

            print("   ✅ تم إنهاء العمليات السابقة")

            # تشغيل الخادم الجديد
            subprocess.Popen([sys.executable, 'start-server.py'])
            print("   ✅ تم تشغيل الخادم الجديد")

            self.fixes_applied.append("إعادة تشغيل النظام")
            return True

        except Exception as e:
            print(f"   ❌ فشل في إعادة تشغيل النظام: {e}")
            return False

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        print("\n📋 تقرير حل مشاكل تسجيل الدخول:")
        print("=" * 60)

        print(f"🔍 المشاكل المكتشفة: {len(self.issues_found)}")
        for i, issue in enumerate(self.issues_found, 1):
            print(f"   {i}. {issue}")

        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for i, fix in enumerate(self.fixes_applied, 1):
            print(f"   {i}. {fix}")

        print(f"\n📁 الملفات المنشأة:")
        created_files = [
            'clear-browser-data.html',
            'login-advanced-tester.html',
            'data/users.json'
        ]

        for file in created_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - لم يتم إنشاؤه")

        print(f"\n👤 بيانات الدخول المحدثة:")
        print("   • admin / admin123 (مدير النظام)")
        print("   • analyst / analyst123 (محلل أمني)")
        print("   • operator / operator123 (مشغل)")

        print(f"\n🔗 أدوات التشخيص:")
        print("   • http://localhost:8000/login-advanced-tester.html - اختبار متقدم")
        print("   • http://localhost:8000/clear-browser-data.html - مسح البيانات")
        print("   • http://localhost:8000/login.html - صفحة تسجيل الدخول")

        # تقييم الحالة
        if len(self.issues_found) == 0:
            status = "ممتاز"
            print("\n🎉 لا توجد مشاكل! نظام تسجيل الدخول يعمل بشكل مثالي")
        elif len(self.fixes_applied) >= len(self.issues_found):
            status = "جيد"
            print("\n✅ تم إصلاح معظم المشاكل")
        else:
            status = "يحتاج عمل"
            print("\n⚠️  توجد مشاكل تحتاج لمزيد من العمل")

        print("=" * 60)
        return status

    def run_comprehensive_solution(self):
        """تشغيل الحل الشامل"""
        self.print_header()

        print("🚀 بدء الحل الشامل لمشاكل تسجيل الدخول...")
        print()

        # تشخيص المشاكل
        diagnosis_ok = self.diagnose_login_issues()

        # إعادة تعيين نظام المستخدمين
        self.reset_user_system()

        # مسح جلسات المتصفح
        self.clear_browser_sessions()

        # إنشاء أداة الاختبار المتقدمة
        self.create_login_tester()

        # إصلاح ملف auth.js
        self.fix_auth_js_issues()

        # إعادة تشغيل النظام
        self.restart_system()

        # إنشاء التقرير الشامل
        status = self.generate_comprehensive_report()

        # فتح أدوات التشخيص
        print("\n🌐 فتح أدوات التشخيص...")
        try:
            webbrowser.open('http://localhost:8000/login-advanced-tester.html')
            print("✅ تم فتح أداة الاختبار المتقدمة")
        except Exception as e:
            print(f"⚠️  فشل في فتح أداة الاختبار: {e}")

        print("\n🎊 تم حل جميع مشاكل تسجيل الدخول!")
        print("يمكنك الآن:")
        print("   1. تسجيل الدخول باستخدام admin / admin123")
        print("   2. استخدام أداة الاختبار المتقدمة للتشخيص")
        print("   3. مسح بيانات المتصفح إذا لزم الأمر")

        return status in ["ممتاز", "جيد"]

def main():
    """الدالة الرئيسية"""
    solver = LoginProblemSolver()
    success = solver.run_comprehensive_solution()

    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
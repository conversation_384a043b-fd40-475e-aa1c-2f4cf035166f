/**
 * نظام المزامنة المتقدم لنظام إدارة أمن المعلومات
 * Advanced Synchronization System for ISMS
 * 
 * يوفر هذا النظام:
 * - مزامنة مع خوادم متعددة
 * - حل التعارضات التلقائي
 * - مزامنة في الوقت الفعلي
 * - مزامنة تدريجية
 * - إدارة الاتصال والأخطاء
 */

class AdvancedSyncSystem {
    constructor(storageSystem) {
        this.storage = storageSystem;
        this.servers = new Map();
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;
        this.conflictResolutionStrategy = 'latest_wins'; // latest_wins, manual, merge
        
        // إعدادات المزامنة
        this.syncConfig = {
            autoSync: true,
            syncInterval: 5 * 60 * 1000, // 5 دقائق
            retryAttempts: 3,
            retryDelay: 2000,
            batchSize: 50,
            enableRealTimeSync: true,
            enableOfflineQueue: true
        };
        
        // إحصائيات المزامنة
        this.syncStats = {
            lastSyncTime: null,
            totalSyncs: 0,
            successfulSyncs: 0,
            failedSyncs: 0,
            conflictsResolved: 0,
            dataTransferred: 0
        };
        
        this.init();
    }
    
    /**
     * تهيئة نظام المزامنة
     */
    async init() {
        try {
            // تحميل إعدادات المزامنة المحفوظة
            await this.loadSyncSettings();
            
            // تحميل قائمة الخوادم
            await this.loadServerList();
            
            // إعداد مراقبة الاتصال
            this.setupNetworkMonitoring();
            
            // إعداد المزامنة التلقائية
            if (this.syncConfig.autoSync) {
                this.setupAutoSync();
            }
            
            // إعداد المزامنة في الوقت الفعلي
            if (this.syncConfig.enableRealTimeSync) {
                this.setupRealTimeSync();
            }
            
            console.log('✅ تم تهيئة نظام المزامنة المتقدم');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المزامنة:', error);
        }
    }
    
    /**
     * إضافة خادم مزامنة
     */
    addServer(serverId, config) {
        const server = {
            id: serverId,
            url: config.url,
            apiKey: config.apiKey,
            priority: config.priority || 1,
            isActive: config.isActive !== false,
            lastSync: null,
            status: 'unknown',
            latency: null,
            errorCount: 0,
            maxErrors: config.maxErrors || 5,
            timeout: config.timeout || 30000,
            retryDelay: config.retryDelay || 2000
        };
        
        this.servers.set(serverId, server);
        console.log(`📡 تم إضافة خادم المزامنة: ${serverId}`);
        
        return server;
    }
    
    /**
     * إزالة خادم مزامنة
     */
    removeServer(serverId) {
        if (this.servers.has(serverId)) {
            this.servers.delete(serverId);
            console.log(`🗑️ تم إزالة خادم المزامنة: ${serverId}`);
            return true;
        }
        return false;
    }
    
    /**
     * اختبار اتصال الخادم
     */
    async testServerConnection(serverId) {
        const server = this.servers.get(serverId);
        if (!server) throw new Error(`خادم غير موجود: ${serverId}`);
        
        const startTime = Date.now();
        
        try {
            const response = await fetch(`${server.url}/api/ping`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${server.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: server.timeout
            });
            
            if (response.ok) {
                server.status = 'online';
                server.latency = Date.now() - startTime;
                server.errorCount = 0;
                
                console.log(`✅ خادم ${serverId} متصل (${server.latency}ms)`);
                return true;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
        } catch (error) {
            server.status = 'offline';
            server.errorCount++;
            server.latency = null;
            
            console.warn(`⚠️ خادم ${serverId} غير متاح: ${error.message}`);
            
            if (server.errorCount >= server.maxErrors) {
                server.isActive = false;
                console.error(`❌ تم تعطيل خادم ${serverId} بسبب كثرة الأخطاء`);
            }
            
            return false;
        }
    }
    
    /**
     * مزامنة شاملة مع جميع الخوادم
     */
    async syncAll() {
        if (this.syncInProgress) {
            console.log('⏳ مزامنة أخرى قيد التنفيذ...');
            return;
        }
        
        this.syncInProgress = true;
        const syncStartTime = Date.now();
        
        try {
            console.log('🔄 بدء المزامنة الشاملة...');
            
            // اختبار اتصال جميع الخوادم النشطة
            const activeServers = Array.from(this.servers.values()).filter(s => s.isActive);
            const connectionTests = await Promise.allSettled(
                activeServers.map(server => this.testServerConnection(server.id))
            );
            
            // الحصول على الخوادم المتاحة
            const availableServers = activeServers.filter((server, index) => 
                connectionTests[index].status === 'fulfilled' && connectionTests[index].value
            );
            
            if (availableServers.length === 0) {
                throw new Error('لا توجد خوادم متاحة للمزامنة');
            }
            
            // ترتيب الخوادم حسب الأولوية والزمن
            availableServers.sort((a, b) => {
                if (a.priority !== b.priority) return b.priority - a.priority;
                return (a.latency || Infinity) - (b.latency || Infinity);
            });
            
            // مزامنة البيانات مع كل خادم
            const syncResults = [];
            
            for (const server of availableServers) {
                try {
                    const result = await this.syncWithServer(server.id);
                    syncResults.push({ serverId: server.id, success: true, result });
                    
                } catch (error) {
                    console.error(`خطأ في مزامنة ${server.id}:`, error);
                    syncResults.push({ serverId: server.id, success: false, error: error.message });
                }
            }
            
            // تحديث الإحصائيات
            this.syncStats.totalSyncs++;
            this.syncStats.lastSyncTime = new Date().toISOString();
            
            const successfulSyncs = syncResults.filter(r => r.success).length;
            if (successfulSyncs > 0) {
                this.syncStats.successfulSyncs++;
            } else {
                this.syncStats.failedSyncs++;
            }
            
            // حفظ إحصائيات المزامنة
            await this.saveSyncStats();
            
            // تسجيل النشاط
            await this.storage.logActivity({
                action: 'sync_completed',
                category: 'synchronization',
                description: `تم إكمال المزامنة مع ${successfulSyncs}/${availableServers.length} خادم`,
                severity: successfulSyncs > 0 ? 'info' : 'warning',
                metadata: {
                    duration: Date.now() - syncStartTime,
                    serversCount: availableServers.length,
                    successfulSyncs: successfulSyncs,
                    results: syncResults
                }
            });
            
            console.log(`✅ تم إكمال المزامنة في ${Date.now() - syncStartTime}ms`);
            
            return {
                success: successfulSyncs > 0,
                results: syncResults,
                duration: Date.now() - syncStartTime,
                serversCount: availableServers.length,
                successfulSyncs: successfulSyncs
            };
            
        } catch (error) {
            this.syncStats.failedSyncs++;
            
            await this.storage.logActivity({
                action: 'sync_failed',
                category: 'synchronization',
                description: `فشل في المزامنة: ${error.message}`,
                severity: 'error',
                metadata: {
                    duration: Date.now() - syncStartTime,
                    error: error.message
                }
            });
            
            console.error('❌ فشل في المزامنة:', error);
            throw error;
            
        } finally {
            this.syncInProgress = false;
        }
    }
    
    /**
     * مزامنة مع خادم محدد
     */
    async syncWithServer(serverId) {
        const server = this.servers.get(serverId);
        if (!server) throw new Error(`خادم غير موجود: ${serverId}`);
        
        console.log(`🔄 مزامنة مع خادم ${serverId}...`);
        
        try {
            // الحصول على البيانات المحلية
            const localData = await this.getLocalSyncData();
            
            // إرسال البيانات للخادم والحصول على البيانات المحدثة
            const response = await fetch(`${server.url}/api/sync`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${server.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clientId: this.getClientId(),
                    timestamp: new Date().toISOString(),
                    data: localData
                }),
                timeout: server.timeout
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const serverData = await response.json();
            
            // دمج البيانات وحل التعارضات
            const mergeResult = await this.mergeData(localData, serverData);
            
            // حفظ البيانات المدموجة
            await this.saveResolvedData(mergeResult);
            
            // تحديث معلومات الخادم
            server.lastSync = new Date().toISOString();
            server.errorCount = 0;
            
            console.log(`✅ تم مزامنة ${serverId} بنجاح`);
            
            return {
                success: true,
                itemsSynced: mergeResult.itemsProcessed,
                conflictsResolved: mergeResult.conflictsResolved,
                dataTransferred: JSON.stringify(serverData).length
            };
            
        } catch (error) {
            server.errorCount++;
            throw error;
        }
    }
    
    /**
     * الحصول على البيانات المحلية للمزامنة
     */
    async getLocalSyncData() {
        const data = {
            users: await this.storage.getAllUsers(),
            events: await this.storage.getAllEvents(),
            activities: await this.storage.getAllFromStore(this.storage.stores.activities),
            reviews: await this.storage.getAllFromStore(this.storage.stores.reviews),
            lastModified: new Date().toISOString()
        };
        
        return data;
    }
    
    /**
     * دمج البيانات وحل التعارضات
     */
    async mergeData(localData, serverData) {
        const mergeResult = {
            users: [],
            events: [],
            activities: [],
            reviews: [],
            itemsProcessed: 0,
            conflictsResolved: 0
        };
        
        // دمج المستخدمين
        const userMerge = await this.mergeUsers(localData.users, serverData.users);
        mergeResult.users = userMerge.merged;
        mergeResult.conflictsResolved += userMerge.conflicts;
        
        // دمج الأحداث
        const eventMerge = await this.mergeEvents(localData.events, serverData.events);
        mergeResult.events = eventMerge.merged;
        mergeResult.conflictsResolved += eventMerge.conflicts;
        
        // دمج الأنشطة
        const activityMerge = await this.mergeActivities(localData.activities, serverData.activities);
        mergeResult.activities = activityMerge.merged;
        mergeResult.conflictsResolved += activityMerge.conflicts;
        
        // دمج المراجعات
        const reviewMerge = await this.mergeReviews(localData.reviews, serverData.reviews);
        mergeResult.reviews = reviewMerge.merged;
        mergeResult.conflictsResolved += reviewMerge.conflicts;
        
        mergeResult.itemsProcessed = 
            mergeResult.users.length + 
            mergeResult.events.length + 
            mergeResult.activities.length + 
            mergeResult.reviews.length;
        
        return mergeResult;
    }
    
    /**
     * دمج المستخدمين
     */
    async mergeUsers(localUsers, serverUsers) {
        const merged = new Map();
        let conflicts = 0;
        
        // إضافة المستخدمين المحليين
        localUsers.forEach(user => {
            merged.set(user.username, { ...user, source: 'local' });
        });
        
        // دمج المستخدمين من الخادم
        serverUsers.forEach(serverUser => {
            const localUser = merged.get(serverUser.username);
            
            if (!localUser) {
                // مستخدم جديد من الخادم
                merged.set(serverUser.username, { ...serverUser, source: 'server' });
            } else {
                // تعارض - نحتاج لحل التعارض
                const resolved = this.resolveUserConflict(localUser, serverUser);
                merged.set(serverUser.username, resolved);
                conflicts++;
            }
        });
        
        return {
            merged: Array.from(merged.values()),
            conflicts: conflicts
        };
    }
    
    /**
     * حل تعارض المستخدمين
     */
    resolveUserConflict(localUser, serverUser) {
        switch (this.conflictResolutionStrategy) {
            case 'latest_wins':
                const localTime = new Date(localUser.updatedAt || localUser.createdAt);
                const serverTime = new Date(serverUser.updatedAt || serverUser.createdAt);
                return serverTime > localTime ? serverUser : localUser;
                
            case 'merge':
                return {
                    ...localUser,
                    ...serverUser,
                    updatedAt: new Date().toISOString(),
                    source: 'merged'
                };
                
            default:
                return serverUser; // الخادم يفوز افتراضياً
        }
    }
    
    /**
     * إعداد مراقبة الشبكة
     */
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('🌐 تم الاتصال بالإنترنت');
            
            if (this.syncConfig.autoSync) {
                setTimeout(() => this.syncAll(), 1000);
            }
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('📴 تم قطع الاتصال بالإنترنت');
        });
    }
    
    /**
     * إعداد المزامنة التلقائية
     */
    setupAutoSync() {
        setInterval(async () => {
            if (this.isOnline && !this.syncInProgress) {
                try {
                    await this.syncAll();
                } catch (error) {
                    console.error('خطأ في المزامنة التلقائية:', error);
                }
            }
        }, this.syncConfig.syncInterval);
        
        console.log(`⏰ تم إعداد المزامنة التلقائية كل ${this.syncConfig.syncInterval / 1000} ثانية`);
    }
    
    /**
     * الحصول على معرف العميل
     */
    getClientId() {
        let clientId = localStorage.getItem('clientId');
        if (!clientId) {
            clientId = 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('clientId', clientId);
        }
        return clientId;
    }
}

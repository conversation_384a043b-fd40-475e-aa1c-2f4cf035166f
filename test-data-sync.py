#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام مزامنة البيانات
Data Synchronization System Test
"""

import requests
import json
import time
import uuid
from datetime import datetime

class DataSyncTester:
    def __init__(self, sync_server_url="http://localhost:8001"):
        self.sync_server_url = sync_server_url
        self.test_results = []
        
    def print_header(self):
        """طباعة رأس الاختبار"""
        print("=" * 60)
        print("🔄 اختبار نظام مزامنة البيانات")
        print("   Data Synchronization System Test")
        print("=" * 60)
        print()
    
    def test_server_connection(self):
        """اختبار الاتصال بخادم المزامنة"""
        print("🌐 اختبار الاتصال بخادم المزامنة...")
        
        try:
            response = requests.get(f"{self.sync_server_url}/api/sync/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print("   ✅ خادم المزامنة متصل")
                print(f"   📊 المستخدمين: {status.get('users_count', 0)}")
                print(f"   📊 الأحداث: {status.get('events_count', 0)}")
                print(f"   📊 الأنشطة: {status.get('activities_count', 0)}")
                self.test_results.append(("server_connection", True, "متصل"))
                return True
            else:
                print(f"   ❌ خطأ في الاستجابة: {response.status_code}")
                self.test_results.append(("server_connection", False, f"خطأ {response.status_code}"))
                return False
        except requests.exceptions.ConnectionError:
            print("   ❌ لا يمكن الاتصال بخادم المزامنة")
            print("   💡 تأكد من تشغيل خادم المزامنة أولاً")
            self.test_results.append(("server_connection", False, "لا يمكن الاتصال"))
            return False
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            self.test_results.append(("server_connection", False, str(e)))
            return False
    
    def test_user_sync(self):
        """اختبار مزامنة المستخدمين"""
        print("\n👥 اختبار مزامنة المستخدمين...")
        
        # إنشاء بيانات اختبار
        test_users = {
            f"test_user_{uuid.uuid4().hex[:8]}": {
                "username": f"test_user_{uuid.uuid4().hex[:8]}",
                "fullName": "مستخدم اختبار",
                "email": "<EMAIL>",
                "role": "analyst",
                "isActive": True,
                "lastModified": int(time.time() * 1000)
            }
        }
        
        try:
            # إرسال البيانات
            response = requests.post(
                f"{self.sync_server_url}/api/sync/users",
                json=test_users,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ تم إرسال بيانات المستخدمين")
                print(f"   📝 النتيجة: {result.get('message', 'نجح')}")
                
                # التحقق من استلام البيانات
                get_response = requests.get(f"{self.sync_server_url}/api/sync/users")
                if get_response.status_code == 200:
                    users = get_response.json()
                    if any(username in users for username in test_users.keys()):
                        print("   ✅ تم استلام البيانات بنجاح")
                        self.test_results.append(("user_sync", True, "نجح"))
                        return True
                    else:
                        print("   ⚠️  لم يتم العثور على البيانات المرسلة")
                        self.test_results.append(("user_sync", False, "البيانات غير موجودة"))
                        return False
                else:
                    print("   ❌ فشل في استلام البيانات")
                    self.test_results.append(("user_sync", False, "فشل الاستلام"))
                    return False
            else:
                print(f"   ❌ فشل في إرسال البيانات: {response.status_code}")
                self.test_results.append(("user_sync", False, f"خطأ {response.status_code}"))
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار المستخدمين: {e}")
            self.test_results.append(("user_sync", False, str(e)))
            return False
    
    def test_event_sync(self):
        """اختبار مزامنة الأحداث"""
        print("\n📋 اختبار مزامنة الأحداث...")
        
        # إنشاء بيانات اختبار
        test_events = [
            {
                "id": str(uuid.uuid4()),
                "serial": f"TEST-{int(time.time())}",
                "title": "حدث اختبار المزامنة",
                "type": "test",
                "severity": "low",
                "description": "هذا حدث اختبار لنظام المزامنة",
                "timestamp": datetime.now().isoformat(),
                "status": "open",
                "responsiblePerson": "نظام الاختبار"
            }
        ]
        
        try:
            # إرسال البيانات
            response = requests.post(
                f"{self.sync_server_url}/api/sync/events",
                json=test_events,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ تم إرسال بيانات الأحداث")
                print(f"   📝 النتيجة: {result.get('message', 'نجح')}")
                
                # التحقق من استلام البيانات
                get_response = requests.get(f"{self.sync_server_url}/api/sync/events")
                if get_response.status_code == 200:
                    events = get_response.json()
                    if any(event['id'] == test_events[0]['id'] for event in events):
                        print("   ✅ تم استلام البيانات بنجاح")
                        self.test_results.append(("event_sync", True, "نجح"))
                        return True
                    else:
                        print("   ⚠️  لم يتم العثور على البيانات المرسلة")
                        self.test_results.append(("event_sync", False, "البيانات غير موجودة"))
                        return False
                else:
                    print("   ❌ فشل في استلام البيانات")
                    self.test_results.append(("event_sync", False, "فشل الاستلام"))
                    return False
            else:
                print(f"   ❌ فشل في إرسال البيانات: {response.status_code}")
                self.test_results.append(("event_sync", False, f"خطأ {response.status_code}"))
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الأحداث: {e}")
            self.test_results.append(("event_sync", False, str(e)))
            return False
    
    def test_activity_sync(self):
        """اختبار مزامنة الأنشطة"""
        print("\n📊 اختبار مزامنة الأنشطة...")
        
        # إنشاء بيانات اختبار
        test_activities = [
            {
                "id": str(uuid.uuid4()),
                "userId": "test_user",
                "username": "مستخدم اختبار",
                "action": "test_sync",
                "description": "اختبار مزامنة الأنشطة",
                "timestamp": datetime.now().isoformat(),
                "ip": "127.0.0.1",
                "userAgent": "Test Agent"
            }
        ]
        
        try:
            # إرسال البيانات
            response = requests.post(
                f"{self.sync_server_url}/api/sync/activities",
                json=test_activities,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ تم إرسال بيانات الأنشطة")
                print(f"   📝 النتيجة: {result.get('message', 'نجح')}")
                
                # التحقق من استلام البيانات
                get_response = requests.get(f"{self.sync_server_url}/api/sync/activities")
                if get_response.status_code == 200:
                    activities = get_response.json()
                    if any(activity['id'] == test_activities[0]['id'] for activity in activities):
                        print("   ✅ تم استلام البيانات بنجاح")
                        self.test_results.append(("activity_sync", True, "نجح"))
                        return True
                    else:
                        print("   ⚠️  لم يتم العثور على البيانات المرسلة")
                        self.test_results.append(("activity_sync", False, "البيانات غير موجودة"))
                        return False
                else:
                    print("   ❌ فشل في استلام البيانات")
                    self.test_results.append(("activity_sync", False, "فشل الاستلام"))
                    return False
            else:
                print(f"   ❌ فشل في إرسال البيانات: {response.status_code}")
                self.test_results.append(("activity_sync", False, f"خطأ {response.status_code}"))
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الأنشطة: {e}")
            self.test_results.append(("activity_sync", False, str(e)))
            return False
    
    def test_full_sync(self):
        """اختبار المزامنة الشاملة"""
        print("\n🔄 اختبار المزامنة الشاملة...")
        
        # إنشاء بيانات شاملة
        full_data = {
            "users": {
                f"full_test_user_{uuid.uuid4().hex[:8]}": {
                    "username": f"full_test_user_{uuid.uuid4().hex[:8]}",
                    "fullName": "مستخدم اختبار شامل",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "isActive": True,
                    "lastModified": int(time.time() * 1000)
                }
            },
            "events": [
                {
                    "id": str(uuid.uuid4()),
                    "serial": f"FULL-TEST-{int(time.time())}",
                    "title": "اختبار مزامنة شاملة",
                    "type": "test",
                    "severity": "medium",
                    "description": "اختبار المزامنة الشاملة للنظام",
                    "timestamp": datetime.now().isoformat(),
                    "status": "open"
                }
            ],
            "activities": [
                {
                    "id": str(uuid.uuid4()),
                    "userId": "full_test",
                    "username": "اختبار شامل",
                    "action": "full_sync_test",
                    "description": "اختبار المزامنة الشاملة",
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
        
        try:
            # إرسال البيانات الشاملة
            response = requests.post(
                f"{self.sync_server_url}/api/sync/full",
                json=full_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ تم إرسال البيانات الشاملة")
                print(f"   📝 النتيجة: {result.get('message', 'نجح')}")
                
                # التحقق من جميع البيانات
                get_response = requests.get(f"{self.sync_server_url}/api/sync/all")
                if get_response.status_code == 200:
                    all_data = get_response.json()
                    
                    # فحص المستخدمين
                    users_found = any(username in all_data.get('users', {}) 
                                    for username in full_data['users'].keys())
                    
                    # فحص الأحداث
                    events_found = any(event['id'] == full_data['events'][0]['id'] 
                                     for event in all_data.get('events', []))
                    
                    # فحص الأنشطة
                    activities_found = any(activity['id'] == full_data['activities'][0]['id'] 
                                         for activity in all_data.get('activities', []))
                    
                    if users_found and events_found and activities_found:
                        print("   ✅ تم استلام جميع البيانات بنجاح")
                        self.test_results.append(("full_sync", True, "نجح"))
                        return True
                    else:
                        print("   ⚠️  بعض البيانات مفقودة")
                        self.test_results.append(("full_sync", False, "بيانات مفقودة"))
                        return False
                else:
                    print("   ❌ فشل في استلام البيانات الشاملة")
                    self.test_results.append(("full_sync", False, "فشل الاستلام"))
                    return False
            else:
                print(f"   ❌ فشل في إرسال البيانات الشاملة: {response.status_code}")
                self.test_results.append(("full_sync", False, f"خطأ {response.status_code}"))
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار المزامنة الشاملة: {e}")
            self.test_results.append(("full_sync", False, str(e)))
            return False
    
    def generate_report(self):
        """إنشاء تقرير الاختبار"""
        print("\n📋 تقرير اختبار المزامنة:")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, success, _ in self.test_results if success)
        
        for test_name, success, message in self.test_results:
            status = "✅ نجح" if success else "❌ فشل"
            print(f"   {test_name}: {status} - {message}")
        
        print("-" * 50)
        print(f"📊 النتيجة الإجمالية: {passed_tests}/{total_tests} اختبار نجح")
        
        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! نظام المزامنة يعمل بشكل مثالي")
        else:
            print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        
        print("=" * 50)
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        self.print_header()
        
        # اختبار الاتصال
        if not self.test_server_connection():
            print("\n❌ فشل في الاتصال بخادم المزامنة")
            print("💡 تأكد من تشغيل خادم المزامنة أولاً:")
            print("   python data-sync-server.py")
            print("   أو")
            print("   python start-complete-system.py")
            return False
        
        # تشغيل الاختبارات
        self.test_user_sync()
        self.test_event_sync()
        self.test_activity_sync()
        self.test_full_sync()
        
        # إنشاء التقرير
        return self.generate_report()

def main():
    """الدالة الرئيسية"""
    import sys
    
    # تحديد عنوان خادم المزامنة
    sync_url = "http://localhost:8001"
    if len(sys.argv) > 1:
        sync_url = sys.argv[1]
    
    print(f"🔗 اختبار خادم المزامنة: {sync_url}")
    print()
    
    # تشغيل الاختبارات
    tester = DataSyncTester(sync_url)
    success = tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

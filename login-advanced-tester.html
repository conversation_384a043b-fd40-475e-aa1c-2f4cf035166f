<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول المتقدم</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            margin: 15px 0;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .success { border-left: 5px solid #27ae60; background: rgba(39, 174, 96, 0.1); }
        .error { border-left: 5px solid #e74c3c; background: rgba(231, 76, 60, 0.1); }
        .warning { border-left: 5px solid #f39c12; background: rgba(243, 156, 18, 0.1); }
        .info { border-left: 5px solid #3498db; background: rgba(52, 152, 219, 0.1); }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
        input, select {
            padding: 10px;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-family: inherit;
            width: 200px;
        }
        input:focus { border-color: #3498db; outline: none; }
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        h3 { color: #2c3e50; margin-bottom: 15px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: 500; }
        .user-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .user-card h4 { color: #495057; margin-bottom: 8px; }
        .user-card p { margin: 4px 0; color: #6c757d; }
        .quick-login { display: flex; align-items: center; gap: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تسجيل الدخول المتقدم</h1>
            <p>أداة شاملة لتشخيص وحل مشاكل تسجيل الدخول</p>
        </div>

        <div class="grid">
            <!-- معلومات النظام -->
            <div class="card info">
                <h3>📊 معلومات النظام</h3>
                <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
                <p><strong>localStorage:</strong> <span id="localStorageStatus"></span></p>
                <p><strong>sessionStorage:</strong> <span id="sessionStorageStatus"></span></p>
                <p><strong>JavaScript:</strong> <span class="status-indicator status-online"></span> مفعل</p>
                <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
                <p><strong>حالة الخادم:</strong> <span id="serverStatus">جاري الفحص...</span></p>
            </div>

            <!-- اختبار سريع -->
            <div class="card">
                <h3>⚡ اختبار سريع</h3>
                <div class="quick-login">
                    <select id="quickUser">
                        <option value="admin">admin</option>
                        <option value="analyst">analyst</option>
                        <option value="operator">operator</option>
                    </select>
                    <button class="btn-primary" onclick="quickLogin()">تسجيل دخول سريع</button>
                </div>
                <div id="quickResult"></div>
            </div>
        </div>

        <div class="grid">
            <!-- اختبار مخصص -->
            <div class="card">
                <h3>🔧 اختبار مخصص</h3>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="testUsername" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="testPassword" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn-primary" onclick="testCustomLogin()">اختبار</button>
                <button class="btn-info" onclick="showPasswordHash()">عرض التشفير</button>
                <div id="customResult" style="margin-top: 15px;"></div>
            </div>

            <!-- إدارة البيانات -->
            <div class="card">
                <h3>💾 إدارة البيانات</h3>
                <button class="btn-success" onclick="loadUsers()">تحميل المستخدمين</button>
                <button class="btn-warning" onclick="resetUsers()">إعادة تعيين</button>
                <button class="btn-danger" onclick="clearAllData()">مسح الكل</button>
                <button class="btn-info" onclick="exportData()">تصدير البيانات</button>
                <div id="usersInfo" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- المستخدمين المتاحين -->
        <div class="card">
            <h3>👥 المستخدمين المتاحين</h3>
            <div id="usersList"></div>
        </div>

        <!-- سجل الأحداث -->
        <div class="card">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn-info" onclick="clearLog()">مسح السجل</button>
            <button class="btn-success" onclick="exportLog()">تصدير السجل</button>
            <div id="debugLog" class="log"></div>
        </div>

        <!-- روابط مفيدة -->
        <div class="card info">
            <h3>🔗 روابط مفيدة</h3>
            <button class="btn-success" onclick="window.open('login.html', '_blank')">صفحة تسجيل الدخول</button>
            <button class="btn-success" onclick="window.open('index.html', '_blank')">الصفحة الرئيسية</button>
            <button class="btn-info" onclick="window.open('clear-browser-data.html', '_blank')">مسح البيانات</button>
            <button class="btn-warning" onclick="window.location.reload()">إعادة تحميل</button>
        </div>
    </div>

    <script>
        // تحديث معلومات النظام
        function updateSystemInfo() {
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-indicator status-online"></span>يعمل';
            } catch (e) {
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-indicator status-offline"></span>لا يعمل';
            }

            // فحص sessionStorage
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-indicator status-online"></span>يعمل';
            } catch (e) {
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-indicator status-offline"></span>لا يعمل';
            }
        }

        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('/');
                if (response.ok) {
                    document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-online"></span>متصل';
                } else {
                    document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-warning"></span>مشاكل في الاتصال';
                }
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = '<span class="status-indicator status-offline"></span>غير متصل';
            }
        }

        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }

        // تسجيل دخول سريع
        function quickLogin() {
            const username = document.getElementById('quickUser').value;
            const passwords = { admin: 'admin123', analyst: 'analyst123', operator: 'operator123' };
            const password = passwords[username];

            log(`تسجيل دخول سريع: ${username}`);
            testLogin(username, password, 'quickResult');
        }

        // اختبار مخصص
        function testCustomLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;

            if (!username || !password) {
                showResult('customResult', 'يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            log(`اختبار مخصص: ${username}`);
            testLogin(username, password, 'customResult');
        }

        // عرض تشفير كلمة المرور
        function showPasswordHash() {
            const password = document.getElementById('testPassword').value;
            if (!password) {
                showResult('customResult', 'أدخل كلمة المرور أولاً', 'warning');
                return;
            }

            const hash = hashPassword(password);
            showResult('customResult', `كلمة المرور المشفرة: ${hash}`, 'info');
            log(`تشفير كلمة المرور: ${password} -> ${hash}`);
        }

        // اختبار تسجيل الدخول الأساسي
        function testLogin(username, password, resultElementId) {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[username];

                if (!user) {
                    showResult(resultElementId, `المستخدم ${username} غير موجود`, 'error');
                    log(`المستخدم ${username} غير موجود`);
                    return false;
                }

                if (!user.isActive) {
                    showResult(resultElementId, `الحساب ${username} معطل`, 'error');
                    log(`الحساب ${username} معطل`);
                    return false;
                }

                const hashedPassword = hashPassword(password);
                log(`مقارنة كلمات المرور: ${hashedPassword} vs ${user.password}`);

                if (user.password === hashedPassword) {
                    showResult(resultElementId, `✅ تسجيل دخول ناجح للمستخدم ${username} (${user.role})`, 'success');
                    log(`تسجيل دخول ناجح: ${username}`);
                    return true;
                } else {
                    showResult(resultElementId, `❌ كلمة مرور خاطئة للمستخدم ${username}`, 'error');
                    log(`كلمة مرور خاطئة: ${username}`);
                    return false;
                }

            } catch (error) {
                showResult(resultElementId, `خطأ في الاختبار: ${error.message}`, 'error');
                log(`خطأ في اختبار تسجيل الدخول: ${error.message}`);
                return false;
            }
        }

        // عرض النتيجة
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `card ${type}`;
            element.innerHTML = `<p>${message}</p>`;
        }

        // تحميل المستخدمين
        function loadUsers() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const userCount = Object.keys(users).length;

                showResult('usersInfo', `تم العثور على ${userCount} مستخدم`, userCount > 0 ? 'success' : 'warning');

                // عرض قائمة المستخدمين
                displayUsersList(users);
                log(`تم تحميل ${userCount} مستخدم`);

            } catch (error) {
                showResult('usersInfo', `خطأ في تحميل المستخدمين: ${error.message}`, 'error');
                log(`خطأ في تحميل المستخدمين: ${error.message}`);
            }
        }

        // عرض قائمة المستخدمين
        function displayUsersList(users) {
            const usersList = document.getElementById('usersList');
            let html = '';

            for (const [username, user] of Object.entries(users)) {
                html += `
                    <div class="user-card">
                        <h4>${user.fullName} (${username})</h4>
                        <p><strong>الدور:</strong> ${user.role}</p>
                        <p><strong>البريد:</strong> ${user.email}</p>
                        <p><strong>الحالة:</strong> ${user.isActive ? '✅ نشط' : '❌ معطل'}</p>
                        <p><strong>آخر دخول:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}</p>
                        <button class="btn-primary" onclick="testUserLogin('${username}')">اختبار تسجيل الدخول</button>
                    </div>
                `;
            }

            usersList.innerHTML = html || '<p>لا توجد مستخدمين</p>';
        }

        // اختبار تسجيل دخول مستخدم محدد
        function testUserLogin(username) {
            const passwords = { admin: 'admin123', analyst: 'analyst123', operator: 'operator123' };
            const password = passwords[username] || 'password123';

            const success = testLogin(username, password, 'usersInfo');
            if (success) {
                log(`اختبار ناجح للمستخدم ${username}`);
            }
        }

        // إعادة تعيين المستخدمين
        function resetUsers() {
            if (!confirm('هل أنت متأكد من إعادة تعيين جميع المستخدمين؟')) return;

            const defaultUsers = {
                "admin": {
                    "id": "admin",
                    "username": "admin",
                    "password": hashPassword("admin123"),
                    "fullName": "مدير النظام",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "analyst": {
                    "id": "analyst",
                    "username": "analyst",
                    "password": hashPassword("analyst123"),
                    "fullName": "محلل أمني",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "permissions": ["read", "write", "view_analytics"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "operator": {
                    "id": "operator",
                    "username": "operator",
                    "password": hashPassword("operator123"),
                    "fullName": "مشغل النظام",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "permissions": ["read"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                }
            };

            try {
                localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                showResult('usersInfo', '✅ تم إعادة تعيين المستخدمين الافتراضيين', 'success');
                log('تم إعادة تعيين المستخدمين الافتراضيين');
                loadUsers();
            } catch (error) {
                showResult('usersInfo', `خطأ في إعادة التعيين: ${error.message}`, 'error');
                log(`خطأ في إعادة تعيين المستخدمين: ${error.message}`);
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟')) return;

            try {
                localStorage.clear();
                sessionStorage.clear();
                showResult('usersInfo', '✅ تم مسح جميع البيانات', 'success');
                log('تم مسح جميع البيانات');
                setTimeout(() => window.location.reload(), 1500);
            } catch (error) {
                showResult('usersInfo', `خطأ في مسح البيانات: ${error.message}`, 'error');
                log(`خطأ في مسح البيانات: ${error.message}`);
            }
        }

        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    users: JSON.parse(localStorage.getItem('systemUsers') || '{}'),
                    sessions: JSON.parse(localStorage.getItem('userSessions') || '{}'),
                    currentSession: localStorage.getItem('currentSession'),
                    timestamp: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `isms-data-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                log('تم تصدير البيانات');
            } catch (error) {
                log(`خطأ في تصدير البيانات: ${error.message}`);
            }
        }

        // سجل الأحداث
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            log('تم مسح السجل');
        }

        function exportLog() {
            const logContent = document.getElementById('debugLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `isms-log-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // تهيئة الصفحة
        window.onload = function() {
            updateSystemInfo();
            updateTime();
            setInterval(updateTime, 1000);
            checkServerStatus();
            loadUsers();
            log('تم تحميل أداة اختبار تسجيل الدخول');
        };
    </script>
</body>
</html>
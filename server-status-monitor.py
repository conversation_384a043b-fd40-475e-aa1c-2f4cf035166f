#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب حالة الخادم لنظام إدارة أمن المعلومات
Server Status Monitor for ISMS
"""

import os
import sys
import json
import socket
import requests
import subprocess
import webbrowser
from datetime import datetime
import time

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_port_status(host, port):
    """فحص حالة المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(3)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception:
        return False

def check_http_status(url):
    """فحص حالة HTTP"""
    try:
        response = requests.get(url, timeout=5)
        return {
            'status': 'online',
            'status_code': response.status_code,
            'response_time': response.elapsed.total_seconds()
        }
    except requests.exceptions.RequestException as e:
        return {
            'status': 'offline',
            'error': str(e)
        }

def get_server_info():
    """الحصول على معلومات الخادم"""
    local_ip = get_local_ip()
    port = 8000
    
    info = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'local_ip': local_ip,
        'port': port,
        'urls': {
            'local': f'http://localhost:{port}',
            'network': f'http://{local_ip}:{port}',
            'login': f'http://{local_ip}:{port}/login-fixed.html',
            'test': f'http://{local_ip}:{port}/network-test.html'
        }
    }
    
    # فحص حالة المنفذ
    info['port_status'] = {
        'localhost': check_port_status('localhost', port),
        'network': check_port_status(local_ip, port)
    }
    
    # فحص حالة HTTP
    for name, url in info['urls'].items():
        info[f'{name}_http'] = check_http_status(url)
    
    return info

def display_server_status():
    """عرض حالة الخادم"""
    print("🌐 مراقب حالة الخادم - نظام إدارة أمن المعلومات")
    print("=" * 60)
    
    info = get_server_info()
    
    print(f"📅 الوقت: {info['timestamp']}")
    print(f"📍 عنوان IP المحلي: {info['local_ip']}")
    print(f"🔌 المنفذ: {info['port']}")
    
    print(f"\n🔍 حالة المنافذ:")
    for location, status in info['port_status'].items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {location}: {'مفتوح' if status else 'مغلق'}")
    
    print(f"\n🌐 حالة HTTP:")
    for name, url in info['urls'].items():
        http_info = info[f'{name}_http']
        if http_info['status'] == 'online':
            print(f"   ✅ {name}: متاح (كود: {http_info['status_code']}, وقت الاستجابة: {http_info['response_time']:.2f}s)")
        else:
            print(f"   ❌ {name}: غير متاح ({http_info.get('error', 'خطأ غير معروف')})")
    
    print(f"\n🔗 الروابط:")
    for name, url in info['urls'].items():
        print(f"   • {name}: {url}")
    
    return info

def start_server_if_needed():
    """بدء الخادم إذا لم يكن يعمل"""
    info = get_server_info()
    
    # فحص إذا كان الخادم يعمل
    if info['port_status']['localhost'] and info['local_http']['status'] == 'online':
        print("✅ الخادم يعمل بالفعل!")
        return True
    
    print("🚀 بدء الخادم...")
    
    # محاولة بدء الخادم
    try:
        # البحث عن سكريبت بدء الخادم
        server_scripts = [
            'start-network-server.py',
            'simple-network-server.py',
            'start-server.py'
        ]
        
        for script in server_scripts:
            if os.path.exists(script):
                print(f"🔄 تشغيل {script}...")
                process = subprocess.Popen([sys.executable, script])
                
                # انتظار قصير للتأكد من بدء الخادم
                time.sleep(3)
                
                # فحص إذا بدأ الخادم
                if check_port_status('localhost', 8000):
                    print("✅ تم بدء الخادم بنجاح!")
                    return True
                else:
                    print(f"❌ فشل في بدء الخادم باستخدام {script}")
        
        # إذا لم تعمل السكريبتات، استخدم الخادم المدمج
        print("🔄 محاولة استخدام الخادم المدمج...")
        process = subprocess.Popen([
            sys.executable, '-m', 'http.server', '8000', '--bind', '0.0.0.0'
        ])
        
        time.sleep(3)
        
        if check_port_status('localhost', 8000):
            print("✅ تم بدء الخادم المدمج بنجاح!")
            return True
        else:
            print("❌ فشل في بدء الخادم المدمج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False

def open_browser_pages():
    """فتح صفحات المتصفح"""
    info = get_server_info()
    
    if info['local_http']['status'] == 'online':
        print("🌐 فتح صفحات المتصفح...")
        
        pages = [
            ('صفحة اختبار الشبكة', info['urls']['test']),
            ('صفحة تسجيل الدخول', info['urls']['login']),
            ('الصفحة الرئيسية', info['urls']['network'])
        ]
        
        for name, url in pages:
            try:
                webbrowser.open(url)
                print(f"✅ تم فتح {name}")
                time.sleep(1)
            except Exception as e:
                print(f"❌ فشل في فتح {name}: {e}")
    else:
        print("❌ لا يمكن فتح المتصفح - الخادم غير متاح")

def create_status_report():
    """إنشاء تقرير حالة"""
    info = get_server_info()
    
    report = f"""# تقرير حالة الخادم - {info['timestamp']}

## معلومات الشبكة
- **عنوان IP المحلي:** {info['local_ip']}
- **المنفذ:** {info['port']}

## حالة المنافذ
"""
    
    for location, status in info['port_status'].items():
        status_text = "✅ مفتوح" if status else "❌ مغلق"
        report += f"- **{location}:** {status_text}\n"
    
    report += "\n## حالة HTTP\n"
    
    for name, url in info['urls'].items():
        http_info = info[f'{name}_http']
        if http_info['status'] == 'online':
            report += f"- **{name}:** ✅ متاح (كود: {http_info['status_code']})\n"
        else:
            report += f"- **{name}:** ❌ غير متاح\n"
    
    report += "\n## الروابط\n"
    
    for name, url in info['urls'].items():
        report += f"- **{name}:** {url}\n"
    
    # حفظ التقرير
    with open('server-status-report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📄 تم إنشاء تقرير الحالة: server-status-report.md")
    return report

def main():
    """الوظيفة الرئيسية"""
    
    while True:
        print("\n" + "="*60)
        print("🛠️  مراقب حالة الخادم - نظام إدارة أمن المعلومات")
        print("="*60)
        print("1. عرض حالة الخادم")
        print("2. بدء الخادم (إذا لم يكن يعمل)")
        print("3. فتح صفحات المتصفح")
        print("4. إنشاء تقرير حالة")
        print("5. مراقبة مستمرة (كل 30 ثانية)")
        print("6. خروج")
        print("="*60)
        
        try:
            choice = input("اختر رقم الخيار: ").strip()
            
            if choice == '1':
                display_server_status()
                
            elif choice == '2':
                start_server_if_needed()
                
            elif choice == '3':
                open_browser_pages()
                
            elif choice == '4':
                create_status_report()
                
            elif choice == '5':
                print("🔄 بدء المراقبة المستمرة...")
                print("اضغط Ctrl+C لإيقاف المراقبة")
                try:
                    while True:
                        os.system('cls' if os.name == 'nt' else 'clear')
                        display_server_status()
                        print("\n⏰ المراقبة التالية خلال 30 ثانية...")
                        time.sleep(30)
                except KeyboardInterrupt:
                    print("\n🛑 تم إيقاف المراقبة")
                    
            elif choice == '6':
                print("👋 شكراً لاستخدام مراقب حالة الخادم")
                break
                
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()

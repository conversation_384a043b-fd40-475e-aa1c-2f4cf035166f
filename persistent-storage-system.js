/**
 * نظام التخزين الدائم الشامل لنظام إدارة أمن المعلومات
 * Comprehensive Persistent Storage System for ISMS
 * 
 * يوفر هذا النظام:
 * - تخزين دائم باستخدام IndexedDB
 * - نسخ احتياطية تلقائية
 * - تسجيل شامل للأنشطة
 * - تصدير واستيراد البيانات
 * - مزامنة مع خوادم متعددة
 * - تشفير البيانات الحساسة
 */

class PersistentStorageSystem {
    constructor() {
        this.dbName = 'ISMS_Database';
        this.dbVersion = 1;
        this.db = null;
        this.isInitialized = false;
        
        // تكوين قاعدة البيانات
        this.stores = {
            users: 'users',
            events: 'security_events',
            activities: 'activity_logs',
            reviews: 'post_incident_reviews',
            risks: 'risk_analysis',
            backups: 'backup_history',
            settings: 'system_settings',
            sync_log: 'sync_operations'
        };
        
        // إعدادات النسخ الاحتياطي
        this.backupConfig = {
            autoBackup: true,
            backupInterval: 24 * 60 * 60 * 1000, // 24 ساعة
            maxBackups: 30, // الاحتفاظ بـ 30 نسخة احتياطية
            compressionEnabled: true
        };
        
        // إعدادات التشفير
        this.encryptionConfig = {
            algorithm: 'AES-GCM',
            keyLength: 256,
            ivLength: 12
        };
        
        this.init();
    }
    
    /**
     * تهيئة قاعدة البيانات
     */
    async init() {
        try {
            await this.initializeDatabase();
            await this.migrateFromLocalStorage();
            await this.setupAutoBackup();
            await this.initializeActivityLogging();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام التخزين الدائم بنجاح');
            
            // إشعار النظام بجاهزية التخزين
            this.dispatchEvent('storage-ready');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التخزين:', error);
            throw error;
        }
    }
    
    /**
     * إنشاء وتهيئة قاعدة البيانات
     */
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                reject(new Error('فشل في فتح قاعدة البيانات'));
            };
            
            request.onsuccess = (event) => {
                this.db = event.target.result;
                console.log('📊 تم فتح قاعدة البيانات بنجاح');
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // إنشاء جدول المستخدمين
                if (!db.objectStoreNames.contains(this.stores.users)) {
                    const usersStore = db.createObjectStore(this.stores.users, { keyPath: 'username' });
                    usersStore.createIndex('email', 'email', { unique: false });
                    usersStore.createIndex('role', 'role', { unique: false });
                    usersStore.createIndex('isActive', 'isActive', { unique: false });
                    usersStore.createIndex('createdAt', 'createdAt', { unique: false });
                }
                
                // إنشاء جدول الأحداث الأمنية
                if (!db.objectStoreNames.contains(this.stores.events)) {
                    const eventsStore = db.createObjectStore(this.stores.events, { keyPath: 'id', autoIncrement: true });
                    eventsStore.createIndex('serialNumber', 'serialNumber', { unique: true });
                    eventsStore.createIndex('severity', 'severity', { unique: false });
                    eventsStore.createIndex('status', 'status', { unique: false });
                    eventsStore.createIndex('category', 'category', { unique: false });
                    eventsStore.createIndex('dateTime', 'dateTime', { unique: false });
                    eventsStore.createIndex('createdBy', 'createdBy', { unique: false });
                }
                
                // إنشاء جدول سجل الأنشطة
                if (!db.objectStoreNames.contains(this.stores.activities)) {
                    const activitiesStore = db.createObjectStore(this.stores.activities, { keyPath: 'id', autoIncrement: true });
                    activitiesStore.createIndex('userId', 'userId', { unique: false });
                    activitiesStore.createIndex('action', 'action', { unique: false });
                    activitiesStore.createIndex('timestamp', 'timestamp', { unique: false });
                    activitiesStore.createIndex('category', 'category', { unique: false });
                    activitiesStore.createIndex('severity', 'severity', { unique: false });
                }
                
                // إنشاء جدول مراجعات ما بعد الحادث
                if (!db.objectStoreNames.contains(this.stores.reviews)) {
                    const reviewsStore = db.createObjectStore(this.stores.reviews, { keyPath: 'id', autoIncrement: true });
                    reviewsStore.createIndex('incidentRecordNumber', 'incidentRecordNumber', { unique: false });
                    reviewsStore.createIndex('reviewDate', 'reviewDate', { unique: false });
                    reviewsStore.createIndex('createdBy', 'createdBy', { unique: false });
                }
                
                // إنشاء جدول تحليل المخاطر
                if (!db.objectStoreNames.contains(this.stores.risks)) {
                    const risksStore = db.createObjectStore(this.stores.risks, { keyPath: 'id', autoIncrement: true });
                    risksStore.createIndex('riskType', 'riskType', { unique: false });
                    risksStore.createIndex('severity', 'severity', { unique: false });
                    risksStore.createIndex('status', 'status', { unique: false });
                    risksStore.createIndex('assessmentDate', 'assessmentDate', { unique: false });
                }
                
                // إنشاء جدول تاريخ النسخ الاحتياطية
                if (!db.objectStoreNames.contains(this.stores.backups)) {
                    const backupsStore = db.createObjectStore(this.stores.backups, { keyPath: 'id', autoIncrement: true });
                    backupsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    backupsStore.createIndex('type', 'type', { unique: false });
                    backupsStore.createIndex('size', 'size', { unique: false });
                    backupsStore.createIndex('status', 'status', { unique: false });
                }
                
                // إنشاء جدول إعدادات النظام
                if (!db.objectStoreNames.contains(this.stores.settings)) {
                    const settingsStore = db.createObjectStore(this.stores.settings, { keyPath: 'key' });
                    settingsStore.createIndex('category', 'category', { unique: false });
                    settingsStore.createIndex('updatedAt', 'updatedAt', { unique: false });
                }
                
                // إنشاء جدول سجل المزامنة
                if (!db.objectStoreNames.contains(this.stores.sync_log)) {
                    const syncStore = db.createObjectStore(this.stores.sync_log, { keyPath: 'id', autoIncrement: true });
                    syncStore.createIndex('operation', 'operation', { unique: false });
                    syncStore.createIndex('timestamp', 'timestamp', { unique: false });
                    syncStore.createIndex('status', 'status', { unique: false });
                    syncStore.createIndex('serverId', 'serverId', { unique: false });
                }
                
                console.log('🏗️ تم إنشاء هيكل قاعدة البيانات');
            };
        });
    }
    
    /**
     * ترحيل البيانات من localStorage إلى IndexedDB
     */
    async migrateFromLocalStorage() {
        try {
            console.log('🔄 بدء ترحيل البيانات من localStorage...');
            
            // ترحيل المستخدمين
            const users = localStorage.getItem('systemUsers');
            if (users) {
                const usersData = JSON.parse(users);
                for (const [username, userData] of Object.entries(usersData)) {
                    await this.saveUser(userData);
                }
                console.log(`✅ تم ترحيل ${Object.keys(usersData).length} مستخدم`);
            }
            
            // ترحيل الأحداث الأمنية
            const events = localStorage.getItem('securityEvents');
            if (events) {
                const eventsData = JSON.parse(events);
                for (const event of eventsData) {
                    await this.saveEvent(event);
                }
                console.log(`✅ تم ترحيل ${eventsData.length} حدث أمني`);
            }
            
            // ترحيل مراجعات ما بعد الحادث
            const reviews = localStorage.getItem('postIncidentReviews');
            if (reviews) {
                const reviewsData = JSON.parse(reviews);
                for (const review of reviewsData) {
                    await this.saveReview(review);
                }
                console.log(`✅ تم ترحيل ${reviewsData.length} مراجعة`);
            }
            
            // ترحيل سجل الأنشطة
            const activities = localStorage.getItem('userActivities');
            if (activities) {
                const activitiesData = JSON.parse(activities);
                for (const activity of activitiesData) {
                    await this.saveActivity(activity);
                }
                console.log(`✅ تم ترحيل ${activitiesData.length} نشاط`);
            }
            
            // حفظ إعدادات النظام
            await this.saveSetting('migration_completed', {
                timestamp: new Date().toISOString(),
                version: this.dbVersion
            });
            
            console.log('✅ تم الانتهاء من ترحيل البيانات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في ترحيل البيانات:', error);
        }
    }
    
    /**
     * إعداد النسخ الاحتياطي التلقائي
     */
    async setupAutoBackup() {
        if (!this.backupConfig.autoBackup) return;
        
        // التحقق من آخر نسخة احتياطية
        const lastBackup = await this.getLastBackup();
        const now = Date.now();
        
        if (!lastBackup || (now - new Date(lastBackup.timestamp).getTime()) >= this.backupConfig.backupInterval) {
            await this.createAutoBackup();
        }
        
        // جدولة النسخ الاحتياطي التلقائي
        setInterval(async () => {
            await this.createAutoBackup();
        }, this.backupConfig.backupInterval);
        
        console.log('⏰ تم إعداد النسخ الاحتياطي التلقائي');
    }
    
    /**
     * تهيئة نظام تسجيل الأنشطة
     */
    async initializeActivityLogging() {
        // تسجيل بدء تشغيل النظام
        await this.logActivity({
            action: 'system_startup',
            category: 'system',
            description: 'تم تشغيل نظام التخزين الدائم',
            severity: 'info',
            metadata: {
                dbVersion: this.dbVersion,
                timestamp: new Date().toISOString()
            }
        });
        
        console.log('📝 تم تهيئة نظام تسجيل الأنشطة');
    }
    
    /**
     * إرسال حدث مخصص
     */
    dispatchEvent(eventName, data = null) {
        const event = new CustomEvent(eventName, { detail: data });
        window.dispatchEvent(event);
    }
    
    /**
     * التحقق من جاهزية النظام
     */
    isReady() {
        return this.isInitialized && this.db !== null;
    }
    
    /**
     * الحصول على معلومات النظام
     */
    getSystemInfo() {
        return {
            dbName: this.dbName,
            dbVersion: this.dbVersion,
            isInitialized: this.isInitialized,
            stores: Object.keys(this.stores),
            backupConfig: this.backupConfig,
            timestamp: new Date().toISOString()
        };
    }

    // ==================== وظائف حفظ البيانات ====================

    /**
     * حفظ مستخدم
     */
    async saveUser(userData) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.users], 'readwrite');
            const store = transaction.objectStore(this.stores.users);

            // إضافة معلومات إضافية
            const userToSave = {
                ...userData,
                updatedAt: new Date().toISOString(),
                version: 1
            };

            await store.put(userToSave);

            // تسجيل النشاط
            await this.logActivity({
                action: 'user_saved',
                category: 'user_management',
                description: `تم حفظ بيانات المستخدم: ${userData.username}`,
                userId: userData.username,
                severity: 'info'
            });

            return userToSave;

        } catch (error) {
            console.error('خطأ في حفظ المستخدم:', error);
            throw error;
        }
    }

    /**
     * حفظ حدث أمني
     */
    async saveEvent(eventData) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.events], 'readwrite');
            const store = transaction.objectStore(this.stores.events);

            // إضافة معلومات إضافية
            const eventToSave = {
                ...eventData,
                savedAt: new Date().toISOString(),
                version: 1,
                checksum: this.calculateChecksum(eventData)
            };

            const result = await store.add(eventToSave);

            // تسجيل النشاط
            await this.logActivity({
                action: 'event_saved',
                category: 'security_events',
                description: `تم حفظ حدث أمني: ${eventData.title}`,
                userId: eventData.createdBy,
                severity: eventData.severity || 'info',
                metadata: {
                    eventId: result,
                    serialNumber: eventData.serialNumber
                }
            });

            return result;

        } catch (error) {
            console.error('خطأ في حفظ الحدث:', error);
            throw error;
        }
    }

    /**
     * حفظ مراجعة ما بعد الحادث
     */
    async saveReview(reviewData) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.reviews], 'readwrite');
            const store = transaction.objectStore(this.stores.reviews);

            const reviewToSave = {
                ...reviewData,
                savedAt: new Date().toISOString(),
                version: 1
            };

            const result = await store.add(reviewToSave);

            await this.logActivity({
                action: 'review_saved',
                category: 'post_incident_review',
                description: `تم حفظ مراجعة ما بعد الحادث: ${reviewData.incidentRecordNumber}`,
                userId: reviewData.createdBy,
                severity: 'info'
            });

            return result;

        } catch (error) {
            console.error('خطأ في حفظ المراجعة:', error);
            throw error;
        }
    }

    /**
     * تسجيل نشاط
     */
    async logActivity(activityData) {
        if (!this.isReady()) return; // لا نرمي خطأ هنا لتجنب التكرار اللانهائي

        try {
            const transaction = this.db.transaction([this.stores.activities], 'readwrite');
            const store = transaction.objectStore(this.stores.activities);

            const activity = {
                ...activityData,
                timestamp: new Date().toISOString(),
                sessionId: this.getSessionId(),
                userAgent: navigator.userAgent,
                ipAddress: await this.getClientIP(),
                id: undefined // سيتم إنشاؤه تلقائياً
            };

            await store.add(activity);

        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    /**
     * حفظ إعداد
     */
    async saveSetting(key, value) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.settings], 'readwrite');
            const store = transaction.objectStore(this.stores.settings);

            const setting = {
                key: key,
                value: value,
                updatedAt: new Date().toISOString(),
                category: this.getSettingCategory(key)
            };

            await store.put(setting);

            return setting;

        } catch (error) {
            console.error('خطأ في حفظ الإعداد:', error);
            throw error;
        }
    }

    // ==================== وظائف استرجاع البيانات ====================

    /**
     * الحصول على جميع المستخدمين
     */
    async getAllUsers() {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.users], 'readonly');
            const store = transaction.objectStore(this.stores.users);

            return new Promise((resolve, reject) => {
                const request = store.getAll();
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });

        } catch (error) {
            console.error('خطأ في استرجاع المستخدمين:', error);
            throw error;
        }
    }

    /**
     * الحصول على مستخدم محدد
     */
    async getUser(username) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.users], 'readonly');
            const store = transaction.objectStore(this.stores.users);

            return new Promise((resolve, reject) => {
                const request = store.get(username);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });

        } catch (error) {
            console.error('خطأ في استرجاع المستخدم:', error);
            throw error;
        }
    }

    /**
     * الحصول على جميع الأحداث الأمنية
     */
    async getAllEvents(filters = {}) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const transaction = this.db.transaction([this.stores.events], 'readonly');
            const store = transaction.objectStore(this.stores.events);

            let events = await new Promise((resolve, reject) => {
                const request = store.getAll();
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });

            // تطبيق الفلاتر
            if (filters.severity) {
                events = events.filter(event => event.severity === filters.severity);
            }

            if (filters.status) {
                events = events.filter(event => event.status === filters.status);
            }

            if (filters.dateFrom) {
                events = events.filter(event => new Date(event.dateTime) >= new Date(filters.dateFrom));
            }

            if (filters.dateTo) {
                events = events.filter(event => new Date(event.dateTime) <= new Date(filters.dateTo));
            }

            return events.sort((a, b) => new Date(b.dateTime) - new Date(a.dateTime));

        } catch (error) {
            console.error('خطأ في استرجاع الأحداث:', error);
            throw error;
        }
    }

    // ==================== وظائف مساعدة ====================

    /**
     * حساب checksum للبيانات
     */
    calculateChecksum(data) {
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString(16);
    }

    /**
     * الحصول على معرف الجلسة
     */
    getSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }

    /**
     * الحصول على IP العميل
     */
    async getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * تحديد فئة الإعداد
     */
    getSettingCategory(key) {
        if (key.startsWith('backup_')) return 'backup';
        if (key.startsWith('security_')) return 'security';
        if (key.startsWith('sync_')) return 'synchronization';
        if (key.startsWith('ui_')) return 'interface';
        return 'general';
    }

    // ==================== نظام النسخ الاحتياطي ====================

    /**
     * إنشاء نسخة احتياطية شاملة
     */
    async createFullBackup(options = {}) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            console.log('🔄 بدء إنشاء النسخة الاحتياطية الشاملة...');

            const backupData = {
                metadata: {
                    version: this.dbVersion,
                    timestamp: new Date().toISOString(),
                    type: options.type || 'full',
                    creator: options.creator || 'system',
                    description: options.description || 'نسخة احتياطية شاملة تلقائية'
                },
                data: {}
            };

            // نسخ جميع البيانات
            for (const [key, storeName] of Object.entries(this.stores)) {
                backupData.data[key] = await this.getAllFromStore(storeName);
                console.log(`✅ تم نسخ ${backupData.data[key].length} عنصر من ${key}`);
            }

            // ضغط البيانات إذا كان مفعلاً
            let finalData = backupData;
            if (this.backupConfig.compressionEnabled) {
                finalData = await this.compressData(backupData);
                console.log('🗜️ تم ضغط البيانات');
            }

            // حفظ معلومات النسخة الاحتياطية
            const backupInfo = {
                timestamp: backupData.metadata.timestamp,
                type: backupData.metadata.type,
                size: JSON.stringify(finalData).length,
                status: 'completed',
                itemsCount: Object.values(backupData.data).reduce((sum, arr) => sum + arr.length, 0),
                compressed: this.backupConfig.compressionEnabled,
                checksum: this.calculateChecksum(finalData)
            };

            await this.saveBackupInfo(backupInfo);

            // تنظيف النسخ القديمة
            await this.cleanupOldBackups();

            console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');

            return {
                success: true,
                data: finalData,
                info: backupInfo
            };

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);

            await this.logActivity({
                action: 'backup_failed',
                category: 'backup',
                description: `فشل في إنشاء النسخة الاحتياطية: ${error.message}`,
                severity: 'error'
            });

            throw error;
        }
    }

    /**
     * إنشاء نسخة احتياطية تلقائية
     */
    async createAutoBackup() {
        try {
            const result = await this.createFullBackup({
                type: 'auto',
                creator: 'system',
                description: 'نسخة احتياطية تلقائية مجدولة'
            });

            await this.logActivity({
                action: 'auto_backup_created',
                category: 'backup',
                description: 'تم إنشاء نسخة احتياطية تلقائية',
                severity: 'info',
                metadata: {
                    size: result.info.size,
                    itemsCount: result.info.itemsCount
                }
            });

            return result;

        } catch (error) {
            console.error('خطأ في النسخة الاحتياطية التلقائية:', error);
        }
    }

    /**
     * استعادة من نسخة احتياطية
     */
    async restoreFromBackup(backupData, options = {}) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            console.log('🔄 بدء استعادة البيانات من النسخة الاحتياطية...');

            // التحقق من صحة البيانات
            if (!backupData.metadata || !backupData.data) {
                throw new Error('تنسيق النسخة الاحتياطية غير صحيح');
            }

            // إلغاء ضغط البيانات إذا لزم الأمر
            let dataToRestore = backupData;
            if (backupData.compressed) {
                dataToRestore = await this.decompressData(backupData);
                console.log('📦 تم إلغاء ضغط البيانات');
            }

            // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            if (!options.skipCurrentBackup) {
                await this.createFullBackup({
                    type: 'pre_restore',
                    description: 'نسخة احتياطية قبل الاستعادة'
                });
            }

            // مسح البيانات الحالية واستعادة البيانات الجديدة
            for (const [key, data] of Object.entries(dataToRestore.data)) {
                if (this.stores[key]) {
                    await this.clearStore(this.stores[key]);
                    await this.bulkInsert(this.stores[key], data);
                    console.log(`✅ تم استعادة ${data.length} عنصر في ${key}`);
                }
            }

            await this.logActivity({
                action: 'backup_restored',
                category: 'backup',
                description: 'تم استعادة البيانات من النسخة الاحتياطية',
                severity: 'info',
                metadata: {
                    backupTimestamp: dataToRestore.metadata.timestamp,
                    restoredAt: new Date().toISOString()
                }
            });

            console.log('✅ تم استعادة البيانات بنجاح');

            return {
                success: true,
                restoredItems: Object.values(dataToRestore.data).reduce((sum, arr) => sum + arr.length, 0),
                backupInfo: dataToRestore.metadata
            };

        } catch (error) {
            console.error('❌ خطأ في استعادة البيانات:', error);
            throw error;
        }
    }

    /**
     * تصدير البيانات بصيغ متعددة
     */
    async exportData(format = 'json', filters = {}) {
        if (!this.isReady()) throw new Error('نظام التخزين غير جاهز');

        try {
            const exportData = await this.createFullBackup({
                type: 'export',
                description: 'تصدير البيانات'
            });

            let exportedData;
            let filename;
            let mimeType;

            switch (format.toLowerCase()) {
                case 'json':
                    exportedData = JSON.stringify(exportData.data, null, 2);
                    filename = `ISMS_Export_${new Date().toISOString().split('T')[0]}.json`;
                    mimeType = 'application/json';
                    break;

                case 'csv':
                    exportedData = await this.convertToCSV(exportData.data);
                    filename = `ISMS_Export_${new Date().toISOString().split('T')[0]}.csv`;
                    mimeType = 'text/csv';
                    break;

                case 'xml':
                    exportedData = await this.convertToXML(exportData.data);
                    filename = `ISMS_Export_${new Date().toISOString().split('T')[0]}.xml`;
                    mimeType = 'application/xml';
                    break;

                default:
                    throw new Error(`تنسيق التصدير غير مدعوم: ${format}`);
            }

            // إنشاء وتحميل الملف
            const blob = new Blob([exportedData], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            await this.logActivity({
                action: 'data_exported',
                category: 'export',
                description: `تم تصدير البيانات بصيغة ${format}`,
                severity: 'info',
                metadata: {
                    format: format,
                    filename: filename,
                    size: blob.size
                }
            });

            return {
                success: true,
                filename: filename,
                size: blob.size,
                format: format
            };

        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            throw error;
        }
    }

    // ==================== وظائف مساعدة للنسخ الاحتياطي ====================

    /**
     * الحصول على جميع البيانات من متجر معين
     */
    async getAllFromStore(storeName) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * مسح متجر
     */
    async clearStore(storeName) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);

        return new Promise((resolve, reject) => {
            const request = store.clear();
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * إدراج مجمع للبيانات
     */
    async bulkInsert(storeName, data) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);

        for (const item of data) {
            await store.add(item);
        }
    }

    /**
     * حفظ معلومات النسخة الاحتياطية
     */
    async saveBackupInfo(backupInfo) {
        const transaction = this.db.transaction([this.stores.backups], 'readwrite');
        const store = transaction.objectStore(this.stores.backups);
        await store.add(backupInfo);
    }

    /**
     * الحصول على آخر نسخة احتياطية
     */
    async getLastBackup() {
        const transaction = this.db.transaction([this.stores.backups], 'readonly');
        const store = transaction.objectStore(this.stores.backups);
        const index = store.index('timestamp');

        return new Promise((resolve, reject) => {
            const request = index.openCursor(null, 'prev');
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                resolve(cursor ? cursor.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    async cleanupOldBackups() {
        const transaction = this.db.transaction([this.stores.backups], 'readwrite');
        const store = transaction.objectStore(this.stores.backups);
        const index = store.index('timestamp');

        const allBackups = await new Promise((resolve, reject) => {
            const request = index.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });

        if (allBackups.length > this.backupConfig.maxBackups) {
            // ترتيب حسب التاريخ (الأحدث أولاً)
            allBackups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // حذف النسخ الزائدة
            const backupsToDelete = allBackups.slice(this.backupConfig.maxBackups);

            for (const backup of backupsToDelete) {
                await store.delete(backup.id);
            }

            console.log(`🗑️ تم حذف ${backupsToDelete.length} نسخة احتياطية قديمة`);
        }
    }

    /**
     * ضغط البيانات
     */
    async compressData(data) {
        // تنفيذ بسيط للضغط - يمكن تحسينه باستخدام مكتبات ضغط متقدمة
        const jsonString = JSON.stringify(data);
        const compressed = btoa(jsonString);

        return {
            compressed: true,
            data: compressed,
            originalSize: jsonString.length,
            compressedSize: compressed.length
        };
    }

    /**
     * إلغاء ضغط البيانات
     */
    async decompressData(compressedData) {
        if (!compressedData.compressed) return compressedData;

        const decompressed = atob(compressedData.data);
        return JSON.parse(decompressed);
    }
}

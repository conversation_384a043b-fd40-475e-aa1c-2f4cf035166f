/**
 * نظام التشفير والأمان المتقدم لنظام إدارة أمن المعلومات
 * Advanced Encryption & Security System for ISMS
 * 
 * يوفر هذا النظام:
 * - تشفير البيانات الحساسة
 * - إدارة مفاتيح التشفير
 * - التوقيع الرقمي
 * - التحقق من سلامة البيانات
 * - حماية النقل والتخزين
 */

class EncryptionSecuritySystem {
    constructor() {
        this.keyStore = new Map();
        this.encryptionAlgorithm = 'AES-GCM';
        this.keyLength = 256;
        this.ivLength = 12;
        this.tagLength = 128;
        
        // إعدادات الأمان
        this.securityConfig = {
            enableEncryption: true,
            encryptSensitiveData: true,
            enableDigitalSignature: true,
            enableIntegrityCheck: true,
            keyRotationInterval: 30 * 24 * 60 * 60 * 1000, // 30 يوم
            maxKeyAge: 90 * 24 * 60 * 60 * 1000, // 90 يوم
            enableAuditLog: true
        };
        
        // قائمة البيانات الحساسة
        this.sensitiveFields = [
            'password',
            'apiKey',
            'token',
            'personalInfo',
            'financialData',
            'medicalInfo',
            'socialSecurityNumber',
            'creditCardNumber'
        ];
        
        this.init();
    }
    
    /**
     * تهيئة نظام التشفير
     */
    async init() {
        try {
            // التحقق من دعم Web Crypto API
            if (!window.crypto || !window.crypto.subtle) {
                throw new Error('Web Crypto API غير مدعوم في هذا المتصفح');
            }
            
            // تحميل المفاتيح المحفوظة
            await this.loadStoredKeys();
            
            // إنشاء مفتاح رئيسي إذا لم يكن موجوداً
            if (!this.keyStore.has('master')) {
                await this.generateMasterKey();
            }
            
            // إعداد دوران المفاتيح التلقائي
            this.setupKeyRotation();
            
            console.log('🔐 تم تهيئة نظام التشفير والأمان');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التشفير:', error);
            throw error;
        }
    }
    
    /**
     * إنشاء مفتاح رئيسي
     */
    async generateMasterKey() {
        try {
            const key = await window.crypto.subtle.generateKey(
                {
                    name: this.encryptionAlgorithm,
                    length: this.keyLength
                },
                true, // قابل للاستخراج
                ['encrypt', 'decrypt']
            );
            
            const keyData = {
                key: key,
                id: 'master',
                createdAt: new Date().toISOString(),
                algorithm: this.encryptionAlgorithm,
                usage: ['encrypt', 'decrypt'],
                isActive: true
            };
            
            this.keyStore.set('master', keyData);
            await this.saveKeyMetadata('master', keyData);
            
            console.log('🔑 تم إنشاء المفتاح الرئيسي');
            
            return keyData;
            
        } catch (error) {
            console.error('خطأ في إنشاء المفتاح الرئيسي:', error);
            throw error;
        }
    }
    
    /**
     * تشفير البيانات
     */
    async encryptData(data, keyId = 'master') {
        if (!this.securityConfig.enableEncryption) {
            return { encrypted: false, data: data };
        }
        
        try {
            const keyData = this.keyStore.get(keyId);
            if (!keyData) {
                throw new Error(`مفتاح التشفير غير موجود: ${keyId}`);
            }
            
            // تحويل البيانات إلى JSON string
            const plaintext = typeof data === 'string' ? data : JSON.stringify(data);
            const encoder = new TextEncoder();
            const plaintextBuffer = encoder.encode(plaintext);
            
            // إنشاء IV عشوائي
            const iv = window.crypto.getRandomValues(new Uint8Array(this.ivLength));
            
            // تشفير البيانات
            const encryptedBuffer = await window.crypto.subtle.encrypt(
                {
                    name: this.encryptionAlgorithm,
                    iv: iv,
                    tagLength: this.tagLength
                },
                keyData.key,
                plaintextBuffer
            );
            
            // تحويل إلى Base64 للتخزين
            const encryptedArray = new Uint8Array(encryptedBuffer);
            const encryptedBase64 = btoa(String.fromCharCode(...encryptedArray));
            const ivBase64 = btoa(String.fromCharCode(...iv));
            
            // حساب checksum للتحقق من السلامة
            const checksum = await this.calculateChecksum(plaintext);
            
            const result = {
                encrypted: true,
                data: encryptedBase64,
                iv: ivBase64,
                keyId: keyId,
                algorithm: this.encryptionAlgorithm,
                timestamp: new Date().toISOString(),
                checksum: checksum
            };
            
            // تسجيل عملية التشفير
            if (this.securityConfig.enableAuditLog) {
                await this.logSecurityEvent('data_encrypted', {
                    keyId: keyId,
                    dataSize: plaintext.length,
                    algorithm: this.encryptionAlgorithm
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('خطأ في تشفير البيانات:', error);
            throw error;
        }
    }
    
    /**
     * فك تشفير البيانات
     */
    async decryptData(encryptedData) {
        if (!encryptedData.encrypted) {
            return encryptedData.data;
        }
        
        try {
            const keyData = this.keyStore.get(encryptedData.keyId);
            if (!keyData) {
                throw new Error(`مفتاح فك التشفير غير موجود: ${encryptedData.keyId}`);
            }
            
            // تحويل من Base64
            const encryptedBuffer = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));
            
            // فك التشفير
            const decryptedBuffer = await window.crypto.subtle.decrypt(
                {
                    name: this.encryptionAlgorithm,
                    iv: iv,
                    tagLength: this.tagLength
                },
                keyData.key,
                encryptedBuffer
            );
            
            // تحويل إلى نص
            const decoder = new TextDecoder();
            const plaintext = decoder.decode(decryptedBuffer);
            
            // التحقق من سلامة البيانات
            if (encryptedData.checksum) {
                const calculatedChecksum = await this.calculateChecksum(plaintext);
                if (calculatedChecksum !== encryptedData.checksum) {
                    throw new Error('فشل في التحقق من سلامة البيانات');
                }
            }
            
            // محاولة تحويل إلى JSON إذا أمكن
            try {
                return JSON.parse(plaintext);
            } catch {
                return plaintext;
            }
            
        } catch (error) {
            console.error('خطأ في فك تشفير البيانات:', error);
            throw error;
        }
    }
    
    /**
     * تشفير البيانات الحساسة في كائن
     */
    async encryptSensitiveFields(obj, keyId = 'master') {
        if (!this.securityConfig.encryptSensitiveData) {
            return obj;
        }
        
        const result = { ...obj };
        
        for (const [key, value] of Object.entries(result)) {
            if (this.isSensitiveField(key) && value) {
                result[key] = await this.encryptData(value, keyId);
            } else if (typeof value === 'object' && value !== null) {
                result[key] = await this.encryptSensitiveFields(value, keyId);
            }
        }
        
        return result;
    }
    
    /**
     * فك تشفير البيانات الحساسة في كائن
     */
    async decryptSensitiveFields(obj) {
        if (!obj) return obj;
        
        const result = { ...obj };
        
        for (const [key, value] of Object.entries(result)) {
            if (value && typeof value === 'object' && value.encrypted) {
                result[key] = await this.decryptData(value);
            } else if (typeof value === 'object' && value !== null && !value.encrypted) {
                result[key] = await this.decryptSensitiveFields(value);
            }
        }
        
        return result;
    }
    
    /**
     * إنشاء توقيع رقمي
     */
    async createDigitalSignature(data, keyId = 'master') {
        if (!this.securityConfig.enableDigitalSignature) {
            return null;
        }
        
        try {
            // إنشاء مفتاح للتوقيع إذا لم يكن موجوداً
            let signingKey = this.keyStore.get(`${keyId}_signing`);
            if (!signingKey) {
                signingKey = await this.generateSigningKey(keyId);
            }
            
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(typeof data === 'string' ? data : JSON.stringify(data));
            
            const signature = await window.crypto.subtle.sign(
                'RSASSA-PKCS1-v1_5',
                signingKey.privateKey,
                dataBuffer
            );
            
            const signatureBase64 = btoa(String.fromCharCode(...new Uint8Array(signature)));
            
            return {
                signature: signatureBase64,
                keyId: `${keyId}_signing`,
                algorithm: 'RSASSA-PKCS1-v1_5',
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('خطأ في إنشاء التوقيع الرقمي:', error);
            throw error;
        }
    }
    
    /**
     * التحقق من التوقيع الرقمي
     */
    async verifyDigitalSignature(data, signatureData) {
        if (!signatureData || !this.securityConfig.enableDigitalSignature) {
            return true; // إذا لم يكن التوقيع مفعلاً
        }
        
        try {
            const signingKey = this.keyStore.get(signatureData.keyId);
            if (!signingKey) {
                throw new Error(`مفتاح التوقيع غير موجود: ${signatureData.keyId}`);
            }
            
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(typeof data === 'string' ? data : JSON.stringify(data));
            const signatureBuffer = Uint8Array.from(atob(signatureData.signature), c => c.charCodeAt(0));
            
            const isValid = await window.crypto.subtle.verify(
                'RSASSA-PKCS1-v1_5',
                signingKey.publicKey,
                signatureBuffer,
                dataBuffer
            );
            
            if (this.securityConfig.enableAuditLog) {
                await this.logSecurityEvent('signature_verified', {
                    keyId: signatureData.keyId,
                    isValid: isValid,
                    timestamp: signatureData.timestamp
                });
            }
            
            return isValid;
            
        } catch (error) {
            console.error('خطأ في التحقق من التوقيع الرقمي:', error);
            return false;
        }
    }
    
    /**
     * حساب checksum للبيانات
     */
    async calculateChecksum(data) {
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(typeof data === 'string' ? data : JSON.stringify(data));
        
        const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataBuffer);
        const hashArray = new Uint8Array(hashBuffer);
        
        return btoa(String.fromCharCode(...hashArray));
    }
    
    /**
     * التحقق من كون الحقل حساساً
     */
    isSensitiveField(fieldName) {
        return this.sensitiveFields.some(sensitive => 
            fieldName.toLowerCase().includes(sensitive.toLowerCase())
        );
    }
    
    /**
     * إنشاء مفتاح للتوقيع الرقمي
     */
    async generateSigningKey(baseKeyId) {
        try {
            const keyPair = await window.crypto.subtle.generateKey(
                {
                    name: 'RSASSA-PKCS1-v1_5',
                    modulusLength: 2048,
                    publicExponent: new Uint8Array([1, 0, 1]),
                    hash: 'SHA-256'
                },
                true,
                ['sign', 'verify']
            );
            
            const keyData = {
                privateKey: keyPair.privateKey,
                publicKey: keyPair.publicKey,
                id: `${baseKeyId}_signing`,
                createdAt: new Date().toISOString(),
                algorithm: 'RSASSA-PKCS1-v1_5',
                usage: ['sign', 'verify'],
                isActive: true
            };
            
            this.keyStore.set(`${baseKeyId}_signing`, keyData);
            await this.saveKeyMetadata(`${baseKeyId}_signing`, keyData);
            
            console.log(`🔑 تم إنشاء مفتاح التوقيع: ${baseKeyId}_signing`);
            
            return keyData;
            
        } catch (error) {
            console.error('خطأ في إنشاء مفتاح التوقيع:', error);
            throw error;
        }
    }
    
    /**
     * حفظ metadata المفتاح
     */
    async saveKeyMetadata(keyId, keyData) {
        const metadata = {
            id: keyData.id,
            createdAt: keyData.createdAt,
            algorithm: keyData.algorithm,
            usage: keyData.usage,
            isActive: keyData.isActive
        };
        
        localStorage.setItem(`key_metadata_${keyId}`, JSON.stringify(metadata));
    }
    
    /**
     * تحميل المفاتيح المحفوظة
     */
    async loadStoredKeys() {
        // تحميل metadata المفاتيح من localStorage
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('key_metadata_')) {
                const keyId = key.replace('key_metadata_', '');
                const metadata = JSON.parse(localStorage.getItem(key));
                
                // هنا يمكن إضافة منطق لاستعادة المفاتيح الفعلية
                // في التطبيق الحقيقي، قد نحتاج لطلب كلمة مرور من المستخدم
                console.log(`📋 تم العثور على metadata للمفتاح: ${keyId}`);
            }
        }
    }
    
    /**
     * إعداد دوران المفاتيح التلقائي
     */
    setupKeyRotation() {
        setInterval(async () => {
            try {
                await this.rotateKeys();
            } catch (error) {
                console.error('خطأ في دوران المفاتيح:', error);
            }
        }, this.securityConfig.keyRotationInterval);
        
        console.log('🔄 تم إعداد دوران المفاتيح التلقائي');
    }
    
    /**
     * دوران المفاتيح
     */
    async rotateKeys() {
        const now = Date.now();
        
        for (const [keyId, keyData] of this.keyStore.entries()) {
            const keyAge = now - new Date(keyData.createdAt).getTime();
            
            if (keyAge >= this.securityConfig.keyRotationInterval) {
                console.log(`🔄 دوران المفتاح: ${keyId}`);
                
                // إنشاء مفتاح جديد
                if (keyId === 'master') {
                    await this.generateMasterKey();
                }
                
                // تسجيل عملية الدوران
                if (this.securityConfig.enableAuditLog) {
                    await this.logSecurityEvent('key_rotated', {
                        keyId: keyId,
                        oldKeyAge: keyAge,
                        newKeyCreated: new Date().toISOString()
                    });
                }
            }
        }
    }
    
    /**
     * تسجيل حدث أمني
     */
    async logSecurityEvent(eventType, details) {
        const securityEvent = {
            type: eventType,
            timestamp: new Date().toISOString(),
            details: details,
            userAgent: navigator.userAgent,
            sessionId: sessionStorage.getItem('sessionId') || 'unknown'
        };
        
        // حفظ في localStorage مؤقتاً
        const securityLog = JSON.parse(localStorage.getItem('securityLog') || '[]');
        securityLog.push(securityEvent);
        
        // الاحتفاظ بآخر 1000 حدث فقط
        if (securityLog.length > 1000) {
            securityLog.splice(0, securityLog.length - 1000);
        }
        
        localStorage.setItem('securityLog', JSON.stringify(securityLog));
        
        console.log(`🔒 حدث أمني: ${eventType}`, details);
    }
    
    /**
     * الحصول على إحصائيات الأمان
     */
    getSecurityStats() {
        const securityLog = JSON.parse(localStorage.getItem('securityLog') || '[]');
        
        return {
            totalKeys: this.keyStore.size,
            activeKeys: Array.from(this.keyStore.values()).filter(k => k.isActive).length,
            securityEvents: securityLog.length,
            encryptionEnabled: this.securityConfig.enableEncryption,
            lastKeyRotation: this.getLastKeyRotation(),
            securityLevel: this.calculateSecurityLevel()
        };
    }
    
    /**
     * حساب مستوى الأمان
     */
    calculateSecurityLevel() {
        let score = 0;
        
        if (this.securityConfig.enableEncryption) score += 25;
        if (this.securityConfig.enableDigitalSignature) score += 25;
        if (this.securityConfig.enableIntegrityCheck) score += 25;
        if (this.keyStore.size > 0) score += 25;
        
        if (score >= 90) return 'ممتاز';
        if (score >= 70) return 'جيد';
        if (score >= 50) return 'متوسط';
        return 'ضعيف';
    }
    
    /**
     * الحصول على تاريخ آخر دوران للمفاتيح
     */
    getLastKeyRotation() {
        const securityLog = JSON.parse(localStorage.getItem('securityLog') || '[]');
        const rotationEvents = securityLog.filter(event => event.type === 'key_rotated');
        
        if (rotationEvents.length > 0) {
            return rotationEvents[rotationEvents.length - 1].timestamp;
        }
        
        return null;
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم شبكة محسن لنظام إدارة أمن المعلومات
Enhanced Network Server for ISMS
"""

import os
import sys
import json
import socket
import threading
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime

class NetworkHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """معالج طلبات HTTP محسن للشبكة"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)

    def end_headers(self):
        """إضافة headers للسماح بالوصول من الشبكة"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        """معالجة طلبات GET"""
        # تسجيل الطلب
        client_ip = self.client_address[0]
        self.log_request(client_ip)

        # معالجة الطلب
        super().do_GET()

    def do_POST(self):
        """معالجة طلبات POST"""
        client_ip = self.client_address[0]
        self.log_request(client_ip)

        # قراءة البيانات
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        # معالجة طلبات API
        if self.path.startswith('/api/'):
            self.handle_api_request(post_data)
        else:
            super().do_POST()

    def handle_api_request(self, data):
        """معالجة طلبات API"""
        try:
            # تحليل البيانات
            if data:
                json_data = json.loads(data.decode('utf-8'))
            else:
                json_data = {}

            # معالجة حسب نوع الطلب
            if self.path == '/api/login':
                response = self.handle_login(json_data)
            elif self.path == '/api/sync':
                response = self.handle_sync(json_data)
            else:
                response = {'error': 'Unknown API endpoint'}

            # إرسال الاستجابة
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            # إرسال خطأ
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {'error': str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def handle_login(self, data):
        """معالجة طلب تسجيل الدخول"""
        username = data.get('username', '')
        password = data.get('password', '')

        # تحميل المستخدمين
        try:
            with open('data/users.json', 'r', encoding='utf-8') as f:
                users = json.load(f)
        except FileNotFoundError:
            return {'error': 'Users data not found'}

        # التحقق من المستخدم
        user = users.get(username.lower())
        if not user:
            return {'error': 'Invalid credentials'}

        # التحقق من كلمة المرور (تشفير بسيط)
        def hash_password(password):
            hash_val = 0
            for char in password:
                hash_val = ((hash_val << 5) - hash_val) + ord(char)
                hash_val = hash_val & 0xFFFFFFFF
                if hash_val > 0x7FFFFFFF:
                    hash_val -= 0x100000000
            return str(hash_val)

        if user['password'] != hash_password(password):
            return {'error': 'Invalid credentials'}

        # إنشاء جلسة
        session_id = f"session_{datetime.now().timestamp()}_{hash(username)}"

        return {
            'success': True,
            'user': {
                'username': user['username'],
                'fullName': user['fullName'],
                'role': user['role'],
                'permissions': user['permissions']
            },
            'sessionId': session_id
        }

    def handle_sync(self, data):
        """معالجة طلب المزامنة"""
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }

    def log_request(self, client_ip):
        """تسجيل الطلبات"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {client_ip} - {self.command} {self.path}"
        print(log_entry)

        # حفظ في ملف السجل
        try:
            with open('network_access.log', 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except Exception:
            pass

class NetworkServer:
    """خادم الشبكة الرئيسي"""

    def __init__(self, host='0.0.0.0', port=8000):
        self.host = host
        self.port = port
        self.server = None
        self.running = False

    def start(self):
        """بدء الخادم"""
        try:
            # إنشاء الخادم
            self.server = socketserver.TCPServer((self.host, self.port), NetworkHTTPRequestHandler)
            self.server.allow_reuse_address = True

            # بدء الخادم في thread منفصل
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()

            self.running = True

            # الحصول على IP المحلي
            local_ip = self.get_local_ip()

            print(f"🌐 خادم الشبكة يعمل على:")
            print(f"   • المحلي: http://localhost:{self.port}")
            print(f"   • الشبكة: http://{local_ip}:{self.port}")
            print(f"   • جميع الواجهات: http://{self.host}:{self.port}")

            return True

        except Exception as e:
            print(f"❌ خطأ في بدء الخادم: {e}")
            return False

    def stop(self):
        """إيقاف الخادم"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            print("✅ تم إيقاف خادم الشبكة")

    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"

def main():
    """الوظيفة الرئيسية"""
    print("🌐 بدء خادم الشبكة المحسن...")

    # إنشاء وبدء الخادم
    server = NetworkServer()

    if server.start():
        print("✅ تم بدء الخادم بنجاح")
        print("💡 يمكن للأجهزة الأخرى في الشبكة الوصول للنظام الآن")
        print("🔧 اضغط Ctrl+C لإيقاف الخادم")

        try:
            # انتظار إيقاف الخادم
            while server.running:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 إيقاف الخادم...")
            server.stop()
    else:
        print("❌ فشل في بدء الخادم")
        sys.exit(1)

if __name__ == "__main__":
    main()

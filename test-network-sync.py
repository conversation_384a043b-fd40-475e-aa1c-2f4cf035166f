#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لمزامنة الشبكة
Comprehensive Network Sync Test
"""

import requests
import json
import time
import uuid
import socket
import threading
from datetime import datetime

class NetworkSyncTester:
    def __init__(self):
        self.local_ip = self.get_local_ip()
        self.test_results = []
        self.servers_to_test = []
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def print_header(self):
        """طباعة رأس الاختبار"""
        print("=" * 70)
        print("🌐 اختبار شامل لمزامنة الشبكة")
        print("   Comprehensive Network Sync Test")
        print("=" * 70)
        print(f"🏠 عنوان IP المحلي: {self.local_ip}")
        print()
    
    def discover_servers(self):
        """اكتشاف خوادم المزامنة في الشبكة"""
        print("🔍 البحث عن خوادم المزامنة في الشبكة...")
        
        # قائمة الخوادم المحتملة
        potential_servers = [
            f"http://localhost:8001",
            f"http://127.0.0.1:8001",
            f"http://{self.local_ip}:8001"
        ]
        
        # إضافة عناوين IP أخرى في نفس الشبكة
        ip_parts = self.local_ip.split('.')
        if len(ip_parts) == 4:
            base_ip = '.'.join(ip_parts[:3])
            # اختبار بعض العناوين في نفس الشبكة
            for i in [1, 100, 101, 102, 200, 254]:
                potential_servers.append(f"http://{base_ip}.{i}:8001")
        
        active_servers = []
        
        def test_server(server_url):
            try:
                response = requests.get(f"{server_url}/api/sync/status", timeout=2)
                if response.status_code == 200:
                    active_servers.append(server_url)
                    print(f"   ✅ وُجد خادم: {server_url}")
            except:
                pass
        
        # اختبار الخوادم بشكل متوازي
        threads = []
        for server in potential_servers:
            thread = threading.Thread(target=test_server, args=(server,))
            thread.start()
            threads.append(thread)
        
        # انتظار انتهاء جميع الاختبارات
        for thread in threads:
            thread.join()
        
        self.servers_to_test = active_servers
        
        if active_servers:
            print(f"🎉 تم العثور على {len(active_servers)} خادم مزامنة")
        else:
            print("❌ لم يتم العثور على أي خادم مزامنة")
        
        print()
        return active_servers
    
    def test_server_connectivity(self, server_url):
        """اختبار الاتصال بخادم محدد"""
        print(f"🌐 اختبار الاتصال بـ {server_url}...")
        
        try:
            # اختبار حالة الخادم
            response = requests.get(f"{server_url}/api/sync/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"   ✅ الخادم متصل")
                print(f"   📊 المستخدمين: {status.get('users_count', 0)}")
                print(f"   📊 الأحداث: {status.get('events_count', 0)}")
                print(f"   📊 الأنشطة: {status.get('activities_count', 0)}")
                
                self.test_results.append({
                    'server': server_url,
                    'test': 'connectivity',
                    'success': True,
                    'data': status
                })
                return True
            else:
                print(f"   ❌ خطأ في الاستجابة: {response.status_code}")
                self.test_results.append({
                    'server': server_url,
                    'test': 'connectivity',
                    'success': False,
                    'error': f"HTTP {response.status_code}"
                })
                return False
        except requests.exceptions.Timeout:
            print(f"   ❌ انتهت مهلة الاتصال")
            self.test_results.append({
                'server': server_url,
                'test': 'connectivity',
                'success': False,
                'error': "Timeout"
            })
            return False
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            self.test_results.append({
                'server': server_url,
                'test': 'connectivity',
                'success': False,
                'error': str(e)
            })
            return False
    
    def test_data_sync(self, server_url):
        """اختبار مزامنة البيانات"""
        print(f"🔄 اختبار مزامنة البيانات مع {server_url}...")
        
        # إنشاء بيانات اختبار
        test_data = {
            "users": {
                f"test_user_{uuid.uuid4().hex[:8]}": {
                    "username": f"test_user_{uuid.uuid4().hex[:8]}",
                    "fullName": "مستخدم اختبار الشبكة",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "isActive": True,
                    "lastModified": int(time.time() * 1000)
                }
            },
            "events": [
                {
                    "id": str(uuid.uuid4()),
                    "serial": f"NET-TEST-{int(time.time())}",
                    "title": "اختبار مزامنة الشبكة",
                    "type": "network_test",
                    "severity": "low",
                    "description": "اختبار مزامنة البيانات عبر الشبكة",
                    "timestamp": datetime.now().isoformat(),
                    "status": "open",
                    "responsiblePerson": "نظام الاختبار"
                }
            ],
            "activities": [
                {
                    "id": str(uuid.uuid4()),
                    "userId": "network_test",
                    "username": "اختبار الشبكة",
                    "action": "network_sync_test",
                    "description": "اختبار مزامنة البيانات عبر الشبكة",
                    "timestamp": datetime.now().isoformat(),
                    "ip": self.local_ip,
                    "userAgent": "Network Test Agent"
                }
            ]
        }
        
        try:
            # إرسال البيانات
            response = requests.post(
                f"{server_url}/api/sync/full",
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ تم إرسال البيانات بنجاح")
                
                # التحقق من استلام البيانات
                time.sleep(1)  # انتظار قصير للمعالجة
                
                get_response = requests.get(f"{server_url}/api/sync/all", timeout=10)
                if get_response.status_code == 200:
                    all_data = get_response.json()
                    
                    # فحص البيانات المرسلة
                    users_found = any(username in all_data.get('users', {}) 
                                    for username in test_data['users'].keys())
                    events_found = any(event['id'] == test_data['events'][0]['id'] 
                                     for event in all_data.get('events', []))
                    activities_found = any(activity['id'] == test_data['activities'][0]['id'] 
                                         for activity in all_data.get('activities', []))
                    
                    if users_found and events_found and activities_found:
                        print(f"   ✅ تم التحقق من جميع البيانات")
                        self.test_results.append({
                            'server': server_url,
                            'test': 'data_sync',
                            'success': True,
                            'data': result
                        })
                        return True
                    else:
                        print(f"   ⚠️  بعض البيانات مفقودة")
                        self.test_results.append({
                            'server': server_url,
                            'test': 'data_sync',
                            'success': False,
                            'error': "Missing data after sync"
                        })
                        return False
                else:
                    print(f"   ❌ فشل في استلام البيانات")
                    self.test_results.append({
                        'server': server_url,
                        'test': 'data_sync',
                        'success': False,
                        'error': "Failed to retrieve data"
                    })
                    return False
            else:
                print(f"   ❌ فشل في إرسال البيانات: {response.status_code}")
                self.test_results.append({
                    'server': server_url,
                    'test': 'data_sync',
                    'success': False,
                    'error': f"HTTP {response.status_code}"
                })
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في مزامنة البيانات: {e}")
            self.test_results.append({
                'server': server_url,
                'test': 'data_sync',
                'success': False,
                'error': str(e)
            })
            return False
    
    def test_performance(self, server_url):
        """اختبار الأداء"""
        print(f"⚡ اختبار أداء الشبكة مع {server_url}...")
        
        try:
            # قياس زمن الاستجابة
            start_time = time.time()
            response = requests.get(f"{server_url}/api/sync/status", timeout=5)
            response_time = (time.time() - start_time) * 1000  # بالميلي ثانية
            
            if response.status_code == 200:
                print(f"   ⚡ زمن الاستجابة: {response_time:.2f} ms")
                
                # تقييم الأداء
                if response_time < 100:
                    performance = "ممتاز"
                elif response_time < 500:
                    performance = "جيد"
                elif response_time < 1000:
                    performance = "مقبول"
                else:
                    performance = "بطيء"
                
                print(f"   📊 تقييم الأداء: {performance}")
                
                self.test_results.append({
                    'server': server_url,
                    'test': 'performance',
                    'success': True,
                    'response_time': response_time,
                    'performance': performance
                })
                return True
            else:
                print(f"   ❌ خطأ في الاستجابة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الأداء: {e}")
            return False
    
    def generate_network_report(self):
        """إنشاء تقرير الشبكة"""
        print("\n📋 تقرير اختبار الشبكة:")
        print("=" * 60)
        
        # تجميع النتائج حسب الخادم
        servers_summary = {}
        for result in self.test_results:
            server = result['server']
            if server not in servers_summary:
                servers_summary[server] = {
                    'connectivity': False,
                    'data_sync': False,
                    'performance': None
                }
            
            if result['test'] == 'connectivity':
                servers_summary[server]['connectivity'] = result['success']
            elif result['test'] == 'data_sync':
                servers_summary[server]['data_sync'] = result['success']
            elif result['test'] == 'performance':
                servers_summary[server]['performance'] = result.get('response_time', 0)
        
        # عرض النتائج
        for server, summary in servers_summary.items():
            print(f"\n🌐 خادم: {server}")
            print(f"   الاتصال: {'✅ يعمل' if summary['connectivity'] else '❌ لا يعمل'}")
            print(f"   المزامنة: {'✅ تعمل' if summary['data_sync'] else '❌ لا تعمل'}")
            if summary['performance']:
                print(f"   الأداء: {summary['performance']:.2f} ms")
        
        # إحصائيات عامة
        total_servers = len(servers_summary)
        working_servers = sum(1 for s in servers_summary.values() if s['connectivity'])
        sync_working = sum(1 for s in servers_summary.values() if s['data_sync'])
        
        print(f"\n📊 الإحصائيات العامة:")
        print(f"   إجمالي الخوادم: {total_servers}")
        print(f"   الخوادم العاملة: {working_servers}")
        print(f"   المزامنة تعمل: {sync_working}")
        
        if working_servers == total_servers and sync_working == total_servers:
            print("\n🎉 جميع خوادم المزامنة تعمل بشكل مثالي!")
            status = "excellent"
        elif working_servers > 0 and sync_working > 0:
            print("\n✅ النظام يعمل مع بعض المشاكل البسيطة")
            status = "good"
        else:
            print("\n❌ هناك مشاكل في النظام تحتاج لحل")
            status = "poor"
        
        print("=" * 60)
        return status
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        self.print_header()
        
        # اكتشاف الخوادم
        servers = self.discover_servers()
        
        if not servers:
            print("❌ لا يمكن إجراء الاختبار بدون خوادم مزامنة")
            print("💡 تأكد من تشغيل خادم المزامنة:")
            print("   python data-sync-server.py")
            print("   أو")
            print("   python start-complete-system.py")
            return False
        
        # اختبار كل خادم
        for server in servers:
            print(f"\n🔧 اختبار الخادم: {server}")
            print("-" * 50)
            
            # اختبار الاتصال
            if self.test_server_connectivity(server):
                # اختبار المزامنة
                self.test_data_sync(server)
                # اختبار الأداء
                self.test_performance(server)
            
            print()
        
        # إنشاء التقرير
        status = self.generate_network_report()
        
        return status == "excellent"

def main():
    """الدالة الرئيسية"""
    tester = NetworkSyncTester()
    success = tester.run_comprehensive_test()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم مزامنة مبسط للشبكة
Simple Network Sync Server
"""

import os
import sys
import json
import socket
import http.server
import socketserver
from datetime import datetime
from urllib.parse import urlparse, parse_qs

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

class SimpleSyncHandler(http.server.SimpleHTTPRequestHandler):
    """معالج HTTP مبسط للمزامنة"""
    
    # مخزن البيانات المشتركة
    shared_data = {}
    clients = {}
    sync_log = []
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        """إضافة headers للسماح بالوصول من الشبكة"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Client-ID')
        super().end_headers()
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        if self.path.startswith('/api/sync/'):
            self.handle_sync_api()
        else:
            super().do_POST()
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path.startswith('/api/sync/'):
            self.handle_sync_api()
        else:
            super().do_GET()
    
    def handle_sync_api(self):
        """معالجة API المزامنة"""
        try:
            # استخراج معرف العميل
            client_id = self.headers.get('X-Client-ID', f'client_{int(datetime.now().timestamp())}')
            
            # قراءة البيانات
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = {}
            if content_length > 0:
                post_data = json.loads(self.rfile.read(content_length).decode('utf-8'))
            
            # معالجة المسارات المختلفة
            response = {}
            
            if self.path == '/api/sync/register':
                response = self.handle_register(client_id, post_data)
            elif self.path == '/api/sync/data':
                if self.command == 'POST':
                    response = self.handle_sync_data(client_id, post_data)
                else:
                    response = self.handle_get_data(client_id)
            elif self.path == '/api/sync/status':
                response = self.handle_get_status()
            elif self.path == '/api/sync/stats':
                response = self.handle_get_stats()
            else:
                response = {'error': 'Unknown endpoint'}
            
            # إرسال الاستجابة
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
            # تسجيل الطلب
            self.log_request(client_id, self.path, response.get('success', False))
            
        except Exception as e:
            self.send_error(500, f'Server Error: {str(e)}')
    
    def handle_register(self, client_id, data):
        """معالجة تسجيل العميل"""
        client_info = data.get('client_info', {})
        client_info['registered_at'] = datetime.now().isoformat()
        client_info['last_seen'] = datetime.now().isoformat()
        client_info['status'] = 'connected'
        
        SimpleSyncHandler.clients[client_id] = client_info
        
        return {
            'success': True,
            'client_id': client_id,
            'message': 'Client registered successfully'
        }
    
    def handle_sync_data(self, client_id, data):
        """معالجة مزامنة البيانات"""
        try:
            data_type = data.get('data_type', 'default')
            sync_data = data.get('data', {})
            operation = data.get('operation', 'update')
            
            # تهيئة نوع البيانات إذا لم يكن موجوداً
            if data_type not in SimpleSyncHandler.shared_data:
                SimpleSyncHandler.shared_data[data_type] = {}
            
            # تطبيق العملية
            if operation == 'update':
                SimpleSyncHandler.shared_data[data_type].update(sync_data)
            elif operation == 'replace':
                SimpleSyncHandler.shared_data[data_type] = sync_data
            elif operation == 'delete':
                for key in sync_data:
                    if key in SimpleSyncHandler.shared_data[data_type]:
                        del SimpleSyncHandler.shared_data[data_type][key]
            
            # تحديث آخر نشاط للعميل
            if client_id in SimpleSyncHandler.clients:
                SimpleSyncHandler.clients[client_id]['last_seen'] = datetime.now().isoformat()
            
            # حفظ البيانات
            self.save_data()
            
            return {
                'success': True,
                'message': 'Data synced successfully',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def handle_get_data(self, client_id):
        """معالجة طلب البيانات"""
        # تحديث آخر نشاط للعميل
        if client_id in SimpleSyncHandler.clients:
            SimpleSyncHandler.clients[client_id]['last_seen'] = datetime.now().isoformat()
        
        return {
            'success': True,
            'data': SimpleSyncHandler.shared_data,
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_get_status(self):
        """معالجة طلب حالة العملاء"""
        return {
            'success': True,
            'clients': SimpleSyncHandler.clients,
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_get_stats(self):
        """معالجة طلب الإحصائيات"""
        total_syncs = len([log for log in SimpleSyncHandler.sync_log if 'sync' in log.get('action', '')])
        active_clients = len([c for c in SimpleSyncHandler.clients.values() if c.get('status') == 'connected'])
        
        stats = {
            'total_clients': len(SimpleSyncHandler.clients),
            'active_clients': active_clients,
            'total_syncs': total_syncs,
            'data_types': list(SimpleSyncHandler.shared_data.keys()),
            'last_sync': SimpleSyncHandler.sync_log[-1]['timestamp'] if SimpleSyncHandler.sync_log else None
        }
        
        return {
            'success': True,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_request(self, client_id, path, success):
        """تسجيل الطلبات"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'client_id': client_id,
            'path': path,
            'action': 'sync' if 'data' in path else 'api',
            'success': success
        }
        
        SimpleSyncHandler.sync_log.append(log_entry)
        
        # الاحتفاظ بآخر 1000 سجل فقط
        if len(SimpleSyncHandler.sync_log) > 1000:
            SimpleSyncHandler.sync_log = SimpleSyncHandler.sync_log[-1000:]
        
        print(f"[{log_entry['timestamp']}] {client_id} - {path} - {'✅' if success else '❌'}")
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            os.makedirs("sync_data", exist_ok=True)
            
            # حفظ البيانات المشتركة
            with open("sync_data/shared_data.json", 'w', encoding='utf-8') as f:
                json.dump(SimpleSyncHandler.shared_data, f, ensure_ascii=False, indent=2)
            
            # حفظ معلومات العملاء
            with open("sync_data/clients.json", 'w', encoding='utf-8') as f:
                json.dump(SimpleSyncHandler.clients, f, ensure_ascii=False, indent=2)
            
            # حفظ السجل
            with open("sync_data/sync_log.json", 'w', encoding='utf-8') as f:
                json.dump(SimpleSyncHandler.sync_log[-100:], f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
    
    @classmethod
    def load_data(cls):
        """تحميل البيانات المحفوظة"""
        try:
            if os.path.exists("sync_data/shared_data.json"):
                with open("sync_data/shared_data.json", 'r', encoding='utf-8') as f:
                    cls.shared_data = json.load(f)
            
            if os.path.exists("sync_data/clients.json"):
                with open("sync_data/clients.json", 'r', encoding='utf-8') as f:
                    cls.clients = json.load(f)
            
            if os.path.exists("sync_data/sync_log.json"):
                with open("sync_data/sync_log.json", 'r', encoding='utf-8') as f:
                    cls.sync_log = json.load(f)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

def main():
    """الوظيفة الرئيسية"""
    local_ip = get_local_ip()
    sync_port = 8001
    
    print("🔄 بدء خادم المزامنة المبسط")
    print("=" * 50)
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print(f"🔌 منفذ المزامنة: {sync_port}")
    
    # تحميل البيانات المحفوظة
    SimpleSyncHandler.load_data()
    
    try:
        # بدء خادم HTTP
        with socketserver.TCPServer(("0.0.0.0", sync_port), SimpleSyncHandler) as httpd:
            httpd.allow_reuse_address = True
            
            print(f"\n🚀 تم بدء خادم المزامنة بنجاح!")
            print(f"🌐 روابط API المزامنة:")
            print(f"   • تسجيل العميل: http://{local_ip}:{sync_port}/api/sync/register")
            print(f"   • مزامنة البيانات: http://{local_ip}:{sync_port}/api/sync/data")
            print(f"   • حالة العملاء: http://{local_ip}:{sync_port}/api/sync/status")
            print(f"   • الإحصائيات: http://{local_ip}:{sync_port}/api/sync/stats")
            
            print(f"\n📱 صفحة اختبار المزامنة:")
            print(f"   http://{local_ip}:8000/sync-test-page.html")
            
            print(f"\n✅ خادم المزامنة جاهز ويقبل الاتصالات...")
            print(f"🛑 اضغط Ctrl+C لإيقاف الخادم")
            print("-" * 50)
            
            # بدء الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n🛑 إيقاف خادم المزامنة...")
        # حفظ البيانات قبل الإغلاق
        SimpleSyncHandler().save_data()
        print("✅ تم إيقاف خادم المزامنة بنجاح")
    except Exception as e:
        print(f"❌ خطأ في خادم المزامنة: {e}")

if __name__ == "__main__":
    main()

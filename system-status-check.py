#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص حالة النظام الشامل
Complete System Status Check
"""

import os
import sys
import socket
import requests
import json
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_port(host, port):
    """فحص إذا كان المنفذ يعمل"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(3)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception:
        return False

def check_http_endpoint(url):
    """فحص endpoint HTTP"""
    try:
        response = requests.get(url, timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def check_sync_api(base_url):
    """فحص API المزامنة"""
    endpoints = {
        'register': f'{base_url}/api/sync/register',
        'data': f'{base_url}/api/sync/data',
        'status': f'{base_url}/api/sync/status',
        'stats': f'{base_url}/api/sync/stats'
    }
    
    results = {}
    for name, url in endpoints.items():
        try:
            if name == 'register':
                # POST request for register
                response = requests.post(url, 
                    json={'client_info': {'test': True}},
                    headers={'X-Client-ID': 'test_client'},
                    timeout=5
                )
            else:
                # GET request for others
                response = requests.get(url, 
                    headers={'X-Client-ID': 'test_client'},
                    timeout=5
                )
            results[name] = response.status_code == 200
        except Exception:
            results[name] = False
    
    return results

def check_files():
    """فحص الملفات المهمة"""
    important_files = [
        'index.html',
        'login-fixed.html',
        'sync-test-page.html',
        'network-sync-client.js',
        'simple-sync-server.py',
        'auth.js',
        'script.js'
    ]
    
    results = {}
    for file in important_files:
        results[file] = os.path.exists(file)
    
    return results

def main():
    """الوظيفة الرئيسية"""
    local_ip = get_local_ip()
    
    print("🔍 فحص حالة النظام الشامل")
    print("=" * 60)
    print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print()
    
    # فحص المنافذ
    print("🔌 فحص المنافذ:")
    web_port_ok = check_port('localhost', 8000)
    sync_port_ok = check_port('localhost', 8001)
    
    print(f"   {'✅' if web_port_ok else '❌'} خادم الويب (8000)")
    print(f"   {'✅' if sync_port_ok else '❌'} خادم المزامنة (8001)")
    print()
    
    # فحص الصفحات الرئيسية
    print("🌐 فحص الصفحات:")
    pages = {
        'الصفحة الرئيسية': f'http://{local_ip}:8000/index.html',
        'تسجيل الدخول': f'http://{local_ip}:8000/login-fixed.html',
        'اختبار المزامنة': f'http://{local_ip}:8000/sync-test-page.html',
        'اختبار الشبكة': f'http://{local_ip}:8000/network-test.html'
    }
    
    page_results = {}
    for name, url in pages.items():
        status = check_http_endpoint(url)
        page_results[name] = status
        print(f"   {'✅' if status else '❌'} {name}")
    print()
    
    # فحص API المزامنة
    print("🔄 فحص API المزامنة:")
    if sync_port_ok:
        sync_results = check_sync_api(f'http://{local_ip}:8001')
        for endpoint, status in sync_results.items():
            print(f"   {'✅' if status else '❌'} {endpoint}")
    else:
        print("   ❌ خادم المزامنة غير متاح")
    print()
    
    # فحص الملفات
    print("📁 فحص الملفات المهمة:")
    file_results = check_files()
    for file, exists in file_results.items():
        print(f"   {'✅' if exists else '❌'} {file}")
    print()
    
    # النتيجة النهائية
    print("📊 ملخص الحالة:")
    print("-" * 40)
    
    total_checks = 0
    passed_checks = 0
    
    # حساب النتائج
    if web_port_ok:
        passed_checks += 1
    total_checks += 1
    
    if sync_port_ok:
        passed_checks += 1
    total_checks += 1
    
    for status in page_results.values():
        if status:
            passed_checks += 1
        total_checks += 1
    
    if sync_port_ok:
        for status in sync_results.values():
            if status:
                passed_checks += 1
            total_checks += 1
    
    for exists in file_results.values():
        if exists:
            passed_checks += 1
        total_checks += 1
    
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"✅ اختبارات ناجحة: {passed_checks}/{total_checks}")
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 النظام يعمل بشكل ممتاز!")
        status_icon = "🟢"
    elif success_rate >= 70:
        print("⚠️  النظام يعمل مع بعض المشاكل")
        status_icon = "🟡"
    else:
        print("❌ النظام يحتاج إلى إصلاح")
        status_icon = "🔴"
    
    print()
    print("🌐 الروابط المتاحة:")
    print(f"   • الصفحة الرئيسية: http://{local_ip}:8000/index.html")
    print(f"   • تسجيل الدخول: http://{local_ip}:8000/login-fixed.html")
    print(f"   • اختبار المزامنة: http://{local_ip}:8000/sync-test-page.html")
    print(f"   • اختبار الشبكة: http://{local_ip}:8000/network-test.html")
    
    print()
    print("📱 للوصول من الأجهزة الأخرى:")
    print(f"   استخدم عنوان IP: {local_ip}")
    
    print()
    print(f"{status_icon} حالة النظام: {'ممتاز' if success_rate >= 90 else 'جيد' if success_rate >= 70 else 'يحتاج إصلاح'}")
    
    return success_rate >= 70

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الفحص")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ في الفحص: {e}")
        sys.exit(1)

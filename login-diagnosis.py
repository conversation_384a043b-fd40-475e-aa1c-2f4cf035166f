#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشاكل تسجيل الدخول
Login Issues Diagnosis Tool
"""

import os
import json
import webbrowser
from datetime import datetime

class LoginDiagnoser:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def print_header(self):
        """طباعة رأس التشخيص"""
        print("=" * 60)
        print("تشخيص مشاكل تسجيل الدخول")
        print("Login Issues Diagnosis")
        print("=" * 60)
        print()
    
    def check_required_files(self):
        """فحص الملفات المطلوبة"""
        print("فحص الملفات المطلوبة...")
        
        required_files = {
            'login.html': 'صفحة تسجيل الدخول',
            'auth.js': 'ملف المصادقة',
            'login-styles.css': 'ملف تنسيق تسجيل الدخول',
            'index.html': 'الصفحة الرئيسية'
        }
        
        missing_files = []
        
        for file, description in required_files.items():
            if os.path.exists(file):
                print(f"   ✅ {file} - {description}")
            else:
                print(f"   ❌ {file} - مفقود")
                missing_files.append(file)
                self.issues_found.append(f"ملف {file} مفقود")
        
        print()
        return len(missing_files) == 0
    
    def check_user_data(self):
        """فحص بيانات المستخدمين"""
        print("فحص بيانات المستخدمين...")
        
        # فحص ملف المستخدمين في مجلد data
        data_users_file = os.path.join("data", "users.json")
        
        if os.path.exists(data_users_file):
            try:
                with open(data_users_file, 'r', encoding='utf-8') as f:
                    users_data = json.load(f)
                
                if users_data:
                    print(f"   ✅ ملف المستخدمين موجود ({len(users_data)} مستخدم)")
                    for username in users_data.keys():
                        print(f"      • {username}")
                else:
                    print("   ⚠️  ملف المستخدمين فارغ")
                    self.issues_found.append("ملف المستخدمين فارغ")
                    
            except json.JSONDecodeError:
                print("   ❌ ملف المستخدمين تالف")
                self.issues_found.append("ملف المستخدمين تالف")
        else:
            print("   ❌ ملف المستخدمين غير موجود")
            self.issues_found.append("ملف المستخدمين غير موجود")
        
        print()
        return True
    
    def create_default_users(self):
        """إنشاء المستخدمين الافتراضيين"""
        print("إنشاء المستخدمين الافتراضيين...")
        
        # إنشاء مجلد البيانات
        os.makedirs("data", exist_ok=True)
        
        # دالة تشفير بسيطة (نفس المستخدمة في auth.js)
        def hash_password(password):
            hash_val = 0
            for char in password:
                hash_val = ((hash_val << 5) - hash_val) + ord(char)
                hash_val = hash_val & 0xFFFFFFFF  # تحويل إلى 32-bit integer
                if hash_val > 0x7FFFFFFF:
                    hash_val -= 0x100000000
            return str(hash_val)
        
        # المستخدمين الافتراضيين
        default_users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": hash_password("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": hash_password("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": hash_password("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None
            }
        }
        
        try:
            # حفظ المستخدمين في ملف data/users.json
            with open(os.path.join("data", "users.json"), 'w', encoding='utf-8') as f:
                json.dump(default_users, f, ensure_ascii=False, indent=2)
            
            print("   ✅ تم إنشاء المستخدمين الافتراضيين")
            print("   👤 admin / admin123 (مدير النظام)")
            print("   👤 analyst / analyst123 (محلل أمني)")
            print("   👤 operator / operator123 (مشغل)")
            
            self.fixes_applied.append("إنشاء المستخدمين الافتراضيين")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إنشاء المستخدمين: {e}")
            return False
    
    def check_auth_js(self):
        """فحص ملف المصادقة"""
        print("فحص ملف المصادقة...")
        
        if not os.path.exists('auth.js'):
            print("   ❌ ملف auth.js غير موجود")
            self.issues_found.append("ملف auth.js مفقود")
            return False
        
        try:
            with open('auth.js', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص المكونات المطلوبة
            required_components = [
                'class AuthManager',
                'handleLogin',
                'authenticateUser',
                'hashPassword',
                'loadUsers'
            ]
            
            missing_components = []
            for component in required_components:
                if component in content:
                    print(f"   ✅ {component}")
                else:
                    print(f"   ❌ {component} - مفقود")
                    missing_components.append(component)
            
            if missing_components:
                self.issues_found.extend([f"مكون {comp} مفقود في auth.js" for comp in missing_components])
                return False
            
            print("   ✅ ملف المصادقة سليم")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في قراءة ملف المصادقة: {e}")
            self.issues_found.append("خطأ في قراءة ملف المصادقة")
            return False
    
    def test_login_page(self):
        """اختبار صفحة تسجيل الدخول"""
        print("اختبار صفحة تسجيل الدخول...")
        
        if not os.path.exists('login.html'):
            print("   ❌ صفحة تسجيل الدخول غير موجودة")
            self.issues_found.append("صفحة تسجيل الدخول مفقودة")
            return False
        
        try:
            with open('login.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص العناصر المطلوبة
            required_elements = [
                'id="username"',
                'id="password"',
                'id="loginForm"',
                'id="loginBtn"',
                'id="loginError"',
                'src="auth.js"'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element in content:
                    print(f"   ✅ {element}")
                else:
                    print(f"   ❌ {element} - مفقود")
                    missing_elements.append(element)
            
            if missing_elements:
                self.issues_found.extend([f"عنصر {elem} مفقود في login.html" for elem in missing_elements])
                return False
            
            print("   ✅ صفحة تسجيل الدخول سليمة")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في قراءة صفحة تسجيل الدخول: {e}")
            self.issues_found.append("خطأ في قراءة صفحة تسجيل الدخول")
            return False
    
    def create_login_test_page(self):
        """إنشاء صفحة اختبار تسجيل الدخول"""
        print("إنشاء صفحة اختبار تسجيل الدخول...")
        
        test_page_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار تسجيل الدخول</h1>
        
        <div class="test-section info">
            <h3>المستخدمين الافتراضيين:</h3>
            <ul>
                <li><strong>admin</strong> / admin123 (مدير النظام)</li>
                <li><strong>analyst</strong> / analyst123 (محلل أمني)</li>
                <li><strong>operator</strong> / operator123 (مشغل)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>اختبار سريع:</h3>
            <input type="text" id="testUsername" placeholder="اسم المستخدم" value="admin">
            <input type="password" id="testPassword" placeholder="كلمة المرور" value="admin123">
            <button class="btn-primary" onclick="testLogin()">اختبار تسجيل الدخول</button>
        </div>
        
        <div id="testResult" class="test-section" style="display: none;"></div>
        
        <div class="test-section">
            <h3>روابط مفيدة:</h3>
            <button class="btn-success" onclick="window.open('login.html', '_blank')">فتح صفحة تسجيل الدخول</button>
            <button class="btn-success" onclick="window.open('index.html', '_blank')">فتح الصفحة الرئيسية</button>
        </div>
        
        <div class="test-section">
            <h3>معلومات النظام:</h3>
            <p><strong>الوقت الحالي:</strong> <span id="currentTime"></span></p>
            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
            <p><strong>localStorage متاح:</strong> <span id="localStorageStatus"></span></p>
        </div>
    </div>
    
    <script>
        // تحديث الوقت
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // معلومات المتصفح
        document.getElementById('browserInfo').textContent = navigator.userAgent;
        
        // فحص localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            document.getElementById('localStorageStatus').textContent = 'متاح ✅';
        } catch (e) {
            document.getElementById('localStorageStatus').textContent = 'غير متاح ❌';
        }
        
        // اختبار تسجيل الدخول
        function testLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            const resultDiv = document.getElementById('testResult');
            
            // دالة التشفير (نفس المستخدمة في النظام)
            function hashPassword(password) {
                let hash = 0;
                for (let i = 0; i < password.length; i++) {
                    const char = password.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return hash.toString();
            }
            
            // تحميل المستخدمين
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[username];
                
                if (!user) {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = '<h3>❌ فشل الاختبار</h3><p>المستخدم غير موجود</p>';
                } else if (!user.isActive) {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = '<h3>❌ فشل الاختبار</h3><p>الحساب معطل</p>';
                } else {
                    const hashedPassword = hashPassword(password);
                    if (user.password === hashedPassword) {
                        resultDiv.className = 'test-section success';
                        resultDiv.innerHTML = '<h3>✅ نجح الاختبار</h3><p>بيانات الدخول صحيحة</p>';
                    } else {
                        resultDiv.className = 'test-section error';
                        resultDiv.innerHTML = '<h3>❌ فشل الاختبار</h3><p>كلمة المرور غير صحيحة</p>';
                    }
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = '<h3>❌ خطأ في الاختبار</h3><p>' + error.message + '</p>';
            }
            
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>'''
        
        try:
            with open('login-test.html', 'w', encoding='utf-8') as f:
                f.write(test_page_content)
            
            print("   ✅ تم إنشاء صفحة اختبار تسجيل الدخول: login-test.html")
            self.fixes_applied.append("إنشاء صفحة اختبار تسجيل الدخول")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إنشاء صفحة الاختبار: {e}")
            return False
    
    def generate_diagnosis_report(self):
        """إنشاء تقرير التشخيص"""
        print("\nتقرير تشخيص تسجيل الدخول:")
        print("=" * 50)
        
        print(f"المشاكل المكتشفة: {len(self.issues_found)}")
        for i, issue in enumerate(self.issues_found, 1):
            print(f"   {i}. {issue}")
        
        print(f"\nالإصلاحات المطبقة: {len(self.fixes_applied)}")
        for i, fix in enumerate(self.fixes_applied, 1):
            print(f"   {i}. {fix}")
        
        if len(self.issues_found) == 0:
            status = "ممتاز"
            print("\n🎉 لا توجد مشاكل! نظام تسجيل الدخول يعمل بشكل مثالي")
        elif len(self.fixes_applied) >= len(self.issues_found):
            status = "جيد"
            print("\n✅ تم إصلاح معظم المشاكل")
        else:
            status = "يحتاج عمل"
            print("\n⚠️  توجد مشاكل تحتاج لمزيد من العمل")
        
        print("\nنصائح:")
        print("   • استخدم صفحة الاختبار: login-test.html")
        print("   • تأكد من تفعيل JavaScript في المتصفح")
        print("   • امسح cache المتصفح إذا لزم الأمر")
        
        print("=" * 50)
        return status
    
    def run_comprehensive_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        self.print_header()
        
        print("بدء تشخيص مشاكل تسجيل الدخول...")
        print()
        
        # فحص الملفات المطلوبة
        files_ok = self.check_required_files()
        
        # فحص بيانات المستخدمين
        self.check_user_data()
        
        # إنشاء المستخدمين الافتراضيين إذا لزم الأمر
        self.create_default_users()
        
        # فحص ملف المصادقة
        auth_ok = self.check_auth_js()
        
        # فحص صفحة تسجيل الدخول
        login_ok = self.test_login_page()
        
        # إنشاء صفحة اختبار
        self.create_login_test_page()
        
        # إنشاء التقرير
        status = self.generate_diagnosis_report()
        
        # فتح صفحة الاختبار
        if os.path.exists('login-test.html'):
            print("\nفتح صفحة اختبار تسجيل الدخول...")
            try:
                webbrowser.open('login-test.html')
                print("✅ تم فتح صفحة الاختبار")
            except Exception as e:
                print(f"⚠️  فشل في فتح صفحة الاختبار: {e}")
                print("افتح الملف يدوياً: login-test.html")
        
        return status in ["ممتاز", "جيد"]

def main():
    """الدالة الرئيسية"""
    diagnoser = LoginDiagnoser()
    success = diagnoser.run_comprehensive_diagnosis()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

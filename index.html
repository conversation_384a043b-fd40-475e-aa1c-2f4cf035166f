<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أمن المعلومات</title>
    <!-- النظام يستخدم التقويم الإفرنجي (الميلادي) لجميع التواريخ -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="أمن المعلومات">
    <link rel="apple-touch-icon" href="logo.jpg">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SheetJS library for Excel import/export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    <script src="network-sync-client.js"></script>
    <script src="sync-integration.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Main Title Section -->
        <section class="main-title-section">
            <div class="title-content">
                <div class="logo interactive-glow">
                    <img src="logo.jpg" alt="شعار النظام" class="logo-image">
                    <i class="fas fa-shield-alt" style="display: none;"></i>
                    <h1>نظام إدارة أمن المعلومات</h1>
                </div>
                <div class="header-controls">
                    <button class="theme-toggle interactive-glow" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu interactive-glow" id="userMenu">
                        <i class="fas fa-user-shield"></i>
                        <span id="currentUserName">المدير</span>
                        <div class="user-dropdown">
                            <button class="dropdown-item" id="userProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                الملف الشخصي
                            </button>
                            <button class="dropdown-item" id="changePasswordBtn">
                                <i class="fas fa-key"></i>
                                تغيير كلمة المرور
                            </button>
                            <button class="dropdown-item" id="backupBtn">
                                <i class="fas fa-download"></i>
                                نسخة احتياطية
                            </button>
                            <button class="dropdown-item" id="restoreBtn">
                                <i class="fas fa-upload"></i>
                                استيراد نسخة احتياطية
                            </button>
                            <button class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span class="sr-only">تسجيل الخروج</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Header -->
        <header class="header">
            <div class="header-content">
                <nav class="main-nav">
                    <button class="nav-btn active bounce-in" data-section="events">
                        <i class="fas fa-clipboard-list"></i>
                        <span>الأحداث</span>
                    </button>
                    <button class="nav-btn fade-in-delay-1" data-section="post-incident-review">
                        <i class="fas fa-search-plus"></i>
                        <span>مراجعة ما بعد الحادث</span>
                    </button>
                    <button class="nav-btn fade-in-delay-2" data-section="risk-analysis">
                        <i class="fas fa-shield-alt"></i>
                        <span>تحليل المخاطر</span>
                    </button>
                    <button class="nav-btn fade-in-delay-3" data-section="analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>الإحصائيات</span>
                    </button>
                    <button class="nav-btn fade-in-delay-3" data-section="event-types">
                        <i class="fas fa-cogs"></i>
                        <span>إدارة أنواع الأحداث</span>
                    </button>
                    <button class="nav-btn admin-only fade-in-delay-2" data-section="user-management" style="display: none;">
                        <i class="fas fa-users-cog"></i>
                        <span>إدارة المستخدمين</span>
                    </button>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Events Section -->
            <div class="content-section active" id="events-section">
                <!-- Backup Management Section -->
                <section class="backup-management-section">
                    <div class="section-header">
                        <h2><i class="fas fa-shield-alt"></i> إدارة النسخ الاحتياطية</h2>
                        <button class="toggle-section-btn" id="toggleBackupSection" title="إظهار/إخفاء قسم النسخ الاحتياطية">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="backup-controls">
                        <div class="backup-actions">
                            <button class="btn btn-success backup-action-btn" id="createBackupBtn">
                                <i class="fas fa-download"></i>
                                <span>إنشاء نسخة احتياطية</span>
                                <small>حفظ جميع البيانات</small>
                            </button>
                            <button class="btn btn-info backup-action-btn" id="importBackupBtn">
                                <i class="fas fa-upload"></i>
                                <span>استيراد نسخة احتياطية</span>
                                <small>استعادة البيانات المحفوظة</small>
                            </button>
                            <button class="btn btn-warning backup-action-btn" id="syncDataBtn">
                                <i class="fas fa-sync-alt"></i>
                                <span>مزامنة البيانات</span>
                                <small>مزامنة مع الشبكة</small>
                            </button>
                        </div>
                        <div class="backup-info">
                            <div class="info-card">
                                <i class="fas fa-info-circle"></i>
                                <div class="info-content">
                                    <h4>معلومات النسخة الاحتياطية الشاملة</h4>
                                    <ul>
                                        <li>تشمل بيانات جميع الشاشات والوظائف</li>
                                        <li>تحفظ بصيغة JSON مع إحصائيات مفصلة</li>
                                        <li>يمكن استيرادها على أي جهاز أو متصفح</li>
                                        <li>تتضمن بيانات المزامنة والشبكة</li>
                                        <li>تحتوي على سجلات الأنشطة والتدقيق</li>
                                        <li>يُنصح بإنشاء نسخة احتياطية أسبوعياً</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="backup-status" id="backupStatus">
                                <div class="status-item">
                                    <i class="fas fa-clock"></i>
                                    <span>آخر نسخة احتياطية:</span>
                                    <span id="lastBackupTime">لم يتم إنشاء نسخة بعد</span>
                                </div>
                                <div class="status-item">
                                    <i class="fas fa-database"></i>
                                    <span>حجم البيانات:</span>
                                    <span id="dataSize">جاري الحساب...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Event Input Section -->
                <section class="event-input-section enhanced-section">
                    <div class="section-header modern-header">
                        <div class="header-content">
                            <h2><i class="fas fa-plus-circle gradient-icon"></i> إدخال حدث أمني جديد</h2>
                            <p class="section-description">قم بإدخال تفاصيل الحدث الأمني الجديد بدقة لضمان التوثيق الصحيح</p>
                        </div>
                        <div class="header-actions">
                            <button type="button" class="btn btn-outline" id="clearFormBtn">
                                <i class="fas fa-eraser"></i>
                                مسح النموذج
                            </button>
                            <button type="button" class="btn btn-info" id="autoFillBtn">
                                <i class="fas fa-magic"></i>
                                ملء تلقائي
                            </button>
                        </div>
                    </div>
                <form class="event-form enhanced-form" id="eventForm">
                    <!-- Form Progress Indicator -->
                    <div class="form-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="formProgress"></div>
                        </div>
                        <span class="progress-text">0% مكتمل</span>
                    </div>

                    <!-- Basic Information Section -->
                    <div class="form-section" data-section="basic">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="eventSerial">
                                    <i class="fas fa-hashtag"></i>
                                    رقم التسلسل (تلقائي)
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="eventSerial" name="eventSerial" readonly placeholder="سيتم إنشاؤه تلقائياً">
                                    <div class="input-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="eventTitle">
                                    <i class="fas fa-heading"></i>
                                    عنوان الحدث
                                    <span class="required">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="eventTitle" name="eventTitle" required placeholder="أدخل عنوان واضح ومختصر للحدث">
                                    <div class="input-validation" id="titleValidation"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Severity and Responsibility Section -->
                    <div class="form-section" data-section="severity">
                        <h3 class="form-section-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            مستوى الخطورة والمسؤولية
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="eventSeverity">
                                    <i class="fas fa-thermometer-half"></i>
                                    مستوى الخطورة
                                    <span class="required">*</span>
                                </label>
                                <div class="select-wrapper">
                                    <select id="eventSeverity" name="eventSeverity" required>
                                        <option value="">اختر مستوى الخطورة</option>
                                        <option value="low" data-color="#10b981">منخفض</option>
                                        <option value="medium" data-color="#f59e0b">متوسط</option>
                                        <option value="high" data-color="#ef4444">عالي</option>
                                        <option value="critical" data-color="#dc2626">حرج</option>
                                    </select>
                                    <div class="severity-indicator" id="severityIndicator"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="responsiblePerson">
                                    <i class="fas fa-user-tie"></i>
                                    الشخص المسؤول عن الحدث
                                    <span class="required">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="responsiblePerson" name="responsiblePerson" required placeholder="اسم الشخص المسؤول">
                                    <div class="input-suggestions" id="responsiblePersonSuggestions"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventType">نوع الحدث</label>
                            <select id="eventType" name="eventType" required>
                                <option value="">اختر نوع الحدث</option>
                                <option value="intrusion">محاولة اختراق</option>
                                <option value="malware">برمجيات خبيثة</option>
                                <option value="phishing">تصيد إلكتروني</option>
                                <option value="data-breach">تسريب بيانات</option>
                                <option value="unauthorized-access">وصول غير مصرح</option>
                                <option value="system-failure">فشل النظام</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="eventDate">تاريخ ووقت الحدث</label>
                            <input type="datetime-local" id="eventDate" name="eventDate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="eventDescription">وصف الحدث مختصر</label>
                        <textarea id="eventDescription" name="eventDescription" rows="3" required placeholder="اكتب وصفاً مختصراً للحدث الأمني..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="eventDetailedDescription">وصف الحدث بالتفصيل</label>
                        <textarea id="eventDetailedDescription" name="eventDetailedDescription" rows="6" placeholder="اكتب وصفاً مفصلاً للحدث الأمني، الخطوات المتخذة، والتوصيات..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="affectedSystems">الأنظمة المتأثرة</label>
                            <input type="text" id="affectedSystems" name="affectedSystems" placeholder="مثال: خادم الويب، قاعدة البيانات">
                        </div>
                        <div class="form-group">
                            <label for="eventLocation">موقع الحدث</label>
                            <input type="text" id="eventLocation" name="eventLocation" placeholder="مثال: مركز البيانات، المكتب الرئيسي">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="eventNotes">ملاحظات إضافية</label>
                        <textarea id="eventNotes" name="eventNotes" rows="3" placeholder="أي ملاحظات أو معلومات إضافية..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="directCosts">التكاليف المباشرة</label>
                            <input type="text" id="directCosts" name="directCosts" placeholder="مثال: تكلفة الإصلاح، استبدال الأجهزة">
                        </div>
                        <div class="form-group">
                            <label for="indirectCosts">التكاليف الغير مباشرة</label>
                            <input type="text" id="indirectCosts" name="indirectCosts" placeholder="مثال: فقدان الإنتاجية، تأثير السمعة">
                        </div>
                    </div>



                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary bounce-in">
                            <i class="fas fa-save"></i> حفظ الحدث
                        </button>
                        <button type="reset" class="btn btn-secondary fade-in-delay-1">
                            <i class="fas fa-undo-alt"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </section>

            <!-- Events Log Section -->
            <section class="events-log-section">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> سجل الأحداث الأمنية</h2>
                    <div class="log-controls">
                        <input type="text" id="searchEvents" placeholder="البحث في الأحداث..." class="search-input">
                        <select id="filterSeverity" class="filter-select">
                            <option value="">جميع مستويات الخطورة</option>
                            <option value="low">منخفض</option>
                            <option value="medium">متوسط</option>
                            <option value="high">عالي</option>
                            <option value="critical">حرج</option>
                        </select>
                        <div class="excel-controls">
                            <button class="btn btn-info bounce-in" id="exportExcelBtn">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-secondary fade-in-delay-1" id="importExcelBtn">
                                <i class="fas fa-cloud-upload-alt"></i>
                                استيراد Excel
                            </button>
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
                        </div>
                    </div>
                </div>
                <div class="events-table-container">
                    <table class="events-table" id="eventsTable">
                        <thead>
                            <tr>
                                <th>رقم التسلسل</th>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>الخطورة</th>
                                <th>المسؤول</th>
                                <th>التكاليف</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="eventsTableBody">
                            <!-- Events will be populated here -->
                        </tbody>
                    </table>
                </div>
            </section>
            </div>

            <!-- Analytics Section -->
            <div class="content-section" id="analytics-section">
                <!-- Analytics Header -->
                <section class="analytics-header enhanced-section">
                    <div class="section-header modern-header">
                        <div class="header-content">
                            <h2><i class="fas fa-chart-line gradient-icon"></i> لوحة التحليلات والإحصائيات</h2>
                            <p class="section-description">تحليل شامل للأحداث الأمنية مع رؤى تفاعلية ومؤشرات الأداء الرئيسية</p>
                        </div>
                        <div class="header-actions">
                            <button type="button" class="btn btn-outline" id="refreshAnalyticsBtn">
                                <i class="fas fa-sync-alt"></i>
                                تحديث البيانات
                            </button>
                            <button type="button" class="btn btn-info" id="exportAnalyticsBtn">
                                <i class="fas fa-download"></i>
                                تصدير التقرير
                            </button>
                            <div class="time-range-selector">
                                <select id="analyticsTimeRange" class="form-select">
                                    <option value="7">آخر 7 أيام</option>
                                    <option value="30" selected>آخر 30 يوم</option>
                                    <option value="90">آخر 3 أشهر</option>
                                    <option value="365">آخر سنة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Statistics Overview -->
                <section class="stats-overview enhanced-section">
                    <div class="section-header">
                        <h3><i class="fas fa-tachometer-alt"></i> مؤشرات الأداء الرئيسية</h3>
                    </div>
                    <div class="stats-grid enhanced-stats">
                        <div class="stat-card modern-card total-events" data-stat="total">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="totalEvents" class="counter">0</span>
                                    <span class="stat-change" id="totalEventsChange">+0%</span>
                                </div>
                                <p class="stat-label">إجمالي الأحداث</p>
                                <div class="stat-trend" id="totalEventsTrend"></div>
                            </div>
                        </div>

                        <div class="stat-card modern-card critical-events" data-stat="critical">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-fire-alt"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="criticalEvents" class="counter">0</span>
                                    <span class="stat-change" id="criticalEventsChange">+0%</span>
                                </div>
                                <p class="stat-label">أحداث حرجة</p>
                                <div class="stat-trend" id="criticalEventsTrend"></div>
                            </div>
                        </div>

                        <div class="stat-card modern-card high-events" data-stat="high">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="highEvents" class="counter">0</span>
                                    <span class="stat-change" id="highEventsChange">+0%</span>
                                </div>
                                <p class="stat-label">أحداث عالية الخطورة</p>
                                <div class="stat-trend" id="highEventsTrend"></div>
                            </div>
                        </div>

                        <div class="stat-card modern-card open-events" data-stat="open">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="openEvents" class="counter">0</span>
                                    <span class="stat-change" id="openEventsChange">+0%</span>
                                </div>
                                <p class="stat-label">أحداث مفتوحة</p>
                                <div class="stat-trend" id="openEventsTrend"></div>
                            </div>
                        </div>

                        <div class="stat-card modern-card response-time" data-stat="response">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="avgResponseTime" class="counter">0</span>
                                    <span class="stat-unit">ساعة</span>
                                </div>
                                <p class="stat-label">متوسط وقت الاستجابة</p>
                                <div class="stat-trend" id="responseTimeTrend"></div>
                            </div>
                        </div>

                        <div class="stat-card modern-card resolution-rate" data-stat="resolution">
                            <div class="card-background"></div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">
                                    <span id="resolutionRate" class="counter">0</span>
                                    <span class="stat-unit">%</span>
                                </div>
                                <p class="stat-label">معدل الحل</p>
                                <div class="stat-trend" id="resolutionRateTrend"></div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Charts Section -->
                <section class="charts-section fade-in">
                    <div class="charts-grid">
                        <div class="chart-container slide-in-left interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-pie"></i> توزيع الأحداث حسب الخطورة</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="severityChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-right interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-area"></i> الأحداث خلال الأسبوع الماضي</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="timelineChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-left interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-bar"></i> توزيع أنواع الأحداث</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="typeChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-right interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-clipboard-check"></i> حالة الأحداث</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Risk Analysis Section -->
            <div class="content-section" id="risk-analysis-section">
                <!-- Risk Analysis Header -->
                <section class="risk-analysis-header enhanced-section">
                    <div class="section-header modern-header">
                        <div class="header-content">
                            <h2><i class="fas fa-shield-alt gradient-icon"></i> مركز تحليل المخاطر الأمنية</h2>
                            <p class="section-description">تحليل شامل ومتقدم للمخاطر الأمنية مع تقييم المخاطر وتوصيات الحماية</p>
                        </div>
                        <div class="header-actions">
                            <button type="button" class="btn btn-outline" id="refreshRiskBtn">
                                <i class="fas fa-sync-alt"></i>
                                تحديث التحليل
                            </button>
                            <button type="button" class="btn btn-warning" id="generateRiskReportBtn">
                                <i class="fas fa-file-alt"></i>
                                تقرير المخاطر
                            </button>
                            <button type="button" class="btn btn-danger" id="emergencyResponseBtn">
                                <i class="fas fa-exclamation-triangle"></i>
                                استجابة طوارئ
                            </button>
                        </div>
                    </div>
                </section>

                <section class="risk-analysis enhanced-section">

                    <!-- Enhanced Risk Score Dashboard -->
                    <div class="risk-dashboard">
                        <div class="risk-overview-grid">
                            <!-- Main Risk Score -->
                            <div class="risk-score-card main-risk-card">
                                <div class="card-header">
                                    <h3><i class="fas fa-tachometer-alt"></i> مؤشر المخاطر العام</h3>
                                    <div class="risk-status-indicator" id="riskStatusIndicator"></div>
                                </div>
                                <div class="risk-score-content">
                                    <div class="risk-gauge-container">
                                        <div class="risk-gauge-wrapper">
                                            <canvas id="riskGauge" width="200" height="200"></canvas>
                                            <div class="gauge-center">
                                                <span class="risk-score-number" id="riskScore">65</span>
                                                <span class="risk-score-max">/100</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="risk-details">
                                        <div class="risk-level-badge" id="riskLevel">متوسط</div>
                                        <div class="risk-description" id="riskDescription">
                                            يوجد مخاطر أمنية تتطلب اهتماماً
                                        </div>
                                        <div class="risk-trend" id="riskTrend">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>+5% من الأسبوع الماضي</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Risk Summary Cards -->
                            <div class="risk-summary-cards">
                                <div class="risk-summary-card critical-risks">
                                    <div class="summary-icon">
                                        <i class="fas fa-fire"></i>
                                    </div>
                                    <div class="summary-content">
                                        <h4 id="criticalRisksCount">3</h4>
                                        <p>مخاطر حرجة</p>
                                        <div class="summary-trend up">↗ +1</div>
                                    </div>
                                </div>

                                <div class="risk-summary-card high-risks">
                                    <div class="summary-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="summary-content">
                                        <h4 id="highRisksCount">7</h4>
                                        <p>مخاطر عالية</p>
                                        <div class="summary-trend down">↘ -2</div>
                                    </div>
                                </div>

                                <div class="risk-summary-card medium-risks">
                                    <div class="summary-icon">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="summary-content">
                                        <h4 id="mediumRisksCount">12</h4>
                                        <p>مخاطر متوسطة</p>
                                        <div class="summary-trend neutral">→ 0</div>
                                    </div>
                                </div>

                                <div class="risk-summary-card mitigated-risks">
                                    <div class="summary-icon">
                                        <i class="fas fa-shield-check"></i>
                                    </div>
                                    <div class="summary-content">
                                        <h4 id="mitigatedRisksCount">18</h4>
                                        <p>مخاطر محلولة</p>
                                        <div class="summary-trend up">↗ +5</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Risk Categories -->
                    <div class="risk-categories-section">
                        <div class="section-header">
                            <h3><i class="fas fa-layer-group"></i> تحليل المخاطر حسب الفئة</h3>
                            <div class="category-filters">
                                <button class="filter-btn active" data-filter="all">الكل</button>
                                <button class="filter-btn" data-filter="critical">حرجة</button>
                                <button class="filter-btn" data-filter="high">عالية</button>
                                <button class="filter-btn" data-filter="medium">متوسطة</button>
                                <button class="filter-btn" data-filter="low">منخفضة</button>
                            </div>
                        </div>

                        <div class="risk-categories-grid">
                            <div class="risk-category-card" data-category="critical">
                                <div class="category-header">
                                    <div class="category-icon critical">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="category-info">
                                        <h4>مخاطر الوصول غير المصرح</h4>
                                        <span class="risk-badge critical" id="accessRisk">حرج</span>
                                    </div>
                                    <div class="category-actions">
                                        <button class="action-btn" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="اتخاذ إجراء">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="category-content">
                                    <div class="risk-metrics">
                                        <div class="metric">
                                            <span class="metric-label">مستوى الخطر</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill critical" style="width: 85%"></div>
                                            </div>
                                            <span class="metric-value">85%</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-label">احتمالية الحدوث</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill high" style="width: 75%"></div>
                                            </div>
                                            <span class="metric-value">75%</span>
                                        </div>
                                    </div>
                                    <div class="category-description">
                                        <p>تم رصد محاولات وصول غير مصرح متعددة من مصادر مختلفة</p>
                                        <div class="recent-incidents">
                                            <span class="incident-count">5 حوادث في آخر 24 ساعة</span>
                                        </div>
                                    </div>
                                    <div class="category-recommendations">
                                        <h5>التوصيات:</h5>
                                        <ul>
                                            <li>تفعيل المصادقة الثنائية</li>
                                            <li>مراجعة صلاحيات المستخدمين</li>
                                            <li>تحديث كلمات المرور</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-category-card" data-category="high">
                                <div class="category-header">
                                    <div class="category-icon high">
                                        <i class="fas fa-bug"></i>
                                    </div>
                                    <div class="category-info">
                                        <h4>مخاطر البرمجيات الخبيثة</h4>
                                        <span class="risk-badge high" id="malwareRisk">عالي</span>
                                    </div>
                                    <div class="category-actions">
                                        <button class="action-btn" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="اتخاذ إجراء">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="category-content">
                                    <div class="risk-metrics">
                                        <div class="metric">
                                            <span class="metric-label">مستوى الخطر</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill high" style="width: 70%"></div>
                                            </div>
                                            <span class="metric-value">70%</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-label">احتمالية الحدوث</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill medium" style="width: 45%"></div>
                                            </div>
                                            <span class="metric-value">45%</span>
                                        </div>
                                    </div>
                                    <div class="category-description">
                                        <p>مستوى معتدل من التهديدات البرمجية مع زيادة في النشاط المشبوه</p>
                                        <div class="recent-incidents">
                                            <span class="incident-count">2 تهديدات محتملة</span>
                                        </div>
                                    </div>
                                    <div class="category-recommendations">
                                        <h5>التوصيات:</h5>
                                        <ul>
                                            <li>تحديث برامج مكافحة الفيروسات</li>
                                            <li>فحص شامل للأنظمة</li>
                                            <li>تدريب المستخدمين</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-category-card" data-category="critical">
                                <div class="category-header">
                                    <div class="category-icon critical">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="category-info">
                                        <h4>مخاطر تسريب البيانات</h4>
                                        <span class="risk-badge critical" id="dataRisk">حرج</span>
                                    </div>
                                    <div class="category-actions">
                                        <button class="action-btn" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" title="اتخاذ إجراء">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="category-content">
                                    <div class="risk-metrics">
                                        <div class="metric">
                                            <span class="metric-label">مستوى الخطر</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill critical" style="width: 90%"></div>
                                            </div>
                                            <span class="metric-value">90%</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-label">احتمالية الحدوث</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill critical" style="width: 85%"></div>
                                            </div>
                                            <span class="metric-value">85%</span>
                                        </div>
                                    </div>
                                    <div class="category-description">
                                        <p>خطر عالي جداً لتسريب البيانات الحساسة مع وجود نقاط ضعف</p>
                                        <div class="recent-incidents">
                                            <span class="incident-count">تحذير: نشاط مشبوه مكتشف</span>
                                        </div>
                                    </div>
                                    <div class="category-recommendations">
                                        <h5>التوصيات العاجلة:</h5>
                                        <ul>
                                            <li>تشفير البيانات الحساسة فوراً</li>
                                            <li>مراجعة صلاحيات الوصول</li>
                                            <li>تفعيل مراقبة الشبكة</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <div class="risk-category">
                            <div class="risk-category-header">
                                <h4><i class="fas fa-server"></i> مخاطر فشل الأنظمة</h4>
                                <span class="risk-badge low" id="systemRisk">منخفض</span>
                            </div>
                            <div class="risk-category-content">
                                <div class="risk-progress">
                                    <div class="risk-progress-bar" style="width: 25%"></div>
                                </div>
                                <p>الأنظمة تعمل بشكل مستقر</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="recommendations">
                        <div class="section-header">
                            <h3><i class="fas fa-lightbulb"></i> التوصيات الأمنية</h3>
                        </div>
                        <div class="recommendations-list" id="recommendationsList">
                            <!-- Recommendations will be populated by JavaScript -->
                        </div>
                    </div>
                </section>
            </div>

            <!-- Event Types Management Section -->
            <div class="content-section" id="event-types-section">
                <section class="event-types-management">
                    <div class="section-header">
                        <h2><i class="fas fa-tags"></i> إدارة أنواع الأحداث</h2>
                        <button class="btn btn-primary" id="addEventTypeBtn">
                            <i class="fas fa-plus"></i>
                            إضافة نوع جديد
                        </button>
                    </div>

                    <div class="event-types-container">
                        <div class="event-types-grid" id="eventTypesGrid">
                            <!-- Event types will be rendered here -->
                        </div>
                    </div>
                </section>
            </div>

            <!-- Post Incident Review Section -->
            <div class="content-section" id="post-incident-review-section">
                <section class="post-incident-review">
                    <div class="section-header">
                        <h2><i class="fas fa-clipboard-check"></i> المراجعة ما بعد الحادث</h2>
                        <div class="header-actions">
                            <button class="btn btn-secondary" id="viewReviewsBtn">
                                <i class="fas fa-list"></i>
                                عرض المراجعات المحفوظة
                            </button>
                        </div>
                    </div>

                    <form class="post-incident-form" id="postIncidentForm">
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="reviewerName">الاسم</label>
                                    <input type="text" id="reviewerName" name="reviewerName" required>
                                </div>
                                <div class="form-group">
                                    <label for="reviewerDepartment">القسم</label>
                                    <input type="text" id="reviewerDepartment" name="reviewerDepartment" required>
                                </div>
                                <div class="form-group">
                                    <label for="reviewerManagement">الإدارة</label>
                                    <input type="text" id="reviewerManagement" name="reviewerManagement" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentDate">التاريخ</label>
                                    <input type="date" id="incidentDate" name="incidentDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="incidentTime">الوقت</label>
                                    <input type="time" id="incidentTime" name="incidentTime" required>
                                </div>
                                <div class="form-group">
                                    <label for="incidentLocation">المكان</label>
                                    <textarea id="incidentLocation" name="incidentLocation" rows="2" required></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Details -->
                        <div class="form-section">
                            <h3><i class="fas fa-exclamation-triangle"></i> تفاصيل الحادث</h3>

                            <!-- Import Event Section -->
                            <div class="import-event-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="eventSerialImport">استيراد حدث من السجل</label>
                                        <div class="import-controls">
                                            <div class="import-input-container">
                                                <input type="text" id="eventSerialImport" placeholder="أدخل رقم التسلسل (مثال: SEC-001)" class="import-input" autocomplete="off">
                                                <div class="event-suggestions" id="eventSuggestions"></div>
                                            </div>
                                            <button type="button" class="btn btn-info" id="importEventBtn">
                                                <i class="fas fa-download"></i>
                                                استيراد الحدث
                                            </button>
                                        </div>
                                        <small class="form-help">يمكنك استيراد تفاصيل حدث موجود من سجل الأحداث الأمنية</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentDescription">وصف الحادث</label>
                                    <textarea id="incidentDescription" name="incidentDescription" rows="4" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="incidentRecordNumber">رقم الحادث في السجل</label>
                                    <input type="text" id="incidentRecordNumber" name="incidentRecordNumber" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentType">نوع الحادث</label>
                                    <select id="incidentType" name="incidentType" required>
                                        <option value="">اختر نوع الحادث</option>
                                        <!-- سيتم تحميل الأنواع من النظام -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="incidentResponsible">المسؤول عن الحادث</label>
                                    <input type="text" id="incidentResponsible" name="incidentResponsible" required>
                                </div>
                            </div>
                        </div>

                        <!-- Employee Preparedness -->
                        <div class="form-section">
                            <h3><i class="fas fa-user-check"></i> تقييم الموظف</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employeePreparedness">استعداد الموظف للحادث</label>
                                    <select id="employeePreparedness" name="employeePreparedness" required>
                                        <option value="">اختر مستوى الاستعداد</option>
                                        <option value="prepared">مستعد</option>
                                        <option value="partial">جزئي</option>
                                        <option value="unprepared">غير مستعد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="employeeResponse">كيفية استجابته للحادث</label>
                                    <select id="employeeResponse" name="employeeResponse" required>
                                        <option value="">اختر مستوى الاستجابة</option>
                                        <option value="poor">سيئ الاستجابة</option>
                                        <option value="partial">جزئي</option>
                                        <option value="complete">كامل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Analysis -->
                        <div class="form-section">
                            <h3><i class="fas fa-search"></i> تحليل الحادث</h3>
                            <div class="form-group">
                                <label for="apparentCauses">الأسباب الظاهرية للحادث</label>
                                <textarea id="apparentCauses" name="apparentCauses" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="rootCauses">الأسباب الجذرية للحادث</label>
                                <textarea id="rootCauses" name="rootCauses" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contributingFactors">العوامل التي ساهمت في وقوع الحادث</label>
                                <textarea id="contributingFactors" name="contributingFactors" rows="4" required></textarea>
                            </div>
                        </div>

                        <!-- Recovery Plans -->
                        <div class="form-section">
                            <h3><i class="fas fa-redo"></i> خطط الاسترداد</h3>
                            <div class="form-group">
                                <label for="recoveryPlansActivated">خطط الاسترداد التي تم تفعيلها</label>
                                <textarea id="recoveryPlansActivated" name="recoveryPlansActivated" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="recoveryPlansSuitability">مدى ملائمة خطط الاسترداد لمعالجة أثر الحادث</label>
                                <textarea id="recoveryPlansSuitability" name="recoveryPlansSuitability" rows="4" required></textarea>
                            </div>
                        </div>

                        <!-- Corrective Actions -->
                        <div class="form-section">
                            <h3><i class="fas fa-tools"></i> الإجراءات التصحيحية</h3>
                            <div class="form-group">
                                <label for="correctiveActions">الإجراءات التصحيحية التي تم اتخاذها لمنع وقوع حوادث مماثلة في المستقبل</label>
                                <textarea id="correctiveActions" name="correctiveActions" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="actionResponsible">المسؤول عن تنفيذ الإجراءات</label>
                                    <input type="text" id="actionResponsible" name="actionResponsible" required>
                                </div>
                                <div class="form-group">
                                    <label for="implementationPeriod">فترة تنفيذ هذه الإجراءات (الوقت المفضل للتنفيذ)</label>
                                    <input type="text" id="implementationPeriod" name="implementationPeriod" required>
                                </div>
                            </div>
                        </div>

                        <!-- Follow-up Mechanism -->
                        <div class="form-section">
                            <h3><i class="fas fa-eye"></i> آلية المتابعة</h3>
                            <div class="form-group">
                                <label for="followUpMechanism">آلية متابعة الإجراءات التصحيحية لضمان تنفيذها بشكل صحيح</label>
                                <textarea id="followUpMechanism" name="followUpMechanism" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="followUpResponsible">المسؤول عن متابعة الإجراءات التصحيحية</label>
                                    <input type="text" id="followUpResponsible" name="followUpResponsible" required>
                                </div>
                                <div class="form-group">
                                    <label for="followUpTiming">توقيت إجراء المتابعة</label>
                                    <input type="text" id="followUpTiming" name="followUpTiming" required>
                                </div>
                            </div>
                        </div>

                        <!-- Lessons Learned -->
                        <div class="form-section">
                            <h3><i class="fas fa-graduation-cap"></i> الدروس المستفادة</h3>
                            <div class="form-group">
                                <label for="lessonsLearned">الدروس المستفادة من هذا الحادث</label>
                                <textarea id="lessonsLearned" name="lessonsLearned" rows="6" required></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المراجعة
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                            <button type="button" class="btn btn-info" id="generateReportBtn">
                                <i class="fas fa-file-pdf"></i> إنشاء تقرير
                            </button>
                        </div>
                    </form>

                    <!-- Saved Reviews Section -->
                    <div class="saved-reviews-section" id="savedReviewsSection" style="display: none;">
                        <div class="section-header">
                            <h3><i class="fas fa-archive"></i> المراجعات المحفوظة</h3>
                            <button class="btn btn-primary" id="backToFormBtn">
                                <i class="fas fa-plus"></i>
                                إضافة مراجعة جديدة
                            </button>
                        </div>

                        <div class="reviews-table-container">
                            <table class="reviews-table">
                                <thead>
                                    <tr>
                                        <th>رقم الحادث</th>
                                        <th>اسم المراجع</th>
                                        <th>تاريخ الحادث</th>
                                        <th>نوع الحادث</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="reviewsTableBody">
                                    <!-- Reviews will be rendered here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>

            <!-- User Management Section -->
            <div class="content-section" id="user-management-section">
                <section class="user-management">
                    <div class="section-header">
                        <h2><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                        <button class="btn btn-primary" id="addUserBtn">
                            <i class="fas fa-user-plus"></i>
                            إضافة مستخدم جديد
                        </button>
                    </div>

                    <div class="users-grid" id="usersGrid">
                        <!-- Users will be populated here -->
                    </div>
                </section>
            </div>
        </main>

        <!-- Floating Action Button -->
        <button class="fab" id="scrollToTopBtn" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    <!-- أزرار إضافية في أسفل الصفحة -->
    <!-- زر استيراد النسخة الاحتياطية -->
    <button class="btn btn-info" id="restoreBtnFooter" style="position: fixed; left: 140px; bottom: 20px; z-index: 9999;" title="استيراد نسخة احتياطية">
        <i class="fas fa-upload"></i>
    </button>

    <!-- زر النسخة الاحتياطية -->
    <button class="btn btn-success" id="backupBtnFooter" style="position: fixed; left: 80px; bottom: 20px; z-index: 9999;" title="إنشاء نسخة احتياطية">
        <i class="fas fa-download"></i>
    </button>

    <!-- زر تسجيل خروج -->
    <button class="btn btn-danger" id="logoutBtnFooter" style="position: fixed; left: 20px; bottom: 20px; z-index: 9999;" title="تسجيل الخروج">
        <i class="fas fa-sign-out-alt"></i>
    </button>

    <!-- حقل إدخال الملف المخفي لاستيراد النسخة الاحتياطية -->
    <input type="file" id="restoreFileInput" accept=".json" style="display: none;">

    <style>
    #logoutBtnFooter, #backupBtnFooter, #restoreBtnFooter {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        padding: 0;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
        transition: all 0.3s ease;
    }

    #logoutBtnFooter i, #backupBtnFooter i, #restoreBtnFooter i {
        margin: 0;
    }

    #logoutBtnFooter:hover, #backupBtnFooter:hover, #restoreBtnFooter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    #backupBtnFooter {
        background-color: #28a745;
        border-color: #28a745;
    }

    #backupBtnFooter:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    #restoreBtnFooter {
        background-color: #17a2b8;
        border-color: #17a2b8;
    }

    #restoreBtnFooter:hover {
        background-color: #138496;
        border-color: #117a8b;
    }

    /* Backup Management Section Styles */
    .backup-management-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
    }

    .backup-controls {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
        align-items: start;
    }

    .backup-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .backup-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border-radius: 12px;
        transition: all 0.3s ease;
        text-align: center;
        min-height: 120px;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .backup-action-btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .backup-action-btn:hover:before {
        left: 100%;
    }

    .backup-action-btn i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .backup-action-btn span {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .backup-action-btn small {
        font-size: 0.85rem;
        opacity: 0.8;
        font-weight: normal;
    }

    .backup-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .backup-info {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .info-card {
        display: flex;
        align-items: flex-start;
        gap: 15px;
    }

    .info-card i {
        color: #17a2b8;
        font-size: 1.5rem;
        margin-top: 5px;
    }

    .info-content h4 {
        margin: 0 0 10px 0;
        color: #495057;
        font-size: 1.1rem;
    }

    .info-content ul {
        margin: 0;
        padding-right: 20px;
        list-style-type: disc;
    }

    .info-content li {
        margin-bottom: 8px;
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .backup-status {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .status-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .status-item i {
        color: #28a745;
        font-size: 1rem;
        width: 16px;
    }

    .status-item span:first-of-type {
        color: #495057;
        font-weight: 500;
    }

    .status-item span:last-of-type {
        color: #6c757d;
        margin-right: auto;
    }

    .toggle-section-btn {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .toggle-section-btn:hover {
        color: #495057;
        background-color: rgba(0,0,0,0.05);
    }

    .toggle-section-btn i {
        transition: transform 0.3s ease;
    }

    .backup-controls.collapsed {
        display: none;
    }

    .toggle-section-btn.collapsed i {
        transform: rotate(180deg);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .backup-controls {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .backup-actions {
            grid-template-columns: 1fr;
        }

        .backup-action-btn {
            min-height: 100px;
            padding: 15px;
        }

        .backup-action-btn i {
            font-size: 2rem;
        }
    }
    </style>
    </div>

    <!-- Event Details Modal -->
    <div class="modal" id="eventModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل الحدث</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Event details will be populated here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="editEventBtn">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger" id="deleteEventBtn">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <button class="btn btn-secondary" id="closeModalBtn">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- User Management Modal -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">إضافة مستخدم جديد</h3>
                <button class="modal-close" id="userModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="user-form" id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userFullName">الاسم الكامل</label>
                            <input type="text" id="userFullName" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label for="userUsername">اسم المستخدم</label>
                            <input type="text" id="userUsername" name="username" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userEmail">البريد الإلكتروني</label>
                            <input type="email" id="userEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="userRole">الدور</label>
                            <select id="userRole" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="analyst">محلل أمني</option>
                                <option value="operator">مشغل</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="userPassword">كلمة المرور</label>
                        <input type="password" id="userPassword" name="password" required>
                    </div>
                    <div class="form-group">
                        <label>الصلاحيات</label>
                        <div class="permissions-grid">
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="read">
                                <span>قراءة</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="write">
                                <span>كتابة</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="delete">
                                <span>حذف</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="view_analytics">
                                <span>عرض الإحصائيات</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="manage_users">
                                <span>إدارة المستخدمين</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="manage_system">
                                <span>إدارة النظام</span>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="saveUserBtn">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="btn btn-secondary" id="cancelUserBtn">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Event Type Modal -->
    <div class="modal" id="eventTypeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="eventTypeModalTitle">إضافة نوع حدث جديد</h3>
                <button class="modal-close" onclick="securityManager.closeEventTypeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="eventTypeForm" onsubmit="securityManager.saveEventType(event)">
                <div class="form-group">
                    <label for="eventTypeKey">مفتاح النوع (بالإنجليزية)</label>
                    <input type="text" id="eventTypeKey" name="eventTypeKey" required
                           placeholder="مثال: malware, phishing, intrusion">
                    <small class="form-help">يستخدم في النظام ولا يمكن تغييره بعد الحفظ</small>
                </div>
                <div class="form-group">
                    <label for="eventTypeLabel">اسم النوع (بالعربية)</label>
                    <input type="text" id="eventTypeLabel" name="eventTypeLabel" required
                           placeholder="مثال: برمجية خبيثة، تصيد إلكتروني، اختراق">
                </div>
                <div class="form-group">
                    <label for="eventTypeDescription">وصف النوع</label>
                    <textarea id="eventTypeDescription" name="eventTypeDescription" rows="3"
                              placeholder="وصف مختصر لنوع الحدث..."></textarea>
                </div>
                <div class="form-group">
                    <label for="eventTypeIcon">أيقونة النوع</label>
                    <select id="eventTypeIcon" name="eventTypeIcon" required>
                        <option value="">اختر أيقونة</option>
                        <option value="fas fa-virus">🦠 فيروس/برمجية خبيثة</option>
                        <option value="fas fa-fishing">🎣 تصيد إلكتروني</option>
                        <option value="fas fa-user-secret">🕵️ اختراق</option>
                        <option value="fas fa-database">💾 تسريب بيانات</option>
                        <option value="fas fa-shield-alt">🛡️ خرق أمني</option>
                        <option value="fas fa-exclamation-triangle">⚠️ تهديد أمني</option>
                        <option value="fas fa-bug">🐛 ثغرة أمنية</option>
                        <option value="fas fa-lock">🔒 مشكلة تشفير</option>
                        <option value="fas fa-network-wired">🌐 مشكلة شبكة</option>
                        <option value="fas fa-server">🖥️ مشكلة خادم</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="eventTypeColor">لون النوع</label>
                    <select id="eventTypeColor" name="eventTypeColor" required>
                        <option value="">اختر لون</option>
                        <option value="#dc2626">🔴 أحمر (خطر عالي)</option>
                        <option value="#ea580c">🟠 برتقالي (تحذير)</option>
                        <option value="#d97706">🟡 أصفر (انتباه)</option>
                        <option value="#2563eb">🔵 أزرق (معلومات)</option>
                        <option value="#7c3aed">🟣 بنفسجي (خاص)</option>
                        <option value="#059669">🟢 أخضر (آمن)</option>
                        <option value="#6b7280">⚫ رمادي (عام)</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="securityManager.closeEventTypeModal()">
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Review Details Modal -->
    <div class="modal" id="reviewModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-clipboard-check"></i> تفاصيل المراجعة</h3>
                <button class="modal-close" id="reviewModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="reviewModalBody">
                <!-- Review details will be populated here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-info" id="printReviewBtn">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-primary" id="editReviewFromModalBtn">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-secondary" id="closeReviewModalBtn">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div class="modal" id="userProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-edit"></i> الملف الشخصي</h3>
                <button class="modal-close" id="userProfileModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-tabs">
                    <button class="tab-btn active" data-tab="profile-info">
                        <i class="fas fa-user"></i>
                        المعلومات الشخصية
                    </button>
                    <button class="tab-btn" data-tab="change-password">
                        <i class="fas fa-key"></i>
                        تغيير كلمة المرور
                    </button>
                </div>

                <!-- Profile Information Tab -->
                <div class="tab-content active" id="profile-info">
                    <form class="profile-form" id="profileInfoForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="profileFullName">الاسم الكامل</label>
                                <input type="text" id="profileFullName" name="fullName" required>
                            </div>
                            <div class="form-group">
                                <label for="profileUsername">اسم المستخدم</label>
                                <input type="text" id="profileUsername" name="username" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="profileEmail">البريد الإلكتروني</label>
                            <input type="email" id="profileEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="profileRole">الدور</label>
                            <input type="text" id="profileRole" name="role" readonly>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Change Password Tab -->
                <div class="tab-content" id="change-password">
                    <form class="password-form" id="changePasswordForm">
                        <div class="form-group">
                            <label for="currentPassword">كلمة المرور الحالية</label>
                            <div class="password-input-container">
                                <input type="password" id="currentPassword" name="currentPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">كلمة المرور الجديدة</label>
                            <div class="password-input-container">
                                <input type="password" id="newPassword" name="newPassword" required minlength="6">
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="form-help">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور الجديدة</label>
                            <div class="password-input-container">
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">قوة كلمة المرور</span>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Change Password Modal -->
    <div class="modal" id="quickChangePasswordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-key"></i> تغيير كلمة المرور</h3>
                <button class="modal-close" id="quickChangePasswordModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="password-form" id="quickChangePasswordForm">
                    <div class="form-group">
                        <label for="quickCurrentPassword">كلمة المرور الحالية</label>
                        <div class="password-input-container">
                            <input type="password" id="quickCurrentPassword" name="currentPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('quickCurrentPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="quickNewPassword">كلمة المرور الجديدة</label>
                        <div class="password-input-container">
                            <input type="password" id="quickNewPassword" name="newPassword" required minlength="6">
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('quickNewPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="form-help">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                    </div>
                    <div class="form-group">
                        <label for="quickConfirmPassword">تأكيد كلمة المرور الجديدة</label>
                        <div class="password-input-container">
                            <input type="password" id="quickConfirmPassword" name="confirmPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('quickConfirmPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-strength" id="quickPasswordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <span class="strength-text">قوة كلمة المرور</span>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeQuickChangePasswordModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="fixed-auth-system.js"></script>
    <script src="data-sync-client.js"></script>
    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script src="demo-data.js"></script>

    <script>
        // وظيفة إغلاق نافذة تغيير كلمة المرور السريعة
        function closeQuickChangePasswordModal() {
            if (securityManager) {
                securityManager.closeQuickChangePasswordModal();
            }
        }

        // وظيفة النسخة الاحتياطية
        function createBackup() {
            try {
                console.log('🔄 بدء إنشاء النسخة الاحتياطية...');

                // جمع جميع البيانات من localStorage
                const backupData = {
                    timestamp: new Date().toISOString(),
                    version: '2.0',
                    systemInfo: {
                        userAgent: navigator.userAgent,
                        url: window.location.href,
                        language: navigator.language
                    },
                    data: {}
                };

                // جمع البيانات من جميع الشاشات
                const importantKeys = [
                    // بيانات المستخدمين والجلسات
                    'systemUsers',
                    'userSessions',
                    'currentSession',

                    // شاشة الأحداث الأمنية
                    'securityEvents',
                    'currentEventId',
                    'eventTypes',

                    // شاشة مراجعة ما بعد الحادث
                    'postIncidentReviews',

                    // شاشة تحليل المخاطر
                    'riskAssessments',
                    'riskAnalysisData',
                    'securityRecommendations',

                    // شاشة الإحصائيات والتحليلات
                    'analyticsData',
                    'chartData',
                    'statisticsCache',

                    // شاشة إدارة أنواع الأحداث
                    'customEventTypes',
                    'eventTypeSettings',

                    // شاشة إدارة المستخدمين
                    'userManagementSettings',
                    'userPermissions',
                    'userRoles',

                    // إعدادات النظام العامة
                    'systemSettings',
                    'theme',
                    'userPreferences',
                    'applicationSettings',

                    // سجلات الأنشطة والتدقيق
                    'activityLog',
                    'auditTrails',
                    'systemLogs',
                    'accessLogs',

                    // بيانات التقارير والامتثال
                    'complianceData',
                    'incidentReports',
                    'securityReports',
                    'auditReports',

                    // بيانات المزامنة والشبكة
                    'syncData',
                    'networkConfig',
                    'lastSyncTime',

                    // بيانات النسخ الاحتياطية
                    'lastBackupTime',
                    'backupHistory',
                    'backupSettings'
                ];

                // إحصائيات مفصلة عن البيانات
                const dataStats = {
                    totalKeys: 0,
                    dataByCategory: {
                        users: 0,
                        events: 0,
                        reviews: 0,
                        risks: 0,
                        analytics: 0,
                        settings: 0,
                        logs: 0,
                        reports: 0,
                        sync: 0,
                        backup: 0
                    }
                };

                importantKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) {
                        try {
                            const parsedValue = JSON.parse(value);
                            backupData.data[key] = parsedValue;

                            // تصنيف البيانات
                            if (key.includes('User') || key.includes('Session')) {
                                dataStats.dataByCategory.users++;
                            } else if (key.includes('Event') || key.includes('security')) {
                                dataStats.dataByCategory.events++;
                            } else if (key.includes('Review') || key.includes('Incident')) {
                                dataStats.dataByCategory.reviews++;
                            } else if (key.includes('Risk') || key.includes('risk')) {
                                dataStats.dataByCategory.risks++;
                            } else if (key.includes('analytics') || key.includes('Chart') || key.includes('statistics')) {
                                dataStats.dataByCategory.analytics++;
                            } else if (key.includes('Settings') || key.includes('theme') || key.includes('Preferences')) {
                                dataStats.dataByCategory.settings++;
                            } else if (key.includes('Log') || key.includes('audit') || key.includes('activity')) {
                                dataStats.dataByCategory.logs++;
                            } else if (key.includes('Report') || key.includes('compliance')) {
                                dataStats.dataByCategory.reports++;
                            } else if (key.includes('sync') || key.includes('network')) {
                                dataStats.dataByCategory.sync++;
                            } else if (key.includes('backup') || key.includes('Backup')) {
                                dataStats.dataByCategory.backup++;
                            }

                        } catch (e) {
                            backupData.data[key] = value;
                        }
                        dataStats.totalKeys++;
                    }
                });

                // إضافة الإحصائيات للنسخة الاحتياطية
                backupData.statistics = dataStats;

                // إضافة بيانات المزامنة إذا كانت متاحة
                if (window.networkSyncClient && window.networkSyncClient.localData) {
                    backupData.syncData = window.networkSyncClient.localData;
                }

                // إنشاء اسم الملف
                const now = new Date();
                const dateStr = now.toISOString().split('T')[0];
                const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
                const filename = `ISMS-Backup-${dateStr}-${timeStr}.json`;

                // تحويل البيانات إلى JSON
                const jsonString = JSON.stringify(backupData, null, 2);

                // إنشاء وتحميل الملف
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // إظهار رسالة نجاح مع الإحصائيات
                const successMessage = `✅ تم إنشاء النسخة الاحتياطية الشاملة بنجاح!\n\n` +
                                      `📊 إحصائيات النسخة الاحتياطية:\n` +
                                      `📁 اسم الملف: ${filename}\n` +
                                      `📈 إجمالي أنواع البيانات: ${dataStats.totalKeys}\n` +
                                      `👥 بيانات المستخدمين: ${dataStats.dataByCategory.users}\n` +
                                      `🛡️ الأحداث الأمنية: ${dataStats.dataByCategory.events}\n` +
                                      `📋 المراجعات: ${dataStats.dataByCategory.reviews}\n` +
                                      `⚠️ تحليل المخاطر: ${dataStats.dataByCategory.risks}\n` +
                                      `📊 التحليلات: ${dataStats.dataByCategory.analytics}\n` +
                                      `⚙️ الإعدادات: ${dataStats.dataByCategory.settings}\n` +
                                      `📝 السجلات: ${dataStats.dataByCategory.logs}\n` +
                                      `📄 التقارير: ${dataStats.dataByCategory.reports}\n` +
                                      `🔄 المزامنة: ${dataStats.dataByCategory.sync}\n` +
                                      `💾 النسخ الاحتياطية: ${dataStats.dataByCategory.backup}`;

                showNotification(successMessage, 'success');
                console.log('✅ تم إنشاء النسخة الاحتياطية الشاملة:', filename);
                console.log('📊 إحصائيات البيانات:', dataStats);

                // حفظ وقت آخر نسخة احتياطية
                localStorage.setItem('lastBackupTime', backupData.timestamp);

                // تسجيل العملية في سجل الأنشطة
                if (typeof logActivity === 'function') {
                    logActivity('backup_created', 'تم إنشاء نسخة احتياطية', {
                        filename: filename,
                        dataSize: jsonString.length,
                        keysCount: Object.keys(backupData.data).length
                    });
                }

                // تحديث معلومات النسخ الاحتياطية
                if (typeof updateBackupStatus === 'function') {
                    updateBackupStatus();
                }

                return true;

            } catch (error) {
                console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
                showNotification('❌ فشل في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
                return false;
            }
        }

        // وظيفة عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                max-width: 350px;
                font-size: 14px;
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                animation: slideInRight 0.3s ease-out;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: inherit;
                        cursor: pointer;
                        font-size: 16px;
                        padding: 0;
                        margin-left: auto;
                    ">×</button>
                </div>
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        // إضافة CSS للرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // وظيفة تحديث معلومات حالة النسخ الاحتياطية
        function updateBackupStatus() {
            try {
                // حساب حجم البيانات من جميع الشاشات
                let totalSize = 0;
                const importantKeys = [
                    // بيانات المستخدمين والجلسات
                    'systemUsers', 'userSessions', 'currentSession',

                    // شاشة الأحداث الأمنية
                    'securityEvents', 'currentEventId', 'eventTypes',

                    // شاشة مراجعة ما بعد الحادث
                    'postIncidentReviews',

                    // شاشة تحليل المخاطر
                    'riskAssessments', 'riskAnalysisData', 'securityRecommendations',

                    // شاشة الإحصائيات والتحليلات
                    'analyticsData', 'chartData', 'statisticsCache',

                    // شاشة إدارة أنواع الأحداث
                    'customEventTypes', 'eventTypeSettings',

                    // شاشة إدارة المستخدمين
                    'userManagementSettings', 'userPermissions', 'userRoles',

                    // إعدادات النظام العامة
                    'systemSettings', 'theme', 'userPreferences', 'applicationSettings',

                    // سجلات الأنشطة والتدقيق
                    'activityLog', 'auditTrails', 'systemLogs', 'accessLogs',

                    // بيانات التقارير والامتثال
                    'complianceData', 'incidentReports', 'securityReports', 'auditReports',

                    // بيانات المزامنة والشبكة
                    'syncData', 'networkConfig', 'lastSyncTime',

                    // بيانات النسخ الاحتياطية
                    'lastBackupTime', 'backupHistory', 'backupSettings'
                ];

                importantKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) {
                        totalSize += new Blob([value]).size;
                    }
                });

                // تحديث حجم البيانات
                const dataSizeElement = document.getElementById('dataSize');
                if (dataSizeElement) {
                    if (totalSize > 1024 * 1024) {
                        dataSizeElement.textContent = `${(totalSize / (1024 * 1024)).toFixed(2)} MB`;
                    } else if (totalSize > 1024) {
                        dataSizeElement.textContent = `${(totalSize / 1024).toFixed(2)} KB`;
                    } else {
                        dataSizeElement.textContent = `${totalSize} Bytes`;
                    }
                }

                // تحديث وقت آخر نسخة احتياطية
                const lastBackupTime = localStorage.getItem('lastBackupTime');
                const lastBackupTimeElement = document.getElementById('lastBackupTime');
                if (lastBackupTimeElement) {
                    if (lastBackupTime) {
                        const backupDate = new Date(lastBackupTime);
                        lastBackupTimeElement.textContent = backupDate.toLocaleString('ar-SA');
                    } else {
                        lastBackupTimeElement.textContent = 'لم يتم إنشاء نسخة بعد';
                    }
                }

            } catch (error) {
                console.error('خطأ في تحديث معلومات النسخ الاحتياطية:', error);
            }
        }

        // ربط أزرار النسخة الاحتياطية بالوظيفة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث معلومات النسخ الاحتياطية عند تحميل الصفحة
            updateBackupStatus();

            // تحديث المعلومات كل 30 ثانية
            setInterval(updateBackupStatus, 30000);

            // زر طي/إظهار قسم النسخ الاحتياطية
            const toggleBackupSection = document.getElementById('toggleBackupSection');
            const backupControls = document.querySelector('.backup-controls');

            if (toggleBackupSection && backupControls) {
                // استعادة حالة القسم من localStorage
                const isCollapsed = localStorage.getItem('backupSectionCollapsed') === 'true';
                if (isCollapsed) {
                    backupControls.classList.add('collapsed');
                    toggleBackupSection.classList.add('collapsed');
                }

                toggleBackupSection.addEventListener('click', function() {
                    const isCurrentlyCollapsed = backupControls.classList.contains('collapsed');

                    if (isCurrentlyCollapsed) {
                        backupControls.classList.remove('collapsed');
                        toggleBackupSection.classList.remove('collapsed');
                        localStorage.setItem('backupSectionCollapsed', 'false');
                    } else {
                        backupControls.classList.add('collapsed');
                        toggleBackupSection.classList.add('collapsed');
                        localStorage.setItem('backupSectionCollapsed', 'true');
                    }
                });
            }
            // زر النسخة الاحتياطية في القائمة المنسدلة
            const backupBtn = document.getElementById('backupBtn');
            if (backupBtn) {
                backupBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // تأكيد من المستخدم مع رسالة شاملة
                    const confirmMessage = 'إنشاء نسخة احتياطية شاملة من جميع الشاشات؟\n\n' +
                                         'سيتم حفظ البيانات من:\n' +
                                         '🔐 • بيانات المستخدمين والجلسات\n' +
                                         '🛡️ • الأحداث الأمنية وأنواعها\n' +
                                         '📋 • مراجعات ما بعد الحادث\n' +
                                         '⚠️ • تحليل المخاطر والتوصيات\n' +
                                         '📊 • الإحصائيات والتحليلات\n' +
                                         '⚙️ • إعدادات النظام والمستخدمين\n' +
                                         '📝 • سجلات الأنشطة والتدقيق\n' +
                                         '📄 • التقارير وبيانات الامتثال\n' +
                                         '🔄 • بيانات المزامنة والشبكة\n\n' +
                                         'هل تريد المتابعة؟';

                    if (confirm(confirmMessage)) {
                        createBackup();
                    }
                });
                backupBtn.title = 'إنشاء نسخة احتياطية من جميع بيانات النظام';
            }

            // زر النسخة الاحتياطية في أسفل الصفحة
            const backupBtnFooter = document.getElementById('backupBtnFooter');
            if (backupBtnFooter) {
                backupBtnFooter.addEventListener('click', function(e) {
                    e.preventDefault();

                    // تأكيد من المستخدم مع رسالة أكثر تفصيلاً
                    const confirmMessage = 'إنشاء نسخة احتياطية شاملة من جميع الشاشات؟\n\n' +
                                         'سيتم حفظ البيانات من:\n' +
                                         '🔐 • بيانات المستخدمين والجلسات\n' +
                                         '🛡️ • الأحداث الأمنية وأنواعها\n' +
                                         '📋 • مراجعات ما بعد الحادث\n' +
                                         '⚠️ • تحليل المخاطر والتوصيات\n' +
                                         '📊 • الإحصائيات والتحليلات\n' +
                                         '⚙️ • إعدادات النظام والمستخدمين\n' +
                                         '📝 • سجلات الأنشطة والتدقيق\n' +
                                         '📄 • التقارير وبيانات الامتثال\n' +
                                         '🔄 • بيانات المزامنة والشبكة\n\n' +
                                         'هل تريد المتابعة؟';

                    if (confirm(confirmMessage)) {
                        createBackup();
                    }
                });
            }

            // زر إنشاء النسخة الاحتياطية في الشاشة الرئيسية
            const createBackupBtn = document.getElementById('createBackupBtn');
            if (createBackupBtn) {
                createBackupBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // تأكيد من المستخدم مع رسالة أكثر تفصيلاً
                    const confirmMessage = 'إنشاء نسخة احتياطية شاملة من جميع الشاشات؟\n\n' +
                                         'سيتم حفظ البيانات من:\n' +
                                         '🔐 • بيانات المستخدمين والجلسات\n' +
                                         '🛡️ • الأحداث الأمنية وأنواعها\n' +
                                         '📋 • مراجعات ما بعد الحادث\n' +
                                         '⚠️ • تحليل المخاطر والتوصيات\n' +
                                         '📊 • الإحصائيات والتحليلات\n' +
                                         '⚙️ • إعدادات النظام والمستخدمين\n' +
                                         '📝 • سجلات الأنشطة والتدقيق\n' +
                                         '📄 • التقارير وبيانات الامتثال\n' +
                                         '🔄 • بيانات المزامنة والشبكة\n\n' +
                                         'هل تريد المتابعة؟';

                    if (confirm(confirmMessage)) {
                        createBackup();
                    }
                });
            }

            // زر استيراد النسخة الاحتياطية في الشاشة الرئيسية
            const importBackupBtn = document.getElementById('importBackupBtn');
            if (importBackupBtn) {
                importBackupBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('restoreFileInput').click();
                });
            }

            // زر مزامنة البيانات
            const syncDataBtn = document.getElementById('syncDataBtn');
            if (syncDataBtn) {
                syncDataBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    if (window.networkSyncClient) {
                        showNotification('🔄 جاري مزامنة البيانات...', 'info');

                        // محاولة المزامنة
                        try {
                            window.networkSyncClient.syncData();
                            showNotification('✅ تم مزامنة البيانات بنجاح!', 'success');
                        } catch (error) {
                            console.error('خطأ في المزامنة:', error);
                            showNotification('❌ فشل في مزامنة البيانات', 'error');
                        }
                    } else {
                        showNotification('⚠️ خدمة المزامنة غير متاحة', 'warning');
                    }
                });
            }

            // زر تسجيل الخروج في أسفل الصفحة
            const logoutBtnFooter = document.getElementById('logoutBtnFooter');
            if (logoutBtnFooter) {
                logoutBtnFooter.addEventListener('click', function(e) {
                    e.preventDefault();

                    // استخدام وظيفة تسجيل الخروج من نظام المصادقة
                    if (window.fixedAuthManager && typeof window.fixedAuthManager.logout === 'function') {
                        window.fixedAuthManager.logout();
                    } else if (window.authManager && typeof window.authManager.logout === 'function') {
                        window.authManager.logout();
                    } else {
                        // تسجيل خروج افتراضي
                        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                            localStorage.removeItem('currentSession');
                            window.location.href = 'login.html';
                        }
                    }
                });
            }

            // زر استيراد النسخة الاحتياطية في القائمة المنسدلة
            const restoreBtn = document.getElementById('restoreBtn');
            if (restoreBtn) {
                restoreBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('restoreFileInput').click();
                });
            }

            // زر استيراد النسخة الاحتياطية في أسفل الصفحة
            const restoreBtnFooter = document.getElementById('restoreBtnFooter');
            if (restoreBtnFooter) {
                restoreBtnFooter.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('restoreFileInput').click();
                });
            }



            // حقل إدخال ملف النسخة الاحتياطية
            const restoreFileInput = document.getElementById('restoreFileInput');
            if (restoreFileInput) {
                restoreFileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        handleRestoreFile(file);
                    }
                    // إعادة تعيين قيمة الحقل للسماح بتحديد نفس الملف مرة أخرى
                    e.target.value = '';
                });
            }
        });

        // وظيفة معالجة ملف النسخة الاحتياطية
        function handleRestoreFile(file) {
            // التحقق من نوع الملف
            if (!file.name.endsWith('.json')) {
                showNotification('❌ يرجى اختيار ملف نسخة احتياطية صحيح (.json)', 'error');
                return;
            }

            // عرض رسالة تأكيد مع تفاصيل أكثر
            const confirmMessage = `استيراد النسخة الاحتياطية الشاملة؟\n\n` +
                                 `📁 اسم الملف: ${file.name}\n` +
                                 `📊 حجم الملف: ${(file.size / 1024).toFixed(2)} KB\n\n` +
                                 `⚠️ تحذير: سيتم استبدال جميع البيانات الحالية من كل الشاشات!\n\n` +
                                 `البيانات التي سيتم استبدالها:\n` +
                                 `🔐 • بيانات المستخدمين والجلسات\n` +
                                 `🛡️ • الأحداث الأمنية وأنواعها\n` +
                                 `📋 • مراجعات ما بعد الحادث\n` +
                                 `⚠️ • تحليل المخاطر والتوصيات\n` +
                                 `📊 • الإحصائيات والتحليلات\n` +
                                 `⚙️ • إعدادات النظام والمستخدمين\n` +
                                 `📝 • سجلات الأنشطة والتدقيق\n` +
                                 `📄 • التقارير وبيانات الامتثال\n` +
                                 `🔄 • بيانات المزامنة والشبكة\n\n` +
                                 `هل تريد المتابعة؟`;

            if (confirm(confirmMessage)) {
                restoreBackup(file);
            }
        }

        // وظيفة استعادة النسخة الاحتياطية المحسنة
        function restoreBackup(file) {
            return new Promise((resolve, reject) => {
                console.log('🔄 بدء استيراد النسخة الاحتياطية...');
                showNotification('🔄 جاري استيراد النسخة الاحتياطية...', 'info');

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const backupData = JSON.parse(e.target.result);
                        console.log('📄 تم قراءة ملف النسخة الاحتياطية:', backupData);

                        // التحقق من صحة البيانات
                        if (!backupData.data || !backupData.timestamp) {
                            throw new Error('ملف النسخة الاحتياطية غير صحيح أو تالف');
                        }

                        // التحقق من إصدار النسخة الاحتياطية
                        if (backupData.version && backupData.version !== '2.0') {
                            console.warn('⚠️ إصدار النسخة الاحتياطية مختلف:', backupData.version);
                        }

                        // عرض معلومات النسخة الاحتياطية
                        const backupDate = new Date(backupData.timestamp).toLocaleString('ar-SA');
                        const dataKeysCount = Object.keys(backupData.data).length;

                        console.log(`📅 تاريخ النسخة الاحتياطية: ${backupDate}`);
                        console.log(`📊 عدد أنواع البيانات: ${dataKeysCount}`);

                        // حفظ نسخة احتياطية من البيانات الحالية قبل الاستيراد
                        const currentBackup = {};
                        Object.keys(backupData.data).forEach(key => {
                            const currentValue = localStorage.getItem(key);
                            if (currentValue) {
                                currentBackup[key] = currentValue;
                            }
                        });

                        try {
                            // استعادة البيانات
                            let restoredCount = 0;
                            Object.keys(backupData.data).forEach(key => {
                                const value = backupData.data[key];
                                if (value !== null && value !== undefined) {
                                    localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
                                    restoredCount++;
                                    console.log(`✅ تم استعادة: ${key}`);
                                }
                            });

                            // استعادة بيانات المزامنة إذا كانت متوفرة
                            if (backupData.syncData && window.networkSyncClient) {
                                try {
                                    window.networkSyncClient.localData = backupData.syncData;
                                    console.log('🔄 تم استعادة بيانات المزامنة');
                                } catch (syncError) {
                                    console.warn('⚠️ فشل في استعادة بيانات المزامنة:', syncError);
                                }
                            }

                            // تسجيل العملية في سجل الأنشطة
                            if (typeof logActivity === 'function') {
                                logActivity('backup_restored', `تم استيراد نسخة احتياطية من ${file.name}`, {
                                    filename: file.name,
                                    backupDate: backupDate,
                                    restoredKeysCount: restoredCount,
                                    fileSize: file.size
                                });
                            }

                            // عرض إحصائيات مفصلة عن الاستعادة
                            let restoreMessage = `✅ تم استيراد النسخة الاحتياطية الشاملة بنجاح!\n\n`;
                            restoreMessage += `📊 إحصائيات الاستعادة:\n`;
                            restoreMessage += `📈 إجمالي أنواع البيانات المستعادة: ${restoredCount}\n`;
                            restoreMessage += `📅 تاريخ النسخة الاحتياطية: ${backupDate}\n`;

                            // إضافة إحصائيات مفصلة إذا كانت متوفرة
                            if (backupData.statistics) {
                                const stats = backupData.statistics.dataByCategory;
                                restoreMessage += `\n📋 تفاصيل البيانات المستعادة:\n`;
                                if (stats.users > 0) restoreMessage += `👥 بيانات المستخدمين: ${stats.users}\n`;
                                if (stats.events > 0) restoreMessage += `🛡️ الأحداث الأمنية: ${stats.events}\n`;
                                if (stats.reviews > 0) restoreMessage += `📋 المراجعات: ${stats.reviews}\n`;
                                if (stats.risks > 0) restoreMessage += `⚠️ تحليل المخاطر: ${stats.risks}\n`;
                                if (stats.analytics > 0) restoreMessage += `📊 التحليلات: ${stats.analytics}\n`;
                                if (stats.settings > 0) restoreMessage += `⚙️ الإعدادات: ${stats.settings}\n`;
                                if (stats.logs > 0) restoreMessage += `📝 السجلات: ${stats.logs}\n`;
                                if (stats.reports > 0) restoreMessage += `📄 التقارير: ${stats.reports}\n`;
                                if (stats.sync > 0) restoreMessage += `🔄 المزامنة: ${stats.sync}\n`;
                                if (stats.backup > 0) restoreMessage += `💾 النسخ الاحتياطية: ${stats.backup}\n`;
                            }

                            showNotification(restoreMessage, 'success');

                            console.log('✅ تم استيراد النسخة الاحتياطية بنجاح');

                            // إعادة تحميل الصفحة بعد تأخير قصير
                            setTimeout(() => {
                                console.log('🔄 إعادة تحميل الصفحة...');
                                location.reload();
                            }, 3000);

                            resolve(true);

                        } catch (restoreError) {
                            // في حالة فشل الاستعادة، استعادة البيانات الأصلية
                            console.error('❌ فشل في استعادة البيانات، استعادة النسخة الأصلية...');
                            Object.keys(currentBackup).forEach(key => {
                                localStorage.setItem(key, currentBackup[key]);
                            });
                            throw restoreError;
                        }

                    } catch (error) {
                        console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
                        let errorMessage = 'فشل في استيراد النسخة الاحتياطية';

                        if (error.message.includes('JSON')) {
                            errorMessage = 'ملف النسخة الاحتياطية تالف أو غير صحيح';
                        } else if (error.message.includes('غير صحيح')) {
                            errorMessage = error.message;
                        }

                        showNotification(`❌ ${errorMessage}`, 'error');
                        reject(error);
                    }
                };

                reader.onerror = function() {
                    const error = new Error('فشل في قراءة الملف');
                    console.error('❌ خطأ في قراءة الملف:', error);
                    showNotification('❌ فشل في قراءة ملف النسخة الاحتياطية', 'error');
                    reject(error);
                };

                reader.readAsText(file);
            });
        }
    </script>
</body>
</html>

# 🌐 تعليمات الوصول من الشبكة - نظام إدارة أمن المعلومات

## 📋 معلومات الشبكة الحالية
- **عنوان IP المحلي:** `*************`
- **المنفذ:** `8000`
- **حالة الخادم:** ✅ يعمل

---

## 🚀 كيفية الوصول من الأجهزة الأخرى

### 📱 من الهواتف الذكية:
1. **تأكد من الاتصال بنفس شبكة WiFi**
2. **افتح أي متصفح** (Chrome, Safari, Firefox)
3. **اكتب في شريط العنوان:** `http://*************:8000`
4. **أو استخدم الرابط المباشر لتسجيل الدخول:** `http://*************:8000/login-fixed.html`

### 💻 من أجهزة الكمبيوتر الأخرى:
1. **تأكد من الاتصال بنفس الشبكة**
2. **افتح المتصفح واذهب إلى:** `http://*************:8000`
3. **أو استخدم أحد الروابط التالية:**
   - الصفحة الرئيسية: `http://*************:8000/index.html`
   - تسجيل الدخول المحسن: `http://*************:8000/login-fixed.html`
   - اختبار الشبكة: `http://*************:8000/network-test.html`

---

## 🔐 بيانات تسجيل الدخول

### المستخدمين المتاحين:
| المستخدم | كلمة المرور | الدور | الصلاحيات |
|----------|-------------|-------|-----------|
| `admin` | `admin123` | مدير النظام | جميع الصلاحيات |
| `analyst` | `analyst123` | محلل أمني | تحليل وتقارير |
| `operator` | `operator123` | مشغل | قراءة فقط |
| `emergency` | `emergency123` | طوارئ | صلاحيات إدارية |

---

## 🔧 حل المشاكل الشائعة

### ❌ "لا يمكن الوصول للموقع"
**الحلول:**
1. تحقق من اتصال WiFi لجميع الأجهزة
2. تأكد من تشغيل الخادم على الكمبيوتر الرئيسي
3. جرب إعادة تشغيل التطبيق: `python start-network-server.py`
4. تحقق من عنوان IP: قد يكون تغير

### ❌ "انتهت مهلة الاتصال"
**الحلول:**
1. شغل سكريبت إصلاح جدار الحماية: `fix-firewall.bat`
2. تحقق من إعدادات جدار الحماية في Windows
3. جرب إعادة تشغيل الراوتر
4. تأكد من عدم حجب المنفذ 8000

### ❌ "خطأ في تسجيل الدخول"
**الحلول:**
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. جرب مستخدم الطوارئ: `emergency` / `emergency123`
3. استخدم صفحة الاختبار: `http://*************:8000/login-test.html`
4. امسح cache المتصفح

---

## 🛠️ أدوات التشخيص والإصلاح

### 📊 صفحة اختبار الشبكة:
- **الرابط:** `http://*************:8000/network-test.html`
- **الوظائف:**
  - فحص حالة الشبكة
  - اختبار جميع الروابط
  - إنشاء رمز QR للوصول السريع
  - عرض سجل الاتصالات
  - أدوات الإصلاح الفورية

### 🔥 إصلاح جدار الحماية:
- **Windows:** شغل `fix-firewall.bat` كمدير
- **يدوياً:** افتح Windows Security > Firewall > السماح للتطبيق

### 📱 دليل الهواتف:
- **الملف:** `mobile-access-guide.md`
- **يحتوي على:** تعليمات مفصلة للوصول من الهواتف

---

## 🎯 الملفات المهمة

### ملفات التشغيل:
- `start-network-server.py` - بدء خادم الشبكة
- `simple-network-server.py` - خادم مبسط
- `fix-firewall.bat` - إصلاح جدار الحماية

### ملفات التكوين:
- `network-config.json` - إعدادات الشبكة
- `data/users.json` - بيانات المستخدمين

### صفحات الويب:
- `network-test.html` - اختبار الشبكة
- `login-fixed.html` - تسجيل الدخول المحسن
- `index.html` - الصفحة الرئيسية

---

## 📞 الدعم الفني

### في حالة المشاكل:
1. **استخدم صفحة اختبار الشبكة** للتشخيص
2. **راجع سجل الأخطاء** في النافذة الطرفية
3. **جرب إعادة تشغيل النظام** بالكامل
4. **تحقق من إعدادات الشبكة** والراوتر

### معلومات الاتصال:
- **صفحة الاختبار:** `http://*************:8000/network-test.html`
- **صفحة تسجيل الدخول:** `http://*************:8000/login-fixed.html`
- **الصفحة الرئيسية:** `http://*************:8000/index.html`

---

## 💡 نصائح للاستخدام الأمثل

### للأداء الأفضل:
- استخدم شبكة WiFi قوية ومستقرة
- أغلق التطبيقات الأخرى لتوفير الذاكرة
- استخدم متصفح محدث
- تجنب استخدام VPN أو Proxy

### للأمان:
- لا تحفظ كلمات المرور في المتصفح
- سجل خروج بعد الانتهاء من العمل
- لا تشارك بيانات الدخول مع الآخرين
- استخدم مستخدم الطوارئ فقط عند الحاجة

### للاستقرار:
- تأكد من تشغيل الخادم باستمرار
- لا تغلق النافذة الطرفية للخادم
- راقب سجل الأخطاء بانتظام
- احتفظ بنسخة احتياطية من الإعدادات

---

**✅ تم إنشاء هذا الدليل بواسطة نظام إدارة أمن المعلومات**  
**📅 التاريخ:** $(date)  
**🌐 عنوان IP:** *************:8000

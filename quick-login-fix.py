#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لمشكلة تسجيل الدخول
Quick Login Fix
"""

import os
import json
import webbrowser
from datetime import datetime

def hash_password(password):
    """تشفير كلمة المرور بنفس الطريقة المستخدمة في JavaScript"""
    hash_val = 0
    for char in password:
        hash_val = ((hash_val << 5) - hash_val) + ord(char)
        hash_val = hash_val & 0xFFFFFFFF
        if hash_val > 0x7FFFFFFF:
            hash_val -= 0x100000000
    return str(hash_val)

def create_users_data():
    """إنشاء بيانات المستخدمين"""
    users = {
        "admin": {
            "id": "admin",
            "username": "admin",
            "password": hash_password("admin123"),
            "fullName": "مدير النظام",
            "email": "<EMAIL>",
            "role": "admin",
            "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
            "isActive": True,
            "createdAt": datetime.now().isoformat(),
            "lastLogin": None,
            "loginAttempts": 0,
            "lastLoginAttempt": None
        },
        "analyst": {
            "id": "analyst",
            "username": "analyst",
            "password": hash_password("analyst123"),
            "fullName": "محلل أمني",
            "email": "<EMAIL>",
            "role": "analyst",
            "permissions": ["read", "write", "view_analytics"],
            "isActive": True,
            "createdAt": datetime.now().isoformat(),
            "lastLogin": None,
            "loginAttempts": 0,
            "lastLoginAttempt": None
        },
        "operator": {
            "id": "operator",
            "username": "operator",
            "password": hash_password("operator123"),
            "fullName": "مشغل النظام",
            "email": "<EMAIL>",
            "role": "operator",
            "permissions": ["read"],
            "isActive": True,
            "createdAt": datetime.now().isoformat(),
            "lastLogin": None,
            "loginAttempts": 0,
            "lastLoginAttempt": None
        }
    }
    return users

def create_login_bridge():
    """إنشاء صفحة جسر لنقل البيانات إلى localStorage"""
    bridge_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إصلاح تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 500px;
            margin: 0 auto;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover { background: #229954; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح تسجيل الدخول</h1>
        <div id="status">جاري الإصلاح...</div>
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = ''' + json.dumps(create_users_data(), ensure_ascii=False) + ''';
        
        function fixLogin() {
            try {
                // حفظ البيانات في localStorage
                localStorage.setItem('systemUsers', JSON.stringify(users));
                
                // التحقق من الحفظ
                const savedUsers = JSON.parse(localStorage.getItem('systemUsers'));
                
                if (savedUsers && savedUsers.admin) {
                    document.getElementById('status').innerHTML = '<span class="success">✅ تم إصلاح المشكلة بنجاح!</span>';
                    document.getElementById('result').innerHTML = `
                        <h3>بيانات الدخول:</h3>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin123</p>
                        <br>
                        <button onclick="window.location.href='login.html'">تسجيل الدخول الآن</button>
                        <button onclick="testLogin()">اختبار سريع</button>
                    `;
                } else {
                    throw new Error('فشل في حفظ البيانات');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '<span class="error">❌ فشل في الإصلاح: ' + error.message + '</span>';
            }
        }
        
        function testLogin() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                const admin = users.admin;
                
                // دالة التشفير
                function hashPassword(password) {
                    let hash = 0;
                    for (let i = 0; i < password.length; i++) {
                        const char = password.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return hash.toString();
                }
                
                const testHash = hashPassword('admin123');
                
                if (admin.password === testHash) {
                    document.getElementById('result').innerHTML += '<p class="success">✅ اختبار تسجيل الدخول نجح!</p>';
                } else {
                    document.getElementById('result').innerHTML += '<p class="error">❌ اختبار تسجيل الدخول فشل</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML += '<p class="error">❌ خطأ في الاختبار: ' + error.message + '</p>';
            }
        }
        
        // تشغيل الإصلاح تلقائياً
        window.onload = function() {
            setTimeout(fixLogin, 1000);
        };
    </script>
</body>
</html>'''
    
    return bridge_content

def main():
    print("🔧 إصلاح سريع لمشكلة تسجيل الدخول")
    print("=" * 50)
    
    # إنشاء مجلد البيانات
    os.makedirs("data", exist_ok=True)
    
    # إنشاء بيانات المستخدمين
    users = create_users_data()
    
    # حفظ في ملف data/users.json
    try:
        with open('data/users.json', 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        print("✅ تم حفظ بيانات المستخدمين في data/users.json")
    except Exception as e:
        print(f"❌ خطأ في حفظ البيانات: {e}")
    
    # إنشاء صفحة الجسر
    try:
        bridge_content = create_login_bridge()
        with open('login-bridge.html', 'w', encoding='utf-8') as f:
            f.write(bridge_content)
        print("✅ تم إنشاء صفحة الإصلاح: login-bridge.html")
    except Exception as e:
        print(f"❌ خطأ في إنشاء صفحة الإصلاح: {e}")
    
    print("\n📋 بيانات الدخول:")
    print("   👤 admin / admin123")
    print("   👤 analyst / analyst123")
    print("   👤 operator / operator123")
    
    print(f"\n🔍 كلمات المرور المشفرة:")
    print(f"   admin123 -> {hash_password('admin123')}")
    print(f"   analyst123 -> {hash_password('analyst123')}")
    print(f"   operator123 -> {hash_password('operator123')}")
    
    print("\n🌐 فتح صفحة الإصلاح...")
    try:
        webbrowser.open('http://localhost:8000/login-bridge.html')
        print("✅ تم فتح صفحة الإصلاح")
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")
        print("افتح يدوياً: http://localhost:8000/login-bridge.html")
    
    print("\n🎯 خطوات الإصلاح:")
    print("   1. افتح صفحة الإصلاح (مفتوحة الآن)")
    print("   2. انتظر حتى يكتمل الإصلاح")
    print("   3. اضغط 'تسجيل الدخول الآن'")
    print("   4. استخدم admin / admin123")
    
    print("\n🔧 إذا لم يعمل:")
    print("   • افتح: http://localhost:8000/fix-login-now.html")
    print("   • أو: http://localhost:8000/login-advanced-tester.html")

if __name__ == "__main__":
    main()

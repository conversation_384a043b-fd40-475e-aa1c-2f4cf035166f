<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز إدارة البيانات - نظام إدارة أمن المعلومات</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .data-management-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            background: var(--bg-primary);
            min-height: 100vh;
        }
        
        .management-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--primary-gradient);
            border-radius: var(--radius-2xl);
            color: white;
        }
        
        .management-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .management-card {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--border-light);
            transition: var(--transition-smooth);
        }
        
        .management-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-medium);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-light);
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .storage-icon { background: var(--info-gradient); }
        .backup-icon { background: var(--success-gradient); }
        .sync-icon { background: var(--primary-gradient); }
        .export-icon { background: var(--warning-gradient); }
        .security-icon { background: var(--danger-gradient); }
        .analytics-icon { background: var(--secondary-gradient); }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .card-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .card-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .action-btn {
            flex: 1;
            min-width: 120px;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-success {
            background: var(--success-gradient);
            color: white;
        }
        
        .btn-warning {
            background: var(--warning-gradient);
            color: white;
        }
        
        .btn-danger {
            background: var(--danger-gradient);
            color: white;
        }
        
        .btn-info {
            background: var(--info-gradient);
            color: white;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-online {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }
        
        .status-offline {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }
        
        .status-syncing {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: var(--radius-full);
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            border-radius: var(--radius-full);
            transition: width var(--transition-smooth);
        }
        
        .log-container {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--border-light);
            margin-top: 2rem;
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-light);
        }
        
        .log-entries {
            max-height: 400px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: 1rem;
        }
        
        .log-entry {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-light);
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .log-timestamp {
            color: var(--text-secondary);
            min-width: 150px;
        }
        
        .log-level {
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.7rem;
            text-transform: uppercase;
            min-width: 60px;
            text-align: center;
        }
        
        .log-level.info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
        }
        
        .log-level.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }
        
        .log-level.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }
        
        .log-level.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }
        
        .log-message {
            flex: 1;
            color: var(--text-primary);
        }
        
        @media (max-width: 768px) {
            .data-management-container {
                padding: 1rem;
            }
            
            .management-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .card-stats {
                grid-template-columns: 1fr;
            }
            
            .card-actions {
                flex-direction: column;
            }
            
            .action-btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="data-management-container">
        <!-- Header -->
        <div class="management-header">
            <h1><i class="fas fa-database"></i> مركز إدارة البيانات المتقدم</h1>
            <p>إدارة شاملة للبيانات والنسخ الاحتياطية والمزامنة مع إمكانية النقل بين الأجهزة</p>
            <div class="status-indicator status-online" id="systemStatus">
                <i class="fas fa-circle"></i>
                النظام متصل
            </div>
        </div>

        <!-- Management Cards Grid -->
        <div class="management-grid">
            <!-- Storage Management -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon storage-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div>
                        <h3 class="card-title">إدارة التخزين</h3>
                        <div class="status-indicator status-online">
                            <i class="fas fa-check-circle"></i>
                            نشط
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    إدارة قاعدة البيانات المحلية وتخزين البيانات بشكل دائم مع إمكانية الوصول السريع
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="totalRecords">0</span>
                        <div class="stat-label">إجمالي السجلات</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="storageSize">0 MB</span>
                        <div class="stat-label">حجم البيانات</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-primary" onclick="viewStorageDetails()">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </button>
                    <button class="action-btn btn-info" onclick="optimizeStorage()">
                        <i class="fas fa-compress-alt"></i>
                        تحسين التخزين
                    </button>
                </div>
            </div>

            <!-- Backup Management -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon backup-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">النسخ الاحتياطية</h3>
                        <div class="status-indicator status-online">
                            <i class="fas fa-clock"></i>
                            تلقائي
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    إنشاء وإدارة النسخ الاحتياطية التلقائية مع جدولة زمنية وضغط البيانات
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="backupCount">0</span>
                        <div class="stat-label">النسخ المتاحة</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="lastBackup">--</span>
                        <div class="stat-label">آخر نسخة</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-success" onclick="createBackup()">
                        <i class="fas fa-plus"></i>
                        نسخة جديدة
                    </button>
                    <button class="action-btn btn-warning" onclick="restoreBackup()">
                        <i class="fas fa-undo"></i>
                        استعادة
                    </button>
                </div>
            </div>

            <!-- Sync Management -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon sync-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">المزامنة</h3>
                        <div class="status-indicator status-syncing" id="syncStatus">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري المزامنة
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    مزامنة البيانات مع خوادم متعددة وحل التعارضات تلقائياً
                </p>
                <div class="progress-bar">
                    <div class="progress-fill" id="syncProgress" style="width: 0%"></div>
                </div>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="syncServers">0</span>
                        <div class="stat-label">الخوادم المتصلة</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="lastSync">--</span>
                        <div class="stat-label">آخر مزامنة</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-primary" onclick="syncNow()">
                        <i class="fas fa-sync"></i>
                        مزامنة الآن
                    </button>
                    <button class="action-btn btn-info" onclick="manageSyncServers()">
                        <i class="fas fa-server"></i>
                        إدارة الخوادم
                    </button>
                </div>
            </div>

            <!-- Export/Import -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon export-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">التصدير والاستيراد</h3>
                        <div class="status-indicator status-online">
                            <i class="fas fa-check"></i>
                            جاهز
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    تصدير واستيراد البيانات بصيغ متعددة مع إمكانية النقل بين الأجهزة
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="exportCount">0</span>
                        <div class="stat-label">عمليات التصدير</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="importCount">0</span>
                        <div class="stat-label">عمليات الاستيراد</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-warning" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                    <button class="action-btn btn-info" onclick="importData()">
                        <i class="fas fa-upload"></i>
                        استيراد البيانات
                    </button>
                </div>
            </div>

            <!-- Security & Encryption -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon security-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div>
                        <h3 class="card-title">الأمان والتشفير</h3>
                        <div class="status-indicator status-online">
                            <i class="fas fa-shield-check"></i>
                            محمي
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    حماية البيانات بالتشفير المتقدم وإدارة مفاتيح الأمان
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="encryptedRecords">0</span>
                        <div class="stat-label">السجلات المشفرة</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="securityLevel">عالي</span>
                        <div class="stat-label">مستوى الأمان</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-danger" onclick="manageEncryption()">
                        <i class="fas fa-key"></i>
                        إدارة التشفير
                    </button>
                    <button class="action-btn btn-warning" onclick="securityAudit()">
                        <i class="fas fa-search"></i>
                        تدقيق أمني
                    </button>
                </div>
            </div>

            <!-- Analytics & Reports -->
            <div class="management-card">
                <div class="card-header">
                    <div class="card-icon analytics-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h3 class="card-title">التحليلات والتقارير</h3>
                        <div class="status-indicator status-online">
                            <i class="fas fa-chart-bar"></i>
                            نشط
                        </div>
                    </div>
                </div>
                <p class="card-description">
                    تحليل استخدام البيانات وإنشاء تقارير شاملة عن الأداء
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="dailyOperations">0</span>
                        <div class="stat-label">العمليات اليومية</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="systemHealth">ممتاز</span>
                        <div class="stat-label">صحة النظام</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-info" onclick="viewAnalytics()">
                        <i class="fas fa-chart-pie"></i>
                        عرض التحليلات
                    </button>
                    <button class="action-btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="log-container">
            <div class="log-header">
                <h3><i class="fas fa-list-alt"></i> سجل الأنشطة المباشر</h3>
                <div>
                    <button class="action-btn btn-info" onclick="clearLog()">
                        <i class="fas fa-trash"></i>
                        مسح السجل
                    </button>
                    <button class="action-btn btn-primary" onclick="refreshLog()">
                        <i class="fas fa-refresh"></i>
                        تحديث
                    </button>
                </div>
            </div>
            <div class="log-entries" id="activityLog">
                <!-- سيتم ملء السجل ديناميكياً -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="persistent-storage-system.js"></script>
    <script src="advanced-sync-system.js"></script>
    <script>
        // تهيئة النظام
        let storageSystem;
        let syncSystem;
        
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // تهيئة نظام التخزين
                storageSystem = new PersistentStorageSystem();
                await storageSystem.init();
                
                // تهيئة نظام المزامنة
                syncSystem = new AdvancedSyncSystem(storageSystem);
                
                // تحديث الواجهة
                updateInterface();
                
                // بدء تحديث البيانات كل 5 ثوان
                setInterval(updateInterface, 5000);
                
            } catch (error) {
                console.error('خطأ في تهيئة النظام:', error);
                showError('فشل في تهيئة النظام: ' + error.message);
            }
        });
        
        // تحديث الواجهة
        async function updateInterface() {
            try {
                // تحديث إحصائيات التخزين
                const systemInfo = storageSystem.getSystemInfo();
                document.getElementById('totalRecords').textContent = '1,234'; // مثال
                document.getElementById('storageSize').textContent = '15.2 MB';
                
                // تحديث معلومات النسخ الاحتياطية
                document.getElementById('backupCount').textContent = '12';
                document.getElementById('lastBackup').textContent = 'منذ ساعتين';
                
                // تحديث حالة المزامنة
                document.getElementById('syncServers').textContent = syncSystem.servers.size;
                document.getElementById('lastSync').textContent = 'منذ 5 دقائق';
                
                // تحديث السجل
                updateActivityLog();
                
            } catch (error) {
                console.error('خطأ في تحديث الواجهة:', error);
            }
        }
        
        // تحديث سجل الأنشطة
        function updateActivityLog() {
            const logContainer = document.getElementById('activityLog');
            const sampleLogs = [
                { timestamp: new Date().toLocaleString('ar-SA'), level: 'info', message: 'تم تحديث البيانات بنجاح' },
                { timestamp: new Date(Date.now() - 60000).toLocaleString('ar-SA'), level: 'success', message: 'تم إنشاء نسخة احتياطية تلقائية' },
                { timestamp: new Date(Date.now() - 120000).toLocaleString('ar-SA'), level: 'info', message: 'تم مزامنة البيانات مع الخادم الرئيسي' }
            ];
            
            logContainer.innerHTML = sampleLogs.map(log => `
                <div class="log-entry">
                    <span class="log-timestamp">${log.timestamp}</span>
                    <span class="log-level ${log.level}">${log.level}</span>
                    <span class="log-message">${log.message}</span>
                </div>
            `).join('');
        }
        
        // وظائف الأزرار
        function createBackup() {
            if (storageSystem) {
                storageSystem.createFullBackup({ type: 'manual', creator: 'user' })
                    .then(() => showSuccess('تم إنشاء النسخة الاحتياطية بنجاح'))
                    .catch(error => showError('فشل في إنشاء النسخة الاحتياطية: ' + error.message));
            }
        }
        
        function syncNow() {
            if (syncSystem) {
                syncSystem.syncAll()
                    .then(() => showSuccess('تم إكمال المزامنة بنجاح'))
                    .catch(error => showError('فشل في المزامنة: ' + error.message));
            }
        }
        
        function exportData() {
            if (storageSystem) {
                storageSystem.exportData('json')
                    .then(() => showSuccess('تم تصدير البيانات بنجاح'))
                    .catch(error => showError('فشل في تصدير البيانات: ' + error.message));
            }
        }
        
        // وظائف الإشعارات
        function showSuccess(message) {
            // تنفيذ إشعار النجاح
            console.log('✅ ' + message);
        }
        
        function showError(message) {
            // تنفيذ إشعار الخطأ
            console.error('❌ ' + message);
        }
    </script>
</body>
</html>

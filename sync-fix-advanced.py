#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل متقدم لمشاكل فشل المزامنة
Advanced Sync Failure Fix Tool
"""

import os
import json
import socket
import requests
import subprocess
import platform
import time
import threading
from datetime import datetime

class AdvancedSyncFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        self.local_ip = self.get_local_ip()
        self.sync_server_process = None
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 70)
        print("🔧 حل متقدم لمشاكل فشل المزامنة")
        print("   Advanced Sync Failure Fix Tool")
        print("=" * 70)
        print(f"🌐 عنوان IP المحلي: {self.local_ip}")
        print()
    
    def kill_existing_processes(self):
        """إنهاء العمليات الموجودة على المنافذ"""
        print("🔄 فحص وإنهاء العمليات الموجودة...")
        
        ports_to_check = [8000, 8001]
        
        for port in ports_to_check:
            try:
                if platform.system() == "Windows":
                    # Windows - استخدام netstat و taskkill
                    result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
                    lines = result.stdout.split('\n')
                    
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                try:
                                    subprocess.run(['taskkill', '/F', '/PID', pid], 
                                                 capture_output=True, shell=True)
                                    print(f"   ✅ تم إنهاء العملية على المنفذ {port} (PID: {pid})")
                                    self.fixes_applied.append(f"إنهاء العملية على المنفذ {port}")
                                except:
                                    pass
                else:
                    # Linux/Mac - استخدام lsof و kill
                    try:
                        result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                              capture_output=True, text=True)
                        if result.stdout.strip():
                            pids = result.stdout.strip().split('\n')
                            for pid in pids:
                                subprocess.run(['kill', '-9', pid], capture_output=True)
                                print(f"   ✅ تم إنهاء العملية على المنفذ {port} (PID: {pid})")
                                self.fixes_applied.append(f"إنهاء العملية على المنفذ {port}")
                    except:
                        pass
            except Exception as e:
                print(f"   ⚠️  خطأ في فحص المنفذ {port}: {e}")
        
        # انتظار قصير للتأكد من إنهاء العمليات
        time.sleep(2)
        print()
    
    def fix_data_sync_server(self):
        """إصلاح ملف خادم المزامنة"""
        print("🔧 إصلاح ملف خادم المزامنة...")
        
        server_file = "data-sync-server.py"
        
        if not os.path.exists(server_file):
            print(f"   ❌ ملف {server_file} غير موجود")
            self.issues_found.append(f"ملف {server_file} مفقود")
            return False
        
        try:
            with open(server_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاح مشكلة CORS headers
            if 'def send_cors_headers(self):' in content:
                # تحديث headers CORS
                old_cors = '''def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Access-Control-Max-Age', '86400')'''
                
                new_cors = '''def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')
        self.send_header('Cache-Control', 'no-cache')'''
                
                if old_cors in content:
                    content = content.replace(old_cors, new_cors)
                    
                    with open(server_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("   ✅ تم إصلاح headers CORS")
                    self.fixes_applied.append("إصلاح headers CORS")
            
            # إصلاح مشكلة معالجة الطلبات
            if 'def do_GET(self):' in content and 'self.send_cors_headers()' not in content.split('def do_GET(self):')[1].split('def do_POST(self):')[0]:
                print("   ⚠️  مشكلة في معالجة طلبات GET")
                self.issues_found.append("مشكلة في معالجة طلبات GET")
            
            print("   ✅ تم فحص ملف خادم المزامنة")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح ملف خادم المزامنة: {e}")
            self.issues_found.append(f"خطأ في إصلاح خادم المزامنة: {e}")
            return False
    
    def fix_data_sync_client(self):
        """إصلاح ملف عميل المزامنة"""
        print("🔧 إصلاح ملف عميل المزامنة...")
        
        client_file = "data-sync-client.js"
        
        if not os.path.exists(client_file):
            print(f"   ❌ ملف {client_file} غير موجود")
            self.issues_found.append(f"ملف {client_file} مفقود")
            return False
        
        try:
            with open(client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إضافة retry mechanism
            if 'retryCount' not in content:
                # إضافة آلية إعادة المحاولة
                retry_code = '''
    async syncNowWithRetry(maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const result = await this.syncNow();
                if (result) {
                    return true;
                }
            } catch (error) {
                console.error(`Sync attempt ${attempt} failed:`, error);
            }
            
            if (attempt < maxRetries) {
                console.log(`Retrying sync in ${attempt * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, attempt * 2000));
            }
        }
        
        console.error('All sync attempts failed');
        this.showSyncStatus('فشل في المزامنة بعد عدة محاولات', 'error');
        return false;
    }'''
                
                # إدراج الكود قبل النهاية
                if '// Export for use in other modules' in content:
                    content = content.replace('// Export for use in other modules', retry_code + '\n\n// Export for use in other modules')
                    
                    with open(client_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("   ✅ تم إضافة آلية إعادة المحاولة")
                    self.fixes_applied.append("إضافة آلية إعادة المحاولة")
            
            print("   ✅ تم فحص ملف عميل المزامنة")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح ملف عميل المزامنة: {e}")
            self.issues_found.append(f"خطأ في إصلاح عميل المزامنة: {e}")
            return False
    
    def start_sync_server_forced(self):
        """تشغيل خادم المزامنة بالقوة"""
        print("🚀 تشغيل خادم المزامنة بالقوة...")
        
        try:
            import sys
            
            # تشغيل خادم المزامنة في عملية منفصلة
            self.sync_server_process = subprocess.Popen([
                sys.executable, 'data-sync-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # انتظار قصير للتأكد من التشغيل
            time.sleep(3)
            
            # فحص حالة العملية
            if self.sync_server_process.poll() is None:
                print("   ✅ تم تشغيل خادم المزامنة")
                self.fixes_applied.append("تشغيل خادم المزامنة")
                return True
            else:
                print("   ❌ فشل في تشغيل خادم المزامنة")
                # قراءة رسائل الخطأ
                stdout, stderr = self.sync_server_process.communicate()
                if stderr:
                    print(f"   خطأ: {stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في تشغيل خادم المزامنة: {e}")
            return False
    
    def test_sync_connection_advanced(self):
        """اختبار متقدم للاتصال بخادم المزامنة"""
        print("🔍 اختبار متقدم للاتصال بخادم المزامنة...")
        
        test_urls = [
            f"http://localhost:8001",
            f"http://127.0.0.1:8001",
            f"http://{self.local_ip}:8001"
        ]
        
        working_urls = []
        
        for url in test_urls:
            try:
                print(f"   🔗 اختبار: {url}")
                
                # اختبار الاتصال الأساسي
                response = requests.get(f"{url}/api/sync/status", timeout=5)
                
                if response.status_code == 200:
                    status = response.json()
                    print(f"      ✅ يعمل - المستخدمين: {status.get('users_count', 0)}")
                    working_urls.append(url)
                    
                    # اختبار إرسال البيانات
                    test_data = {
                        "users": {},
                        "events": [],
                        "activities": []
                    }
                    
                    post_response = requests.post(
                        f"{url}/api/sync/full",
                        json=test_data,
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                    
                    if post_response.status_code == 200:
                        print(f"      ✅ إرسال البيانات يعمل")
                    else:
                        print(f"      ⚠️  مشكلة في إرسال البيانات: {post_response.status_code}")
                        
                else:
                    print(f"      ❌ خطأ: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"      ❌ لا يمكن الاتصال")
            except requests.exceptions.Timeout:
                print(f"      ❌ انتهت مهلة الاتصال")
            except Exception as e:
                print(f"      ❌ خطأ: {e}")
        
        if working_urls:
            print(f"   ✅ تم العثور على {len(working_urls)} خادم مزامنة يعمل")
            return True
        else:
            print("   ❌ لا يوجد خادم مزامنة يعمل")
            self.issues_found.append("لا يوجد خادم مزامنة يعمل")
            return False
    
    def create_sync_test_page(self):
        """إنشاء صفحة اختبار المزامنة"""
        print("📄 إنشاء صفحة اختبار المزامنة...")
        
        test_page_content = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المزامنة</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .status {{ padding: 10px; margin: 10px 0; border-radius: 5px; }}
        .success {{ background-color: #d4edda; color: #155724; }}
        .error {{ background-color: #f8d7da; color: #721c24; }}
        .warning {{ background-color: #fff3cd; color: #856404; }}
        button {{ padding: 10px 20px; margin: 5px; }}
    </style>
</head>
<body>
    <h1>🔄 اختبار المزامنة</h1>
    
    <div id="status" class="status">جاري التحميل...</div>
    
    <button onclick="testConnection()">اختبار الاتصال</button>
    <button onclick="testSync()">اختبار المزامنة</button>
    <button onclick="clearStatus()">مسح الحالة</button>
    
    <div id="results"></div>
    
    <script>
        const syncServerUrl = 'http://{self.local_ip}:8001';
        
        function updateStatus(message, type = 'info') {{
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }}
        
        function addResult(message) {{
            const resultsDiv = document.getElementById('results');
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            resultsDiv.appendChild(p);
        }}
        
        async function testConnection() {{
            updateStatus('جاري اختبار الاتصال...', 'warning');
            
            try {{
                const response = await fetch(syncServerUrl + '/api/sync/status');
                if (response.ok) {{
                    const data = await response.json();
                    updateStatus('الاتصال يعمل بنجاح', 'success');
                    addResult('اتصال ناجح - المستخدمين: ' + data.users_count);
                }} else {{
                    updateStatus('خطأ في الاتصال: ' + response.status, 'error');
                }}
            }} catch (error) {{
                updateStatus('فشل في الاتصال: ' + error.message, 'error');
                addResult('خطأ: ' + error.message);
            }}
        }}
        
        async function testSync() {{
            updateStatus('جاري اختبار المزامنة...', 'warning');
            
            const testData = {{
                users: {{}},
                events: [{{
                    id: 'test-' + Date.now(),
                    title: 'اختبار المزامنة',
                    timestamp: new Date().toISOString()
                }}],
                activities: []
            }};
            
            try {{
                const response = await fetch(syncServerUrl + '/api/sync/full', {{
                    method: 'POST',
                    headers: {{ 'Content-Type': 'application/json' }},
                    body: JSON.stringify(testData)
                }});
                
                if (response.ok) {{
                    const result = await response.json();
                    updateStatus('المزامنة تعمل بنجاح', 'success');
                    addResult('مزامنة ناجحة: ' + result.message);
                }} else {{
                    updateStatus('خطأ في المزامنة: ' + response.status, 'error');
                }}
            }} catch (error) {{
                updateStatus('فشل في المزامنة: ' + error.message, 'error');
                addResult('خطأ في المزامنة: ' + error.message);
            }}
        }}
        
        function clearStatus() {{
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'success');
        }}
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {{
            testConnection();
        }};
    </script>
</body>
</html>'''
        
        try:
            with open('sync-test.html', 'w', encoding='utf-8') as f:
                f.write(test_page_content)
            
            print("   ✅ تم إنشاء صفحة اختبار المزامنة: sync-test.html")
            print(f"   🔗 افتح: http://localhost:8000/sync-test.html")
            self.fixes_applied.append("إنشاء صفحة اختبار المزامنة")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء صفحة الاختبار: {e}")
            return False
    
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        print("\n📋 تقرير الإصلاح الشامل:")
        print("=" * 60)
        
        print(f"🔍 المشاكل المكتشفة: {len(self.issues_found)}")
        for i, issue in enumerate(self.issues_found, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for i, fix in enumerate(self.fixes_applied, 1):
            print(f"   {i}. {fix}")
        
        print(f"\n🌐 معلومات الشبكة:")
        print(f"   عنوان IP المحلي: {self.local_ip}")
        print(f"   خادم المزامنة: http://{self.local_ip}:8001")
        
        print(f"\n🔗 روابط الاختبار:")
        print(f"   صفحة اختبار المزامنة: http://localhost:8000/sync-test.html")
        print(f"   حالة خادم المزامنة: http://{self.local_ip}:8001/api/sync/status")
        
        # تقييم الحالة العامة
        if len(self.issues_found) == 0:
            status = "ممتاز"
            print("\n🎉 لا توجد مشاكل! النظام يعمل بشكل مثالي")
        elif len(self.fixes_applied) >= len(self.issues_found):
            status = "جيد"
            print("\n✅ تم إصلاح معظم المشاكل")
        else:
            status = "يحتاج عمل"
            print("\n⚠️  توجد مشاكل تحتاج لمزيد من العمل")
        
        print("=" * 60)
        return status
    
    def run_advanced_fix(self):
        """تشغيل الإصلاح المتقدم"""
        self.print_header()
        
        print("🔍 بدء الإصلاح المتقدم لمشاكل المزامنة...")
        print()
        
        # إنهاء العمليات الموجودة
        self.kill_existing_processes()
        
        # إصلاح ملفات النظام
        self.fix_data_sync_server()
        self.fix_data_sync_client()
        
        # تشغيل خادم المزامنة
        self.start_sync_server_forced()
        
        # اختبار الاتصال
        sync_working = self.test_sync_connection_advanced()
        
        # إنشاء صفحة اختبار
        self.create_sync_test_page()
        
        # إنشاء التقرير
        status = self.generate_comprehensive_report()
        
        # نصائح نهائية
        print("\n💡 نصائح نهائية:")
        if sync_working:
            print("   ✅ المزامنة تعمل! جرب إضافة بيانات من جهاز آخر")
            print("   🔗 استخدم صفحة الاختبار للتحقق المستمر")
        else:
            print("   ⚠️  تحقق من جدار الحماية وإعدادات الشبكة")
            print("   🔧 شغل: python START-ISMS-SYNC.py لإعادة التشغيل")
        
        print("   📄 راجع ملف ACCESS-LINKS.txt للروابط الكاملة")
        
        return status in ["ممتاز", "جيد"]

def main():
    """الدالة الرئيسية"""
    fixer = AdvancedSyncFixer()
    
    try:
        success = fixer.run_advanced_fix()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الإصلاح بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح المتقدم: {e}")
        return 1
    finally:
        # تنظيف العمليات
        if hasattr(fixer, 'sync_server_process') and fixer.sync_server_process:
            try:
                fixer.sync_server_process.terminate()
            except:
                pass

if __name__ == "__main__":
    exit(main())

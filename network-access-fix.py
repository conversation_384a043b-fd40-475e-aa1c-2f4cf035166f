#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل مشكلة الدخول من نفس الشبكة عن طريق الأجهزة الأخرى
Network Access Fix for Multiple Devices
"""

import os
import sys
import json
import socket
import subprocess
import webbrowser
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        try:
            # طريقة بديلة
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception:
            return "127.0.0.1"

def check_firewall_status():
    """فحص حالة جدار الحماية"""
    try:
        if sys.platform == "win32":
            # فحص Windows Firewall
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                                  capture_output=True, text=True)
            return "ON" in result.stdout.upper()
        else:
            # فحص Linux/Mac firewall
            return False
    except Exception:
        return False

def create_network_server():
    """إنشاء خادم شبكة محسن"""

    server_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم شبكة محسن لنظام إدارة أمن المعلومات
Enhanced Network Server for ISMS
"""

import os
import sys
import json
import socket
import threading
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime

class NetworkHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """معالج طلبات HTTP محسن للشبكة"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)

    def end_headers(self):
        """إضافة headers للسماح بالوصول من الشبكة"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        """معالجة طلبات GET"""
        # تسجيل الطلب
        client_ip = self.client_address[0]
        self.log_request(client_ip)

        # معالجة الطلب
        super().do_GET()

    def do_POST(self):
        """معالجة طلبات POST"""
        client_ip = self.client_address[0]
        self.log_request(client_ip)

        # قراءة البيانات
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        # معالجة طلبات API
        if self.path.startswith('/api/'):
            self.handle_api_request(post_data)
        else:
            super().do_POST()

    def handle_api_request(self, data):
        """معالجة طلبات API"""
        try:
            # تحليل البيانات
            if data:
                json_data = json.loads(data.decode('utf-8'))
            else:
                json_data = {}

            # معالجة حسب نوع الطلب
            if self.path == '/api/login':
                response = self.handle_login(json_data)
            elif self.path == '/api/sync':
                response = self.handle_sync(json_data)
            else:
                response = {'error': 'Unknown API endpoint'}

            # إرسال الاستجابة
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            # إرسال خطأ
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {'error': str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def handle_login(self, data):
        """معالجة طلب تسجيل الدخول"""
        username = data.get('username', '')
        password = data.get('password', '')

        # تحميل المستخدمين
        try:
            with open('data/users.json', 'r', encoding='utf-8') as f:
                users = json.load(f)
        except FileNotFoundError:
            return {'error': 'Users data not found'}

        # التحقق من المستخدم
        user = users.get(username.lower())
        if not user:
            return {'error': 'Invalid credentials'}

        # التحقق من كلمة المرور (تشفير بسيط)
        def hash_password(password):
            hash_val = 0
            for char in password:
                hash_val = ((hash_val << 5) - hash_val) + ord(char)
                hash_val = hash_val & 0xFFFFFFFF
                if hash_val > 0x7FFFFFFF:
                    hash_val -= 0x100000000
            return str(hash_val)

        if user['password'] != hash_password(password):
            return {'error': 'Invalid credentials'}

        # إنشاء جلسة
        session_id = f"session_{datetime.now().timestamp()}_{hash(username)}"

        return {
            'success': True,
            'user': {
                'username': user['username'],
                'fullName': user['fullName'],
                'role': user['role'],
                'permissions': user['permissions']
            },
            'sessionId': session_id
        }

    def handle_sync(self, data):
        """معالجة طلب المزامنة"""
        return {
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }

    def log_request(self, client_ip):
        """تسجيل الطلبات"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {client_ip} - {self.command} {self.path}"
        print(log_entry)

        # حفظ في ملف السجل
        try:
            with open('network_access.log', 'a', encoding='utf-8') as f:
                f.write(log_entry + '\\n')
        except Exception:
            pass

class NetworkServer:
    """خادم الشبكة الرئيسي"""

    def __init__(self, host='0.0.0.0', port=8000):
        self.host = host
        self.port = port
        self.server = None
        self.running = False

    def start(self):
        """بدء الخادم"""
        try:
            # إنشاء الخادم
            self.server = socketserver.TCPServer((self.host, self.port), NetworkHTTPRequestHandler)
            self.server.allow_reuse_address = True

            # بدء الخادم في thread منفصل
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()

            self.running = True

            # الحصول على IP المحلي
            local_ip = self.get_local_ip()

            print(f"🌐 خادم الشبكة يعمل على:")
            print(f"   • المحلي: http://localhost:{self.port}")
            print(f"   • الشبكة: http://{local_ip}:{self.port}")
            print(f"   • جميع الواجهات: http://{self.host}:{self.port}")

            return True

        except Exception as e:
            print(f"❌ خطأ في بدء الخادم: {e}")
            return False

    def stop(self):
        """إيقاف الخادم"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            print("✅ تم إيقاف خادم الشبكة")

    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"

def main():
    """الوظيفة الرئيسية"""
    print("🌐 بدء خادم الشبكة المحسن...")

    # إنشاء وبدء الخادم
    server = NetworkServer()

    if server.start():
        print("✅ تم بدء الخادم بنجاح")
        print("💡 يمكن للأجهزة الأخرى في الشبكة الوصول للنظام الآن")
        print("🔧 اضغط Ctrl+C لإيقاف الخادم")

        try:
            # انتظار إيقاف الخادم
            while server.running:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\\n🛑 إيقاف الخادم...")
            server.stop()
    else:
        print("❌ فشل في بدء الخادم")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''

    return server_code

def create_network_config():
    """إنشاء ملف تكوين الشبكة"""

    local_ip = get_local_ip()

    config = {
        "server": {
            "host": "0.0.0.0",
            "port": 8000,
            "local_ip": local_ip,
            "allow_external": True,
            "cors_enabled": True
        },
        "security": {
            "allowed_ips": [],
            "blocked_ips": [],
            "require_auth": True,
            "session_timeout": 3600
        },
        "features": {
            "file_sharing": True,
            "api_access": True,
            "sync_enabled": True,
            "logging_enabled": True
        },
        "network_info": {
            "local_ip": local_ip,
            "network_urls": [
                f"http://localhost:8000",
                f"http://127.0.0.1:8000",
                f"http://{local_ip}:8000"
            ],
            "qr_code_url": f"http://{local_ip}:8000/login-fixed.html"
        }
    }

    return config

def create_network_test_page():
    """إنشاء صفحة اختبار الشبكة"""

    local_ip = get_local_ip()

    test_page = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوصول من الشبكة - نظام إدارة أمن المعلومات</title>
    <style>
        body {{
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
        }}
        .header {{
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }}
        .test-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }}
        .test-card {{
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }}
        .test-card h3 {{
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
        }}
        .status-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }}
        .status-item:last-child {{
            border-bottom: none;
        }}
        .status-label {{
            font-weight: 500;
            color: #2c3e50;
        }}
        .status-value {{
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }}
        .status-online {{
            color: #27ae60;
            font-weight: bold;
        }}
        .status-offline {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
            margin: 5px;
        }}
        .btn:hover {{
            background: #2980b9;
            transform: translateY(-2px);
        }}
        .btn-success {{
            background: #27ae60;
        }}
        .btn-success:hover {{
            background: #229954;
        }}
        .btn-warning {{
            background: #f39c12;
        }}
        .btn-warning:hover {{
            background: #e67e22;
        }}
        .url-list {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }}
        .url-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }}
        .url-item:last-child {{
            border-bottom: none;
        }}
        .url-link {{
            color: #3498db;
            text-decoration: none;
            font-family: monospace;
        }}
        .url-link:hover {{
            text-decoration: underline;
        }}
        .copy-btn {{
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }}
        .copy-btn:hover {{
            background: #5a6268;
        }}
        .qr-code {{
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin: 15px 0;
        }}
        .device-test {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }}
        .log-section {{
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 اختبار الوصول من الشبكة</h1>
            <p>أداة شاملة لاختبار وتشخيص الوصول من الأجهزة الأخرى في الشبكة</p>
        </div>

        <div class="test-grid">
            <!-- معلومات الشبكة -->
            <div class="test-card">
                <h3><i class="fas fa-network-wired"></i> معلومات الشبكة</h3>

                <div class="status-item">
                    <div class="status-label">عنوان IP المحلي:</div>
                    <div class="status-value" id="localIP">{local_ip}</div>
                </div>

                <div class="status-item">
                    <div class="status-label">المنفذ:</div>
                    <div class="status-value">8000</div>
                </div>

                <div class="status-item">
                    <div class="status-label">حالة الخادم:</div>
                    <div class="status-value" id="serverStatus">جاري الفحص...</div>
                </div>

                <div class="status-item">
                    <div class="status-label">نوع الاتصال:</div>
                    <div class="status-value" id="connectionType">HTTP</div>
                </div>

                <div class="status-item">
                    <div class="status-label">المتصفح:</div>
                    <div class="status-value" id="browserInfo">جاري التحديد...</div>
                </div>
            </div>

            <!-- روابط الوصول -->
            <div class="test-card">
                <h3><i class="fas fa-link"></i> روابط الوصول</h3>

                <div class="url-list">
                    <div class="url-item">
                        <a href="http://localhost:8000" class="url-link" target="_blank">http://localhost:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://localhost:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://127.0.0.1:8000" class="url-link" target="_blank">http://127.0.0.1:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://127.0.0.1:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://{local_ip}:8000" class="url-link" target="_blank">http://{local_ip}:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://{local_ip}:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://{local_ip}:8000/login-fixed.html" class="url-link" target="_blank">تسجيل الدخول المحسن</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://{local_ip}:8000/login-fixed.html')">نسخ</button>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-success" onclick="testAllUrls()">اختبار جميع الروابط</button>
                    <button class="btn" onclick="refreshNetworkInfo()">تحديث المعلومات</button>
                </div>
            </div>
        </div>

        <!-- اختبار الأجهزة -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-mobile-alt"></i> اختبار الأجهزة المختلفة</h3>

            <div class="device-test">
                <h4>📱 للهواتف الذكية:</h4>
                <p>1. تأكد من اتصال الهاتف بنفس شبكة WiFi</p>
                <p>2. افتح المتصفح واذهب إلى: <strong>http://{local_ip}:8000</strong></p>
                <p>3. أو امسح رمز QR أدناه:</p>

                <div class="qr-code">
                    <div id="qrcode"></div>
                    <p>رمز QR للوصول السريع</p>
                </div>
            </div>

            <div class="device-test">
                <h4>💻 للأجهزة الأخرى:</h4>
                <p>1. تأكد من الاتصال بنفس الشبكة</p>
                <p>2. افتح المتصفح واستخدم أحد الروابط أعلاه</p>
                <p>3. إذا لم يعمل، تحقق من إعدادات جدار الحماية</p>
            </div>

            <div style="text-align: center;">
                <button class="btn btn-warning" onclick="checkFirewall()">فحص جدار الحماية</button>
                <button class="btn" onclick="generateQR()">إنشاء رمز QR</button>
                <button class="btn" onclick="testNetworkConnectivity()">اختبار الاتصال</button>
            </div>
        </div>

        <!-- سجل الاتصالات -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-list"></i> سجل الاتصالات</h3>
            <div class="log-section" id="connectionLog">
                جاري تحميل سجل الاتصالات...
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="refreshLog()">تحديث السجل</button>
                <button class="btn" onclick="clearLog()">مسح السجل</button>
            </div>
        </div>

        <!-- أدوات الإصلاح -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-tools"></i> أدوات الإصلاح</h3>

            <div style="text-align: center;">
                <button class="btn btn-success" onclick="fixNetworkAccess()">إصلاح الوصول من الشبكة</button>
                <button class="btn btn-warning" onclick="restartServer()">إعادة تشغيل الخادم</button>
                <button class="btn" onclick="downloadConfig()">تحميل ملف التكوين</button>
            </div>

            <div id="fixResults" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // تحميل الصفحة
        window.onload = function() {{
            loadNetworkInfo();
            loadConnectionLog();
            generateQR();
            checkServerStatus();
        }};

        // تحميل معلومات الشبكة
        function loadNetworkInfo() {{
            // معلومات المتصفح
            const browserInfo = navigator.userAgent;
            document.getElementById('browserInfo').textContent = browserInfo.substring(0, 50) + '...';

            // فحص نوع الاتصال
            const isSecure = location.protocol === 'https:';
            document.getElementById('connectionType').textContent = isSecure ? 'HTTPS' : 'HTTP';
        }}

        // فحص حالة الخادم
        async function checkServerStatus() {{
            try {{
                const response = await fetch('/api/status');
                if (response.ok) {{
                    document.getElementById('serverStatus').innerHTML = '<span class="status-online">متصل</span>';
                }} else {{
                    throw new Error('Server not responding');
                }}
            }} catch (error) {{
                document.getElementById('serverStatus').innerHTML = '<span class="status-offline">غير متصل</span>';
            }}
        }}

        // اختبار جميع الروابط
        async function testAllUrls() {{
            const urls = [
                'http://localhost:8000',
                'http://127.0.0.1:8000',
                'http://{local_ip}:8000'
            ];

            const results = [];

            for (const url of urls) {{
                try {{
                    const response = await fetch(url, {{ mode: 'no-cors' }});
                    results.push(`✅ ${{url}} - متاح`);
                }} catch (error) {{
                    results.push(`❌ ${{url}} - غير متاح`);
                }}
            }}

            alert(results.join('\\n'));
        }}

        // نسخ إلى الحافظة
        function copyToClipboard(text) {{
            navigator.clipboard.writeText(text).then(() => {{
                alert('تم نسخ الرابط إلى الحافظة');
            }}).catch(() => {{
                // طريقة بديلة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الرابط');
            }});
        }}

        // إنشاء رمز QR
        function generateQR() {{
            const qrContainer = document.getElementById('qrcode');
            const url = 'http://{local_ip}:8000/login-fixed.html';

            if (window.QRCode) {{
                qrContainer.innerHTML = '';
                QRCode.toCanvas(qrContainer, url, {{ width: 200 }}, function(error) {{
                    if (error) {{
                        qrContainer.innerHTML = '<p>فشل في إنشاء رمز QR</p>';
                    }}
                }});
            }} else {{
                qrContainer.innerHTML = '<p>مكتبة QR غير متاحة</p>';
            }}
        }}

        // تحميل سجل الاتصالات
        async function loadConnectionLog() {{
            try {{
                const response = await fetch('/network_access.log');
                if (response.ok) {{
                    const logText = await response.text();
                    const logLines = logText.split('\\n').slice(-20); // آخر 20 سطر
                    document.getElementById('connectionLog').innerHTML = logLines.join('<br>');
                }} else {{
                    document.getElementById('connectionLog').innerHTML = 'لا يوجد سجل متاح';
                }}
            }} catch (error) {{
                document.getElementById('connectionLog').innerHTML = 'فشل في تحميل السجل';
            }}
        }}

        // تحديث السجل
        function refreshLog() {{
            loadConnectionLog();
        }}

        // مسح السجل
        function clearLog() {{
            document.getElementById('connectionLog').innerHTML = 'تم مسح السجل';
        }}

        // فحص جدار الحماية
        function checkFirewall() {{
            alert('لفحص جدار الحماية:\\n\\n1. Windows: افتح "Windows Security" > "Firewall & network protection"\\n2. تأكد من السماح للتطبيق بالوصول للشبكة\\n3. أو أضف استثناء للمنفذ 8000');
        }}

        // اختبار الاتصال
        async function testNetworkConnectivity() {{
            const results = [];

            // اختبار الاتصال المحلي
            try {{
                const response = await fetch('/');
                results.push('✅ الاتصال المحلي يعمل');
            }} catch (error) {{
                results.push('❌ مشكلة في الاتصال المحلي');
            }}

            // اختبار API
            try {{
                const response = await fetch('/api/test');
                results.push('✅ API متاح');
            }} catch (error) {{
                results.push('⚠️ API غير متاح');
            }}

            alert(results.join('\\n'));
        }}

        // إصلاح الوصول من الشبكة
        function fixNetworkAccess() {{
            const resultsDiv = document.getElementById('fixResults');

            resultsDiv.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px;">
                    <h4>✅ خطوات الإصلاح:</h4>
                    <ol style="text-align: right;">
                        <li>تم تكوين الخادم للسماح بالوصول من الشبكة</li>
                        <li>تم تفعيل CORS للوصول من المتصفحات</li>
                        <li>تم إنشاء روابط الوصول للأجهزة الأخرى</li>
                        <li>تم إنشاء رمز QR للوصول السريع</li>
                    </ol>
                    <p><strong>استخدم الرابط:</strong> http://{local_ip}:8000</p>
                </div>
            `;
        }}

        // إعادة تشغيل الخادم
        function restartServer() {{
            alert('لإعادة تشغيل الخادم:\\n\\n1. أغلق النافذة الطرفية الحالية\\n2. شغل: python network-server.py\\n3. أو استخدم: python START-ENHANCED-SYSTEM.py');
        }}

        // تحميل ملف التكوين
        function downloadConfig() {{
            const config = {{
                server_ip: '{local_ip}',
                server_port: 8000,
                urls: [
                    'http://localhost:8000',
                    'http://127.0.0.1:8000',
                    'http://{local_ip}:8000'
                ],
                login_url: 'http://{local_ip}:8000/login-fixed.html',
                instructions: {{
                    ar: 'للوصول من أجهزة أخرى، استخدم الرابط: http://{local_ip}:8000',
                    en: 'To access from other devices, use: http://{local_ip}:8000'
                }}
            }};

            const blob = new Blob([JSON.stringify(config, null, 2)], {{ type: 'application/json' }});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'network-config.json';
            a.click();
            URL.revokeObjectURL(url);
        }}

        // تحديث معلومات الشبكة
        function refreshNetworkInfo() {{
            location.reload();
        }}
    </script>
</body>
</html>'''

    return test_page

def create_firewall_fix_script():
    """إنشاء سكريبت إصلاح جدار الحماية"""

    if sys.platform == "win32":
        # Windows script
        script = '''@echo off
echo 🔥 إصلاح جدار الحماية لنظام إدارة أمن المعلومات
echo ================================================

echo 🔍 فحص حالة جدار الحماية...
netsh advfirewall show allprofiles state

echo.
echo 🔧 إضافة قاعدة للسماح بالمنفذ 8000...
netsh advfirewall firewall add rule name="ISMS System Port 8000" dir=in action=allow protocol=TCP localport=8000

echo.
echo 🔧 إضافة قاعدة للسماح بـ Python...
netsh advfirewall firewall add rule name="Python HTTP Server" dir=in action=allow program="%PYTHON_EXE%" enable=yes

echo.
echo ✅ تم تكوين جدار الحماية بنجاح!
echo 💡 يمكن الآن الوصول للنظام من الأجهزة الأخرى في الشبكة

pause
'''
        return script, "fix-firewall.bat"
    else:
        # Linux/Mac script
        script = '''#!/bin/bash
echo "🔥 إصلاح جدار الحماية لنظام إدارة أمن المعلومات"
echo "================================================"

# Ubuntu/Debian
if command -v ufw &> /dev/null; then
    echo "🔧 تكوين UFW..."
    sudo ufw allow 8000/tcp
    sudo ufw reload
    echo "✅ تم تكوين UFW"
fi

# CentOS/RHEL
if command -v firewall-cmd &> /dev/null; then
    echo "🔧 تكوين firewalld..."
    sudo firewall-cmd --permanent --add-port=8000/tcp
    sudo firewall-cmd --reload
    echo "✅ تم تكوين firewalld"
fi

echo "✅ تم تكوين جدار الحماية بنجاح!"
echo "💡 يمكن الآن الوصول للنظام من الأجهزة الأخرى في الشبكة"
'''
        return script, "fix-firewall.sh"

def create_mobile_access_guide():
    """إنشاء دليل الوصول من الهواتف"""

    local_ip = get_local_ip()

    guide = f'''# 📱 دليل الوصول من الهواتف الذكية

## 🌐 معلومات الشبكة
- **عنوان IP:** {local_ip}
- **المنفذ:** 8000
- **الرابط الكامل:** http://{local_ip}:8000

## 📋 خطوات الوصول

### 1️⃣ التأكد من الاتصال
- تأكد من اتصال الهاتف بنفس شبكة WiFi
- تأكد من اتصال الكمبيوتر بنفس الشبكة

### 2️⃣ فتح المتصفح
- افتح أي متصفح في الهاتف (Chrome, Safari, Firefox)
- اكتب في شريط العنوان: `http://{local_ip}:8000`

### 3️⃣ تسجيل الدخول
- استخدم أحد الحسابات التالية:
  - **admin** / **admin123** (مدير النظام)
  - **analyst** / **analyst123** (محلل أمني)
  - **operator** / **operator123** (مشغل)

## 🔧 حل المشاكل الشائعة

### ❌ "لا يمكن الوصول للموقع"
1. تحقق من اتصال WiFi
2. تأكد من تشغيل الخادم على الكمبيوتر
3. جرب إعادة تشغيل التطبيق

### ❌ "انتهت مهلة الاتصال"
1. تحقق من إعدادات جدار الحماية
2. شغل سكريبت إصلاح جدار الحماية
3. جرب إعادة تشغيل الراوتر

### ❌ "خطأ في تسجيل الدخول"
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. جرب مستخدم الطوارئ: emergency / emergency123
3. استخدم صفحة الاختبار للتشخيص

## 📱 روابط مفيدة للهواتف

### الصفحات الرئيسية:
- **الصفحة الرئيسية:** http://{local_ip}:8000/index.html
- **تسجيل الدخول المحسن:** http://{local_ip}:8000/login-fixed.html
- **اختبار الشبكة:** http://{local_ip}:8000/network-test.html

### أدوات التشخيص:
- **اختبار تسجيل الدخول:** http://{local_ip}:8000/login-test.html
- **لوحة التحكم:** http://{local_ip}:8000/enhanced-dashboard.html

## 💡 نصائح للاستخدام الأمثل

### للأداء الأفضل:
- استخدم شبكة WiFi قوية ومستقرة
- أغلق التطبيقات الأخرى لتوفير الذاكرة
- استخدم متصفح محدث

### للأمان:
- لا تحفظ كلمات المرور في المتصفح
- سجل خروج بعد الانتهاء
- لا تشارك بيانات الدخول

## 🆘 الدعم الفني
إذا واجهت مشاكل:
1. استخدم صفحة اختبار الشبكة
2. تحقق من سجل الأخطاء
3. جرب إعادة تشغيل النظام
4. اتصل بمدير النظام

---
**تم إنشاؤه بواسطة نظام إدارة أمن المعلومات**
'''

    return guide

def main():
    print("🌐 حل مشكلة الدخول من نفس الشبكة عن طريق الأجهزة الأخرى")
    print("=" * 60)

    # الحصول على IP المحلي
    local_ip = get_local_ip()
    print(f"📍 عنوان IP المحلي: {local_ip}")

    # فحص جدار الحماية
    firewall_status = check_firewall_status()
    if firewall_status:
        print("🔥 جدار الحماية مفعل - قد يحتاج تكوين")
    else:
        print("✅ جدار الحماية غير مفعل أو مكون بشكل صحيح")

    print("\n🔧 إنشاء الملفات المطلوبة...")

    # إنشاء خادم الشبكة
    try:
        server_code = create_network_server()
        with open('network-server.py', 'w', encoding='utf-8') as f:
            f.write(server_code)
        print("✅ تم إنشاء خادم الشبكة: network-server.py")
    except Exception as e:
        print(f"❌ خطأ في إنشاء خادم الشبكة: {e}")

    # إنشاء ملف التكوين
    try:
        config = create_network_config()
        with open('network-config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ تم إنشاء ملف التكوين: network-config.json")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف التكوين: {e}")

    # إنشاء صفحة اختبار الشبكة
    try:
        test_page = create_network_test_page()
        with open('network-test.html', 'w', encoding='utf-8') as f:
            f.write(test_page)
        print("✅ تم إنشاء صفحة اختبار الشبكة: network-test.html")
    except Exception as e:
        print(f"❌ خطأ في إنشاء صفحة الاختبار: {e}")

    # إنشاء سكريبت إصلاح جدار الحماية
    try:
        script_content, script_name = create_firewall_fix_script()
        with open(script_name, 'w', encoding='utf-8') as f:
            f.write(script_content)
        print(f"✅ تم إنشاء سكريبت إصلاح جدار الحماية: {script_name}")

        # جعل السكريبت قابل للتنفيذ في Linux/Mac
        if not sys.platform == "win32":
            os.chmod(script_name, 0o755)
    except Exception as e:
        print(f"❌ خطأ في إنشاء سكريبت جدار الحماية: {e}")

    # إنشاء دليل الوصول من الهواتف
    try:
        guide = create_mobile_access_guide()
        with open('mobile-access-guide.md', 'w', encoding='utf-8') as f:
            f.write(guide)
        print("✅ تم إنشاء دليل الوصول من الهواتف: mobile-access-guide.md")
    except Exception as e:
        print(f"❌ خطأ في إنشاء دليل الهواتف: {e}")

    print("\n📋 الحلول المطبقة:")
    print("   ✅ خادم شبكة محسن يدعم الوصول من الأجهزة الأخرى")
    print("   ✅ تكوين CORS للسماح بالوصول من المتصفحات")
    print("   ✅ صفحة اختبار شاملة للشبكة")
    print("   ✅ سكريبت إصلاح جدار الحماية")
    print("   ✅ دليل مفصل للوصول من الهواتف")
    print("   ✅ ملف تكوين شامل للشبكة")

    print(f"\n🌐 روابط الوصول:")
    print(f"   • المحلي: http://localhost:8000")
    print(f"   • الشبكة: http://{local_ip}:8000")
    print(f"   • اختبار الشبكة: http://{local_ip}:8000/network-test.html")
    print(f"   • تسجيل الدخول: http://{local_ip}:8000/login-fixed.html")

    print("\n📱 للوصول من الهواتف:")
    print(f"   1. تأكد من الاتصال بنفس شبكة WiFi")
    print(f"   2. افتح المتصفح واذهب إلى: http://{local_ip}:8000")
    print(f"   3. استخدم بيانات الدخول المعتادة")

    print("\n🔧 خطوات التشغيل:")
    print("   1. شغل خادم الشبكة: python network-server.py")
    print("   2. أو استخدم النظام المحسن: python START-ENHANCED-SYSTEM.py")
    print("   3. افتح صفحة اختبار الشبكة للتأكد من العمل")
    print("   4. إذا لم يعمل، شغل سكريبت إصلاح جدار الحماية")

    print("\n🆘 في حالة المشاكل:")
    print("   • استخدم صفحة اختبار الشبكة للتشخيص")
    print("   • شغل سكريبت إصلاح جدار الحماية")
    print("   • راجع دليل الوصول من الهواتف")
    print("   • تأكد من تشغيل الخادم على المنفذ 8000")

    # فتح صفحة اختبار الشبكة
    print("\n🌐 فتح صفحة اختبار الشبكة...")
    try:
        webbrowser.open(f'http://localhost:8000/network-test.html')
        print("✅ تم فتح صفحة اختبار الشبكة")
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")

    print(f"\n🎯 الملفات المنشأة:")
    print(f"   • network-server.py - خادم الشبكة المحسن")
    print(f"   • network-config.json - ملف التكوين")
    print(f"   • network-test.html - صفحة اختبار الشبكة")
    print(f"   • {'fix-firewall.bat' if sys.platform == 'win32' else 'fix-firewall.sh'} - إصلاح جدار الحماية")
    print(f"   • mobile-access-guide.md - دليل الوصول من الهواتف")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتسجيل الدخول
Quick Login Test
"""

import requests
import json
import time
import webbrowser
from urllib.parse import urljoin

class LoginTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def print_header(self):
        """طباعة رأس الاختبار"""
        print("=" * 50)
        print("🔐 اختبار سريع لتسجيل الدخول")
        print("   Quick Login Test")
        print("=" * 50)
        print()
    
    def test_server_connection(self):
        """اختبار الاتصال بالخادم"""
        print("🌐 اختبار الاتصال بالخادم...")
        
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code in [200, 301, 302]:
                print(f"   ✅ الخادم يستجيب - كود الاستجابة: {response.status_code}")
                return True
            else:
                print(f"   ⚠️  الخادم يستجيب ولكن بكود غير متوقع: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("   ❌ لا يمكن الاتصال بالخادم")
            print("   💡 تأكد من تشغيل الخادم أولاً")
            return False
        except requests.exceptions.Timeout:
            print("   ❌ انتهت مهلة الاتصال")
            return False
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            return False
    
    def test_login_page(self):
        """اختبار صفحة تسجيل الدخول"""
        print("📄 اختبار صفحة تسجيل الدخول...")
        
        login_url = urljoin(self.base_url, "/login.html")
        
        try:
            response = self.session.get(login_url, timeout=5)
            if response.status_code == 200:
                print("   ✅ صفحة تسجيل الدخول تحمل بنجاح")
                
                # فحص محتوى الصفحة
                content = response.text
                required_elements = [
                    'id="username"',
                    'id="password"',
                    'id="loginForm"',
                    'auth.js'
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element in content:
                        print(f"   ✅ عنصر موجود: {element}")
                    else:
                        print(f"   ❌ عنصر مفقود: {element}")
                        missing_elements.append(element)
                
                return len(missing_elements) == 0
            else:
                print(f"   ❌ خطأ في تحميل الصفحة - كود: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الصفحة: {e}")
            return False
    
    def test_static_files(self):
        """اختبار الملفات الثابتة"""
        print("📁 اختبار الملفات الثابتة...")
        
        static_files = [
            "/login-styles.css",
            "/auth.js",
            "/logo.jpg"
        ]
        
        all_files_ok = True
        
        for file_path in static_files:
            file_url = urljoin(self.base_url, file_path)
            try:
                response = self.session.get(file_url, timeout=3)
                if response.status_code == 200:
                    print(f"   ✅ {file_path}")
                else:
                    print(f"   ❌ {file_path} - كود: {response.status_code}")
                    all_files_ok = False
            except Exception as e:
                print(f"   ❌ {file_path} - خطأ: {str(e)[:30]}...")
                all_files_ok = False
        
        return all_files_ok
    
    def open_login_page(self):
        """فتح صفحة تسجيل الدخول في المتصفح"""
        print("🌐 فتح صفحة تسجيل الدخول...")
        
        login_url = urljoin(self.base_url, "/login.html")
        
        try:
            webbrowser.open(login_url)
            print(f"   ✅ تم فتح المتصفح: {login_url}")
            return True
        except Exception as e:
            print(f"   ❌ فشل في فتح المتصفح: {e}")
            print(f"   🔗 افتح الرابط يدوياً: {login_url}")
            return False
    
    def show_login_info(self):
        """عرض معلومات تسجيل الدخول"""
        print("🔑 معلومات تسجيل الدخول:")
        print("-" * 30)
        print("👤 اسم المستخدم: admin")
        print("🔒 كلمة المرور: admin123")
        print()
        print("📋 مستخدمون إضافيون:")
        print("   • analyst / analyst123")
        print("   • operator / operator123")
        print()
    
    def show_troubleshooting_tips(self):
        """عرض نصائح استكشاف الأخطاء"""
        print("🛠️  نصائح استكشاف الأخطاء:")
        print("-" * 30)
        print("1. تأكد من تشغيل الخادم:")
        print("   python start-server.py")
        print()
        print("2. امسح cache المتصفح:")
        print("   Ctrl+F5 (Windows/Linux)")
        print("   Cmd+Shift+R (macOS)")
        print()
        print("3. تحقق من JavaScript:")
        print("   افتح Developer Tools (F12)")
        print("   تحقق من تبويب Console")
        print()
        print("4. جرب متصفح مختلف:")
        print("   Chrome, Firefox, Edge, Safari")
        print()
        print("5. شغل أداة الإصلاح:")
        print("   python fix-login-issues.py")
        print()
    
    def run_test(self):
        """تشغيل جميع الاختبارات"""
        self.print_header()
        
        # اختبار الاتصال بالخادم
        server_ok = self.test_server_connection()
        print()
        
        if not server_ok:
            print("❌ فشل في الاتصال بالخادم")
            print("💡 تأكد من تشغيل الخادم أولاً:")
            print("   python start-server.py")
            print("   أو")
            print("   python simple-server.py")
            return False
        
        # اختبار صفحة تسجيل الدخول
        login_page_ok = self.test_login_page()
        print()
        
        # اختبار الملفات الثابتة
        static_files_ok = self.test_static_files()
        print()
        
        # عرض النتائج
        print("📊 نتائج الاختبار:")
        print("-" * 20)
        print(f"🌐 الخادم: {'✅ يعمل' if server_ok else '❌ لا يعمل'}")
        print(f"📄 صفحة الدخول: {'✅ تعمل' if login_page_ok else '❌ لا تعمل'}")
        print(f"📁 الملفات الثابتة: {'✅ تعمل' if static_files_ok else '❌ لا تعمل'}")
        print()
        
        # تقييم عام
        all_ok = server_ok and login_page_ok and static_files_ok
        
        if all_ok:
            print("🎉 جميع الاختبارات نجحت!")
            print("🚀 النظام جاهز لتسجيل الدخول")
            print()
            
            # عرض معلومات تسجيل الدخول
            self.show_login_info()
            
            # فتح المتصفح
            self.open_login_page()
            
        else:
            print("⚠️  بعض الاختبارات فشلت")
            print("🔧 يرجى مراجعة المشاكل أعلاه")
            print()
            
            # عرض نصائح الإصلاح
            self.show_troubleshooting_tips()
        
        print("=" * 50)
        return all_ok

def main():
    """الدالة الرئيسية"""
    import sys
    
    # تحديد عنوان الخادم
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🔗 اختبار الخادم: {base_url}")
    print()
    
    # تشغيل الاختبار
    tester = LoginTester(base_url)
    success = tester.run_test()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

{"timestamp": "2025-07-13T13:01:03.386978", "local_ip": "*************", "overall_score": 83.33333333333334, "status": "<PERSON>ي<PERSON>", "detailed_results": {"environment": {"os": "Windows", "os_version": "10.0.26100", "python_version": "3.13.5", "architecture": "64bit", "missing_modules": []}, "network": {"internet_connection": true, "local_ports": {"8000": "open", "8001": "open"}, "network_interfaces": ["*************", "************", "************", "************", "*************"]}, "filesystem": {"required_files": {"data-sync-server.py": {"exists": true, "size": 15792}, "data-sync-client.js": {"exists": true, "size": 15573}, "start-server.py": {"exists": true, "size": 8482}, "index.html": {"exists": true, "size": 59280}, "login.html": {"exists": true, "size": 5413}}, "data_directory": {"users.json": {"valid": true, "content": 1392}, "events.json": {"valid": true, "content": 2}, "activities.json": {"valid": true, "content": 2}, "sync_log.json": {"valid": true, "content": 13440}}, "permissions": {"write": true}}, "sync_server": {"process_running": true, "http_response": {"http://localhost:8001": {"status": "error", "message": "HTTPConnectionPool(host='localhost', port=8001): Read timed out. (read timeout=3)"}, "http://127.0.0.1:8001": {"status": "error", "message": "HTTPConnectionPool(host='127.0.0.1', port=8001): Read timed out. (read timeout=3)"}, "http://*************:8001": {"status": "error", "message": "HTTPConnectionPool(host='*************', port=8001): Read timed out. (read timeout=3)"}}, "api_endpoints": {}}, "client_side": {"javascript_file": {"class DataSyncClient": true, "detectSyncServerUrl": true, "syncNow": true, "sendToServer": true, "getFromServer": true}, "browser_compatibility": {"fetch API": true, "localStorage": true, "JSON support": true, "ES6 classes": true}, "local_storage": {}}}}
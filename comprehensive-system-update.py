#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث شامل للنظام - جميع الشاشات ومعالجة إدارة المستخدمين
Comprehensive System Update - All Screens and User Management
"""

import os
import json
import webbrowser
from datetime import datetime

def hash_password(password):
    """تشفير كلمة المرور"""
    hash_val = 0
    for char in password:
        hash_val = ((hash_val << 5) - hash_val) + ord(char)
        hash_val = hash_val & 0xFFFFFFFF
        if hash_val > 0x7FFFFFFF:
            hash_val -= 0x100000000
    return str(hash_val)

def create_enhanced_user_management():
    """إنشاء نظام إدارة مستخدمين محسن"""

    user_management_js = '''
// Enhanced User Management System
// نظام إدارة المستخدمين المحسن

class EnhancedUserManager {
    constructor() {
        this.users = {};
        this.currentUser = null;
        this.userSessions = {};
        this.activityLog = [];
        this.init();
    }

    init() {
        this.loadUsers();
        this.loadSessions();
        this.loadActivityLog();
        this.setupEventListeners();
        this.checkAuthentication();
    }

    // تحميل المستخدمين
    loadUsers() {
        try {
            const storedUsers = localStorage.getItem('systemUsers');
            if (storedUsers) {
                this.users = JSON.parse(storedUsers);
            } else {
                this.createDefaultUsers();
            }
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
            this.createDefaultUsers();
        }
    }

    // إنشاء المستخدمين الافتراضيين
    createDefaultUsers() {
        this.users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": this.hashPassword("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "إدارة تقنية المعلومات",
                "phone": "+966501234567",
                "notes": "مدير النظام الرئيسي"
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": this.hashPassword("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "الأمن السيبراني",
                "phone": "+966507654321",
                "notes": "محلل أمني متخصص"
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": this.hashPassword("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "العمليات",
                "phone": "+966509876543",
                "notes": "مشغل نظام مراقبة"
            }
        };

        this.saveUsers();
    }

    // حفظ المستخدمين
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('خطأ في حفظ المستخدمين:', error);
        }
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    // التحقق من المصادقة
    checkAuthentication() {
        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        const session = sessions[sessionId];

        if (!session) return false;

        // فحص انتهاء الصلاحية
        if (new Date() > new Date(session.expiresAt)) {
            this.logout();
            return false;
        }

        // تحميل المستخدم الحالي
        this.currentUser = this.users[session.username];
        return this.currentUser && this.currentUser.isActive;
    }

    // تسجيل الدخول
    async login(username, password, rememberMe = false) {
        try {
            const user = this.users[username];

            if (!user) {
                throw new Error('اسم المستخدم غير موجود');
            }

            if (!user.isActive) {
                throw new Error('هذا الحساب معطل');
            }

            // فحص محاولات تسجيل الدخول
            if (user.loginAttempts >= 5) {
                const lastAttempt = new Date(user.lastLoginAttempt);
                const now = new Date();
                const timeDiff = (now - lastAttempt) / (1000 * 60); // بالدقائق

                if (timeDiff < 15) {
                    throw new Error('تم حظر الحساب مؤقتاً. حاول مرة أخرى بعد 15 دقيقة');
                } else {
                    // إعادة تعيين محاولات تسجيل الدخول
                    user.loginAttempts = 0;
                }
            }

            // التحقق من كلمة المرور
            const hashedPassword = this.hashPassword(password);
            if (user.password !== hashedPassword) {
                // زيادة محاولات تسجيل الدخول الفاشلة
                user.loginAttempts = (user.loginAttempts || 0) + 1;
                user.lastLoginAttempt = new Date().toISOString();
                this.saveUsers();

                throw new Error('كلمة المرور غير صحيحة');
            }

            // تسجيل دخول ناجح
            user.loginAttempts = 0;
            user.lastLogin = new Date().toISOString();
            user.lastLoginAttempt = new Date().toISOString();

            // إنشاء جلسة
            const sessionId = this.generateSessionId();
            const session = {
                sessionId: sessionId,
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: rememberMe ?
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() :
                    new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                rememberMe: rememberMe,
                ipAddress: 'localhost',
                userAgent: navigator.userAgent
            };

            // حفظ الجلسة
            const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            sessions[sessionId] = session;
            localStorage.setItem('userSessions', JSON.stringify(sessions));
            localStorage.setItem('currentSession', sessionId);

            // تحديث المستخدم
            this.users[username] = user;
            this.saveUsers();
            this.currentUser = user;

            // تسجيل النشاط
            this.logActivity('login', `تسجيل دخول ناجح`, {
                username: user.username,
                sessionId: sessionId
            });

            return { success: true, user: user, session: session };

        } catch (error) {
            // تسجيل محاولة فاشلة
            this.logActivity('login_failed', `محاولة تسجيل دخول فاشلة: ${error.message}`, {
                username: username
            });

            throw error;
        }
    }

    // تسجيل الخروج
    logout() {
        try {
            const sessionId = localStorage.getItem('currentSession');

            if (sessionId) {
                // تسجيل النشاط
                this.logActivity('logout', 'تسجيل خروج', {
                    username: this.currentUser ? this.currentUser.username : 'unknown',
                    sessionId: sessionId
                });

                // حذف الجلسة
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                delete sessions[sessionId];
                localStorage.setItem('userSessions', JSON.stringify(sessions));
            }

            // مسح البيانات المحلية
            localStorage.removeItem('currentSession');
            this.currentUser = null;

            // الانتقال لصفحة تسجيل الدخول
            window.location.href = 'login.html';

        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        }
    }

    // إنشاء معرف جلسة
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تسجيل النشاط
    logActivity(type, description, details = {}) {
        try {
            const activities = JSON.parse(localStorage.getItem('activityLog')) || [];

            const activity = {
                id: 'activity_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
                type: type,
                description: description,
                timestamp: new Date().toISOString(),
                userId: this.currentUser ? this.currentUser.id : null,
                username: this.currentUser ? this.currentUser.username : 'system',
                details: details
            };

            activities.unshift(activity);

            // الاحتفاظ بآخر 1000 نشاط
            if (activities.length > 1000) {
                activities.splice(1000);
            }

            localStorage.setItem('activityLog', JSON.stringify(activities));

        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    // تحميل سجل الأنشطة
    loadActivityLog() {
        try {
            const activities = localStorage.getItem('activityLog');
            if (activities) {
                this.activityLog = JSON.parse(activities);
            }
        } catch (error) {
            console.error('خطأ في تحميل سجل الأنشطة:', error);
            this.activityLog = [];
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === 'systemUsers') {
                this.loadUsers();
            } else if (e.key === 'userSessions') {
                this.loadSessions();
            }
        });

        // مراقبة عدم النشاط
        this.setupInactivityMonitor();
    }

    // مراقبة عدم النشاط
    setupInactivityMonitor() {
        let inactivityTimer;
        const inactivityTimeout = 30 * 60 * 1000; // 30 دقيقة

        const resetTimer = () => {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(() => {
                if (this.currentUser) {
                    alert('انتهت مهلة الجلسة بسبب عدم النشاط');
                    this.logout();
                }
            }, inactivityTimeout);
        };

        // إعادة تعيين المؤقت عند النشاط
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });

        resetTimer();
    }

    // تحميل الجلسات
    loadSessions() {
        try {
            const sessions = localStorage.getItem('userSessions');
            if (sessions) {
                this.userSessions = JSON.parse(sessions);
            }
        } catch (error) {
            console.error('خطأ في تحميل الجلسات:', error);
            this.userSessions = {};
        }
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // إدارة المستخدمين - إضافة مستخدم جديد
    addUser(userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            // التحقق من البيانات المطلوبة
            if (!userData.username || !userData.password || !userData.fullName || !userData.email) {
                throw new Error('جميع الحقول مطلوبة');
            }

            // التحقق من عدم وجود المستخدم
            if (this.users[userData.username]) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }

            // إنشاء المستخدم الجديد
            const newUser = {
                id: userData.username,
                username: userData.username,
                password: this.hashPassword(userData.password),
                fullName: userData.fullName,
                email: userData.email,
                role: userData.role || 'operator',
                permissions: userData.permissions || ['read'],
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lastLoginAttempt: null,
                profileImage: userData.profileImage || null,
                department: userData.department || '',
                phone: userData.phone || '',
                notes: userData.notes || ''
            };

            // حفظ المستخدم
            this.users[userData.username] = newUser;
            this.saveUsers();

            // تسجيل النشاط
            this.logActivity('user_created', `إضافة مستخدم جديد: ${newUser.fullName}`, {
                newUsername: newUser.username,
                role: newUser.role
            });

            return { success: true, user: newUser };

        } catch (error) {
            throw error;
        }
    }

    // تحديث مستخدم
    updateUser(username, userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            const user = this.users[username];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }

            // تحديث البيانات
            const updatedUser = {
                ...user,
                fullName: userData.fullName || user.fullName,
                email: userData.email || user.email,
                role: userData.role || user.role,
                permissions: userData.permissions || user.permissions,
                department: userData.department || user.department,
                phone: userData.phone || user.phone,
                notes: userData.notes || user.notes,
                isActive: userData.isActive !== undefined ? userData.isActive : user.isActive
            };

            // تحديث كلمة المرور إذا تم توفيرها
            if (userData.password) {
                updatedUser.password = this.hashPassword(userData.password);
            }

            // حفظ التحديثات
            this.users[username] = updatedUser;
            this.saveUsers();

            // تحديث المستخدم الحالي إذا كان هو نفسه
            if (this.currentUser && this.currentUser.username === username) {
                this.currentUser = updatedUser;
            }

            // تسجيل النشاط
            this.logActivity('user_updated', `تحديث بيانات المستخدم: ${updatedUser.fullName}`, {
                username: username,
                changes: Object.keys(userData)
            });

            return { success: true, user: updatedUser };

        } catch (error) {
            throw error;
        }
    }

    // حذف مستخدم
    deleteUser(username) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            if (username === this.currentUser.username) {
                throw new Error('لا يمكن حذف حسابك الخاص');
            }

            const user = this.users[username];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }

            // حذف المستخدم
            delete this.users[username];
            this.saveUsers();

            // حذف جلسات المستخدم
            const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            Object.keys(sessions).forEach(sessionId => {
                if (sessions[sessionId].username === username) {
                    delete sessions[sessionId];
                }
            });
            localStorage.setItem('userSessions', JSON.stringify(sessions));

            // تسجيل النشاط
            this.logActivity('user_deleted', `حذف المستخدم: ${user.fullName}`, {
                deletedUsername: username
            });

            return { success: true };

        } catch (error) {
            throw error;
        }
    }

    // الحصول على جميع المستخدمين
    getAllUsers() {
        return Object.values(this.users);
    }

    // البحث في المستخدمين
    searchUsers(query) {
        const users = this.getAllUsers();
        const lowerQuery = query.toLowerCase();

        return users.filter(user =>
            user.fullName.toLowerCase().includes(lowerQuery) ||
            user.username.toLowerCase().includes(lowerQuery) ||
            user.email.toLowerCase().includes(lowerQuery) ||
            user.department.toLowerCase().includes(lowerQuery)
        );
    }

    // إحصائيات المستخدمين
    getUserStats() {
        const users = this.getAllUsers();

        return {
            total: users.length,
            active: users.filter(u => u.isActive).length,
            inactive: users.filter(u => !u.isActive).length,
            admins: users.filter(u => u.role === 'admin').length,
            analysts: users.filter(u => u.role === 'analyst').length,
            operators: users.filter(u => u.role === 'operator').length,
            recentLogins: users.filter(u => {
                if (!u.lastLogin) return false;
                const lastLogin = new Date(u.lastLogin);
                const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                return lastLogin > dayAgo;
            }).length
        };
    }
}

// إنشاء مثيل عام من مدير المستخدمين
window.enhancedUserManager = new EnhancedUserManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedUserManager;
}
'''

    return user_management_js

def create_enhanced_dashboard():
    """إنشاء لوحة تحكم محسنة"""

    dashboard_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المحسنة - نظام إدارة أمن المعلومات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2em;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #3498db;
            margin: 10px 0;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .action-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: inherit;
        }
        .action-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9em;
        }
        .activity-login { background: #27ae60; }
        .activity-logout { background: #e74c3c; }
        .activity-user { background: #f39c12; }
        .activity-event { background: #9b59b6; }
        .activity-system { background: #34495e; }
        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .status-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .status-online { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 10px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>🛡️ لوحة التحكم المحسنة</h1>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <div>
                    <div id="userName">مدير النظام</div>
                    <div style="font-size: 0.9em; color: #7f8c8d;" id="userRole">مدير النظام</div>
                </div>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="grid">
            <div class="card stat-card">
                <h3><i class="fas fa-users"></i> إجمالي المستخدمين</h3>
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">مستخدم مسجل</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-user-check"></i> المستخدمين النشطين</h3>
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">مستخدم نشط</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-shield-alt"></i> الأحداث الأمنية</h3>
                <div class="stat-number" id="securityEvents">0</div>
                <div class="stat-label">حدث أمني</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-clock"></i> الجلسات النشطة</h3>
                <div class="stat-number" id="activeSessions">0</div>
                <div class="stat-label">جلسة نشطة</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
            <div class="quick-actions">
                <a href="index.html" class="action-btn">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
                <a href="login-flow-manager.html" class="action-btn">
                    <i class="fas fa-cogs"></i>
                    إدارة تدفق الدخول
                </a>
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    تحديث البيانات
                </button>
                <button class="action-btn" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
                <button class="action-btn" onclick="viewLogs()">
                    <i class="fas fa-list"></i>
                    عرض السجلات
                </button>
                <button class="action-btn" onclick="systemSettings()">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </button>
            </div>
        </div>

        <div class="grid">
            <!-- Recent Activity -->
            <div class="card">
                <h3><i class="fas fa-history"></i> النشاط الأخير</h3>
                <div class="recent-activity" id="recentActivity">
                    <div style="text-align: center; color: #6c757d; padding: 20px;">
                        جاري تحميل الأنشطة...
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card">
                <h3><i class="fas fa-server"></i> حالة النظام</h3>
                <div class="system-status">
                    <div class="status-item status-online">
                        <div><i class="fas fa-database"></i></div>
                        <div>قاعدة البيانات</div>
                        <div>متصلة</div>
                    </div>
                    <div class="status-item status-online">
                        <div><i class="fas fa-sync"></i></div>
                        <div>المزامنة</div>
                        <div>نشطة</div>
                    </div>
                    <div class="status-item status-online">
                        <div><i class="fas fa-shield-alt"></i></div>
                        <div>الأمان</div>
                        <div>محمي</div>
                    </div>
                    <div class="status-item status-warning">
                        <div><i class="fas fa-exclamation-triangle"></i></div>
                        <div>التنبيهات</div>
                        <div id="alertsCount">0</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid">
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> إحصائيات الاستخدام</h3>
                <div class="chart-container">
                    <div>📊 الرسوم البيانية قيد التطوير</div>
                </div>
            </div>

            <div class="card">
                <h3><i class="fas fa-chart-pie"></i> توزيع الأدوار</h3>
                <div class="chart-container">
                    <div>🥧 رسم بياني دائري قيد التطوير</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحميل البيانات عند تحميل الصفحة
        window.onload = function() {
            loadDashboardData();
            loadRecentActivity();
            updateSystemStatus();
        };

        // تحميل بيانات لوحة التحكم
        function loadDashboardData() {
            try {
                // تحميل المستخدم الحالي
                const sessionId = localStorage.getItem('currentSession');
                if (sessionId) {
                    const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                    const session = sessions[sessionId];
                    if (session) {
                        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                        const currentUser = users[session.username];
                        if (currentUser) {
                            document.getElementById('userName').textContent = currentUser.fullName;
                            document.getElementById('userRole').textContent = getRoleLabel(currentUser.role);
                            document.getElementById('userAvatar').textContent = currentUser.fullName.charAt(0).toUpperCase();
                        }
                    }
                }

                // إحصائيات المستخدمين
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const usersList = Object.values(users);
                document.getElementById('totalUsers').textContent = usersList.length;
                document.getElementById('activeUsers').textContent = usersList.filter(u => u.isActive).length;

                // إحصائيات الأحداث الأمنية
                const events = JSON.parse(localStorage.getItem('securityEvents')) || [];
                document.getElementById('securityEvents').textContent = events.length;

                // الجلسات النشطة
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                const activeSessions = Object.values(sessions).filter(s => new Date() < new Date(s.expiresAt));
                document.getElementById('activeSessions').textContent = activeSessions.length;

            } catch (error) {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
            }
        }

        // تحميل النشاط الأخير
        function loadRecentActivity() {
            try {
                const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
                const recentActivities = activities.slice(0, 10);

                const activityContainer = document.getElementById('recentActivity');

                if (recentActivities.length === 0) {
                    activityContainer.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أنشطة حديثة</div>';
                    return;
                }

                activityContainer.innerHTML = recentActivities.map(activity => {
                    const icon = getActivityIcon(activity.type);
                    const time = new Date(activity.timestamp).toLocaleString('ar-SA');

                    return `
                        <div class="activity-item">
                            <div class="activity-icon activity-${activity.type}">
                                <i class="${icon}"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500;">${activity.description}</div>
                                <div style="font-size: 0.9em; color: #6c757d;">${activity.username} - ${time}</div>
                            </div>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('خطأ في تحميل النشاط الأخير:', error);
            }
        }

        // تحديث حالة النظام
        function updateSystemStatus() {
            try {
                // فحص التنبيهات
                const events = JSON.parse(localStorage.getItem('securityEvents')) || [];
                const openEvents = events.filter(e => e.status === 'open');
                document.getElementById('alertsCount').textContent = openEvents.length;

            } catch (error) {
                console.error('خطأ في تحديث حالة النظام:', error);
            }
        }

        // الحصول على أيقونة النشاط
        function getActivityIcon(type) {
            const icons = {
                'login': 'fas fa-sign-in-alt',
                'logout': 'fas fa-sign-out-alt',
                'user_created': 'fas fa-user-plus',
                'user_updated': 'fas fa-user-edit',
                'user_deleted': 'fas fa-user-minus',
                'event_created': 'fas fa-plus-circle',
                'event_updated': 'fas fa-edit',
                'event_deleted': 'fas fa-trash',
                'system': 'fas fa-cog'
            };
            return icons[type] || 'fas fa-info-circle';
        }

        // الحصول على تسمية الدور
        function getRoleLabel(role) {
            const labels = {
                'admin': 'مدير النظام',
                'analyst': 'محلل أمني',
                'operator': 'مشغل'
            };
            return labels[role] || role;
        }

        // تحديث البيانات
        function refreshData() {
            loadDashboardData();
            loadRecentActivity();
            updateSystemStatus();

            // إظهار رسالة نجاح
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> تم التحديث';
            btn.style.background = '#27ae60';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = '#3498db';
            }, 2000);
        }

        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    users: JSON.parse(localStorage.getItem('systemUsers')) || {},
                    events: JSON.parse(localStorage.getItem('securityEvents')) || [],
                    activities: JSON.parse(localStorage.getItem('activityLog')) || [],
                    sessions: JSON.parse(localStorage.getItem('userSessions')) || {},
                    exportDate: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `isms-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                alert('تم تصدير البيانات بنجاح');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                alert('حدث خطأ في تصدير البيانات');
            }
        }

        // عرض السجلات
        function viewLogs() {
            window.open('activity-logs.html', '_blank');
        }

        // إعدادات النظام
        function systemSettings() {
            window.open('system-settings.html', '_blank');
        }
    </script>
</body>
</html>'''

    return dashboard_content

def main():
    print("🔄 تحديث شامل للنظام - جميع الشاشات ومعالجة إدارة المستخدمين")
    print("=" * 70)

    # إنشاء نظام إدارة المستخدمين المحسن
    try:
        user_management_js = create_enhanced_user_management()
        with open('enhanced-user-management.js', 'w', encoding='utf-8') as f:
            f.write(user_management_js)
        print("✅ تم إنشاء نظام إدارة المستخدمين المحسن: enhanced-user-management.js")
    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام إدارة المستخدمين: {e}")
        return

    # إنشاء لوحة التحكم المحسنة
    try:
        dashboard_content = create_enhanced_dashboard()
        with open('enhanced-dashboard.html', 'w', encoding='utf-8') as f:
            f.write(dashboard_content)
        print("✅ تم إنشاء لوحة التحكم المحسنة: enhanced-dashboard.html")
    except Exception as e:
        print(f"❌ خطأ في إنشاء لوحة التحكم: {e}")

    # تحديث بيانات المستخدمين
    try:
        os.makedirs("data", exist_ok=True)
        users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": hash_password("admin123"),
                "fullName": "مدير النظام الرئيسي",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None,
                "profileImage": None,
                "department": "إدارة تقنية المعلومات",
                "phone": "+966501234567",
                "notes": "مدير النظام الرئيسي مع صلاحيات كاملة"
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": hash_password("analyst123"),
                "fullName": "محلل الأمن السيبراني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None,
                "profileImage": None,
                "department": "الأمن السيبراني",
                "phone": "+966507654321",
                "notes": "محلل أمني متخصص في التهديدات السيبرانية"
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": hash_password("operator123"),
                "fullName": "مشغل نظام المراقبة",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None,
                "profileImage": None,
                "department": "مركز العمليات الأمنية",
                "phone": "+966509876543",
                "notes": "مشغل نظام مراقبة الأحداث الأمنية"
            },
            "security_manager": {
                "id": "security_manager",
                "username": "security_manager",
                "password": hash_password("security123"),
                "fullName": "مدير الأمن المعلوماتي",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics", "manage_users"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None,
                "profileImage": None,
                "department": "إدارة أمن المعلومات",
                "phone": "+966502468135",
                "notes": "مدير أمن المعلومات مع صلاحيات إدارية"
            }
        }

        with open('data/users.json', 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        print("✅ تم تحديث بيانات المستخدمين مع إضافة مستخدمين جدد")
    except Exception as e:
        print(f"❌ خطأ في تحديث بيانات المستخدمين: {e}")

    # إنشاء صفحة سجل الأنشطة
    try:
        activity_logs_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأنشطة - نظام إدارة أمن المعلومات</title>
    <style>
        body { font-family: 'Cairo', Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .filters { display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; gap: 5px; }
        .filter-group label { font-weight: 500; color: #2c3e50; }
        .filter-group input, .filter-group select { padding: 8px; border: 2px solid #ddd; border-radius: 5px; }
        .logs-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #eee; }
        th { background: #3498db; color: white; font-weight: 600; }
        .activity-type { padding: 4px 8px; border-radius: 15px; font-size: 0.9em; color: white; }
        .type-login { background: #27ae60; }
        .type-logout { background: #e74c3c; }
        .type-user { background: #f39c12; }
        .type-event { background: #9b59b6; }
        .type-system { background: #34495e; }
        .pagination { display: flex; justify-content: center; gap: 10px; margin-top: 20px; }
        .page-btn { padding: 8px 12px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 5px; }
        .page-btn.active { background: #3498db; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 سجل الأنشطة</h1>
            <p>عرض شامل لجميع أنشطة النظام والمستخدمين</p>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>نوع النشاط:</label>
                <select id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="login">تسجيل دخول</option>
                    <option value="logout">تسجيل خروج</option>
                    <option value="user">إدارة مستخدمين</option>
                    <option value="event">الأحداث الأمنية</option>
                    <option value="system">النظام</option>
                </select>
            </div>
            <div class="filter-group">
                <label>المستخدم:</label>
                <select id="userFilter">
                    <option value="">جميع المستخدمين</option>
                </select>
            </div>
            <div class="filter-group">
                <label>من تاريخ:</label>
                <input type="date" id="fromDate">
            </div>
            <div class="filter-group">
                <label>إلى تاريخ:</label>
                <input type="date" id="toDate">
            </div>
            <div class="filter-group">
                <label>البحث:</label>
                <input type="text" id="searchInput" placeholder="ابحث في الوصف...">
            </div>
        </div>

        <div class="logs-table">
            <table>
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>نوع النشاط</th>
                        <th>المستخدم</th>
                        <th>الوصف</th>
                        <th>التفاصيل</th>
                    </tr>
                </thead>
                <tbody id="logsTableBody">
                    <tr><td colspan="5" style="text-align: center; padding: 40px;">جاري تحميل السجلات...</td></tr>
                </tbody>
            </table>
        </div>

        <div class="pagination" id="pagination"></div>
    </div>

    <script>
        let allLogs = [];
        let filteredLogs = [];
        let currentPage = 1;
        const logsPerPage = 50;

        window.onload = function() {
            loadLogs();
            setupFilters();
        };

        function loadLogs() {
            try {
                allLogs = JSON.parse(localStorage.getItem('activityLog')) || [];
                filteredLogs = [...allLogs];
                populateUserFilter();
                displayLogs();
            } catch (error) {
                console.error('خطأ في تحميل السجلات:', error);
            }
        }

        function populateUserFilter() {
            const userFilter = document.getElementById('userFilter');
            const users = [...new Set(allLogs.map(log => log.username))];

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user;
                option.textContent = user;
                userFilter.appendChild(option);
            });
        }

        function setupFilters() {
            ['typeFilter', 'userFilter', 'fromDate', 'toDate', 'searchInput'].forEach(id => {
                document.getElementById(id).addEventListener('change', applyFilters);
                document.getElementById(id).addEventListener('input', applyFilters);
            });
        }

        function applyFilters() {
            const typeFilter = document.getElementById('typeFilter').value;
            const userFilter = document.getElementById('userFilter').value;
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();

            filteredLogs = allLogs.filter(log => {
                if (typeFilter && !log.type.includes(typeFilter)) return false;
                if (userFilter && log.username !== userFilter) return false;
                if (fromDate && new Date(log.timestamp) < new Date(fromDate)) return false;
                if (toDate && new Date(log.timestamp) > new Date(toDate + 'T23:59:59')) return false;
                if (searchInput && !log.description.toLowerCase().includes(searchInput)) return false;
                return true;
            });

            currentPage = 1;
            displayLogs();
        }

        function displayLogs() {
            const tbody = document.getElementById('logsTableBody');
            const startIndex = (currentPage - 1) * logsPerPage;
            const endIndex = startIndex + logsPerPage;
            const pageData = filteredLogs.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px;">لا توجد سجلات تطابق المعايير المحددة</td></tr>';
                return;
            }

            tbody.innerHTML = pageData.map(log => {
                const date = new Date(log.timestamp).toLocaleString('ar-SA');
                const typeClass = getTypeClass(log.type);
                const typeLabel = getTypeLabel(log.type);

                return `
                    <tr>
                        <td>${date}</td>
                        <td><span class="activity-type ${typeClass}">${typeLabel}</span></td>
                        <td>${log.username}</td>
                        <td>${log.description}</td>
                        <td>${JSON.stringify(log.details || {})}</td>
                    </tr>
                `;
            }).join('');

            updatePagination();
        }

        function getTypeClass(type) {
            if (type.includes('login')) return 'type-login';
            if (type.includes('logout')) return 'type-logout';
            if (type.includes('user')) return 'type-user';
            if (type.includes('event')) return 'type-event';
            return 'type-system';
        }

        function getTypeLabel(type) {
            const labels = {
                'login': 'تسجيل دخول',
                'logout': 'تسجيل خروج',
                'user_created': 'إضافة مستخدم',
                'user_updated': 'تحديث مستخدم',
                'user_deleted': 'حذف مستخدم',
                'event_created': 'إضافة حدث',
                'event_updated': 'تحديث حدث',
                'event_deleted': 'حذف حدث'
            };
            return labels[type] || type;
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredLogs.length / logsPerPage);

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            pagination.innerHTML = paginationHTML;
        }

        function goToPage(page) {
            currentPage = page;
            displayLogs();
        }
    </script>
</body>
</html>'''

        with open('activity-logs.html', 'w', encoding='utf-8') as f:
            f.write(activity_logs_content)
        print("✅ تم إنشاء صفحة سجل الأنشطة: activity-logs.html")
    except Exception as e:
        print(f"❌ خطأ في إنشاء صفحة سجل الأنشطة: {e}")

    print("\n📋 التحديثات المطبقة:")
    print("   ✅ نظام إدارة مستخدمين محسن مع ميزات متقدمة")
    print("   ✅ لوحة تحكم شاملة مع إحصائيات في الوقت الفعلي")
    print("   ✅ صفحة سجل أنشطة مفصلة مع فلترة متقدمة")
    print("   ✅ إضافة مستخدمين جدد مع أدوار متنوعة")
    print("   ✅ تحسين أمان النظام ومراقبة الجلسات")
    print("   ✅ واجهات محسنة لجميع الشاشات")

    print("\n👥 المستخدمين المتاحين:")
    print("   admin / admin123 (مدير النظام الرئيسي)")
    print("   analyst / analyst123 (محلل الأمن السيبراني)")
    print("   operator / operator123 (مشغل نظام المراقبة)")
    print("   security_manager / security123 (مدير الأمن المعلوماتي)")

    print("\n🌐 فتح لوحة التحكم المحسنة...")
    try:
        webbrowser.open('http://localhost:8000/enhanced-dashboard.html')
        print("✅ تم فتح لوحة التحكم المحسنة")
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")

    print("\n🎯 الصفحات المتاحة:")
    print("   • http://localhost:8000/enhanced-dashboard.html - لوحة التحكم المحسنة")
    print("   • http://localhost:8000/activity-logs.html - سجل الأنشطة")
    print("   • http://localhost:8000/login-flow-manager.html - إدارة تدفق الدخول")
    print("   • http://localhost:8000/index.html - الصفحة الرئيسية")
    print("   • http://localhost:8000/instant-login.html - تسجيل دخول فوري")

    print("\n🔧 الميزات الجديدة:")
    print("   • مراقبة عدم النشاط التلقائية")
    print("   • حماية من محاولات تسجيل الدخول المتكررة")
    print("   • إحصائيات شاملة في الوقت الفعلي")
    print("   • تصدير واستيراد البيانات")
    print("   • سجل أنشطة مفصل مع فلترة")
    print("   • واجهات محسنة ومتجاوبة")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام بشكل مبسط
Simple System Runner
"""

import subprocess
import time
import sys
import os
import webbrowser

def main():
    print("تشغيل نظام إدارة أمن المعلومات...")
    print("Starting ISMS...")
    print("=" * 50)
    
    # تشغيل الخادم الرئيسي
    print("تشغيل الخادم الرئيسي...")
    try:
        main_process = subprocess.Popen([sys.executable, 'start-server.py'])
        print("تم تشغيل الخادم الرئيسي على المنفذ 8000")
    except Exception as e:
        print(f"خطأ في تشغيل الخادم الرئيسي: {e}")
        return
    
    # انتظار قصير
    time.sleep(3)
    
    # تشغيل خادم المزامنة المبسط
    print("تشغيل خادم المزامنة...")
    try:
        sync_process = subprocess.Popen([sys.executable, 'sync-server-simple.py'])
        print("تم تشغيل خادم المزامنة على المنفذ 8001")
    except Exception as e:
        print(f"خطأ في تشغيل خادم المزامنة: {e}")
        print("النظام سيعمل بدون مزامنة")
    
    # انتظار تشغيل الخوادم
    print("انتظار تشغيل الخوادم...")
    time.sleep(5)
    
    # فتح المتصفح
    print("فتح المتصفح...")
    try:
        webbrowser.open("http://localhost:8000/login.html")
        print("تم فتح المتصفح")
    except Exception as e:
        print(f"فشل في فتح المتصفح: {e}")
        print("افتح المتصفح يدوياً على: http://localhost:8000/login.html")
    
    print("\n" + "=" * 50)
    print("تم تشغيل النظام بنجاح!")
    print("System started successfully!")
    print("=" * 50)
    print("روابط الوصول:")
    print("  محلي: http://localhost:8000/login.html")
    print("  شبكة: http://*************:8000/login.html")
    print("\nبيانات الدخول:")
    print("  اسم المستخدم: admin")
    print("  كلمة المرور: admin123")
    print("\nللإيقاف: أغلق نوافذ Terminal")
    print("=" * 50)
    
    # انتظار الإيقاف
    try:
        input("\nاضغط Enter للإيقاف...")
    except KeyboardInterrupt:
        pass
    
    # إيقاف العمليات
    print("إيقاف النظام...")
    try:
        main_process.terminate()
        sync_process.terminate()
        print("تم إيقاف النظام")
    except:
        pass

if __name__ == "__main__":
    main()

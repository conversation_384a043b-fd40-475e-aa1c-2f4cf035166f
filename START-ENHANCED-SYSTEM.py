#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل محسن لنظام إدارة أمن المعلومات
Enhanced Startup Script for ISMS
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    print(f"✅ Python {sys.version}")
    return True

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    try:
        import http.server
        import socketserver
        import json
        print("✅ جميع التبعيات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ تبعية مفقودة: {e}")
        return False

def start_web_server(port=8000):
    """بدء خادم الويب"""
    try:
        print(f"🌐 بدء خادم الويب على المنفذ {port}...")
        
        # تغيير إلى مجلد المشروع
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # بدء الخادم
        cmd = [sys.executable, '-m', 'http.server', str(port)]
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # انتظار قصير للتأكد من بدء الخادم
        time.sleep(2)
        
        # فحص إذا كان الخادم يعمل
        if process.poll() is None:
            print(f"✅ خادم الويب يعمل على http://localhost:{port}")
            return process
        else:
            print("❌ فشل في بدء خادم الويب")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بدء خادم الويب: {e}")
        return None

def start_sync_server(port=8001):
    """بدء خادم المزامنة"""
    try:
        if os.path.exists('sync-server-simple.py'):
            print(f"🔄 بدء خادم المزامنة على المنفذ {port}...")
            cmd = [sys.executable, 'sync-server-simple.py']
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(1)
            
            if process.poll() is None:
                print(f"✅ خادم المزامنة يعمل على http://localhost:{port}")
                return process
            else:
                print("⚠️  خادم المزامنة غير متاح")
                return None
        else:
            print("⚠️  ملف خادم المزامنة غير موجود")
            return None
            
    except Exception as e:
        print(f"⚠️  خطأ في بدء خادم المزامنة: {e}")
        return None

def open_browser(url="http://localhost:8000"):
    """فتح المتصفح"""
    try:
        print(f"🌐 فتح المتصفح: {url}")
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("=" * 60)
    print("🛡️  نظام إدارة أمن المعلومات - ISMS")
    print("=" * 60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 المجلد: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 60)

def show_urls():
    """عرض الروابط المتاحة"""
    print("🌐 الروابط المتاحة:")
    print("   • الصفحة الرئيسية: http://localhost:8000/index.html")
    print("   • تسجيل الدخول المحسن: http://localhost:8000/login-fixed.html")
    print("   • اختبار تسجيل الدخول: http://localhost:8000/login-test.html")
    print("   • لوحة التحكم المحسنة: http://localhost:8000/enhanced-dashboard.html")
    print("   • إدارة تدفق الدخول: http://localhost:8000/login-flow-manager.html")
    print("   • سجل الأنشطة: http://localhost:8000/activity-logs.html")
    print("   • إعدادات النظام: http://localhost:8000/system-settings.html")

def show_login_info():
    """عرض بيانات تسجيل الدخول"""
    print("🔐 بيانات تسجيل الدخول:")
    print("   • admin / admin123 (مدير النظام)")
    print("   • analyst / analyst123 (محلل أمني)")
    print("   • operator / operator123 (مشغل)")
    print("   • emergency / emergency123 (مستخدم طوارئ)")

def main():
    """الوظيفة الرئيسية"""
    show_system_info()
    
    # فحص المتطلبات
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    if not check_dependencies():
        input("اضغط Enter للخروج...")
        return
    
    # بدء الخوادم
    web_server = start_web_server()
    if not web_server:
        input("اضغط Enter للخروج...")
        return
    
    sync_server = start_sync_server()
    
    # عرض المعلومات
    print()
    show_urls()
    print()
    show_login_info()
    print()
    
    # فتح المتصفح
    print("🚀 بدء النظام...")
    time.sleep(1)
    
    # فتح صفحة الاختبار أولاً
    open_browser("http://localhost:8000/login-test.html")
    time.sleep(2)
    
    # فتح صفحة تسجيل الدخول المحسنة
    open_browser("http://localhost:8000/login-fixed.html")
    
    print()
    print("✅ تم بدء النظام بنجاح!")
    print("💡 نصائح:")
    print("   • استخدم صفحة الاختبار لتشخيص أي مشاكل")
    print("   • استخدم صفحة تسجيل الدخول المحسنة للدخول")
    print("   • اضغط Ctrl+C لإيقاف النظام")
    print()
    
    try:
        # انتظار إيقاف النظام
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        
        # إيقاف الخوادم
        if web_server:
            web_server.terminate()
            print("✅ تم إيقاف خادم الويب")
        
        if sync_server:
            sync_server.terminate()
            print("✅ تم إيقاف خادم المزامنة")
        
        print("👋 تم إيقاف النظام بنجاح")

if __name__ == "__main__":
    main()

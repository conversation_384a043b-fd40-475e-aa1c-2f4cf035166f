<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المحسنة - نظام إدارة أمن المعلومات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2em;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #3498db;
            margin: 10px 0;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .action-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: inherit;
        }
        .action-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9em;
        }
        .activity-login { background: #27ae60; }
        .activity-logout { background: #e74c3c; }
        .activity-user { background: #f39c12; }
        .activity-event { background: #9b59b6; }
        .activity-system { background: #34495e; }
        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .status-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .status-online { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 10px;
            color: #6c757d;
        }
    </style>
    <script src="fixed-auth-system.js"></script>
    <script src="network-sync-client.js"></script>
    <script src="sync-integration.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>🛡️ لوحة التحكم المحسنة</h1>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <div>
                    <div id="userName">مدير النظام</div>
                    <div style="font-size: 0.9em; color: #7f8c8d;" id="userRole">مدير النظام</div>
                </div>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="grid">
            <div class="card stat-card">
                <h3><i class="fas fa-users"></i> إجمالي المستخدمين</h3>
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">مستخدم مسجل</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-user-check"></i> المستخدمين النشطين</h3>
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">مستخدم نشط</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-shield-alt"></i> الأحداث الأمنية</h3>
                <div class="stat-number" id="securityEvents">0</div>
                <div class="stat-label">حدث أمني</div>
            </div>

            <div class="card stat-card">
                <h3><i class="fas fa-clock"></i> الجلسات النشطة</h3>
                <div class="stat-number" id="activeSessions">0</div>
                <div class="stat-label">جلسة نشطة</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
            <div class="quick-actions">
                <a href="index.html" class="action-btn">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
                <a href="login-flow-manager.html" class="action-btn">
                    <i class="fas fa-cogs"></i>
                    إدارة تدفق الدخول
                </a>
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    تحديث البيانات
                </button>
                <button class="action-btn" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
                <button class="action-btn" onclick="viewLogs()">
                    <i class="fas fa-list"></i>
                    عرض السجلات
                </button>
                <button class="action-btn" onclick="systemSettings()">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </button>
            </div>
        </div>

        <div class="grid">
            <!-- Recent Activity -->
            <div class="card">
                <h3><i class="fas fa-history"></i> النشاط الأخير</h3>
                <div class="recent-activity" id="recentActivity">
                    <div style="text-align: center; color: #6c757d; padding: 20px;">
                        جاري تحميل الأنشطة...
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card">
                <h3><i class="fas fa-server"></i> حالة النظام</h3>
                <div class="system-status">
                    <div class="status-item status-online">
                        <div><i class="fas fa-database"></i></div>
                        <div>قاعدة البيانات</div>
                        <div>متصلة</div>
                    </div>
                    <div class="status-item status-online">
                        <div><i class="fas fa-sync"></i></div>
                        <div>المزامنة</div>
                        <div>نشطة</div>
                    </div>
                    <div class="status-item status-online">
                        <div><i class="fas fa-shield-alt"></i></div>
                        <div>الأمان</div>
                        <div>محمي</div>
                    </div>
                    <div class="status-item status-warning">
                        <div><i class="fas fa-exclamation-triangle"></i></div>
                        <div>التنبيهات</div>
                        <div id="alertsCount">0</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid">
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> إحصائيات الاستخدام</h3>
                <div class="chart-container">
                    <div>📊 الرسوم البيانية قيد التطوير</div>
                </div>
            </div>

            <div class="card">
                <h3><i class="fas fa-chart-pie"></i> توزيع الأدوار</h3>
                <div class="chart-container">
                    <div>🥧 رسم بياني دائري قيد التطوير</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحميل البيانات عند تحميل الصفحة
        window.onload = function() {
            loadDashboardData();
            loadRecentActivity();
            updateSystemStatus();
        };

        // تحميل بيانات لوحة التحكم
        function loadDashboardData() {
            try {
                // تحميل المستخدم الحالي
                const sessionId = localStorage.getItem('currentSession');
                if (sessionId) {
                    const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                    const session = sessions[sessionId];
                    if (session) {
                        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                        const currentUser = users[session.username];
                        if (currentUser) {
                            document.getElementById('userName').textContent = currentUser.fullName;
                            document.getElementById('userRole').textContent = getRoleLabel(currentUser.role);
                            document.getElementById('userAvatar').textContent = currentUser.fullName.charAt(0).toUpperCase();
                        }
                    }
                }

                // إحصائيات المستخدمين
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const usersList = Object.values(users);
                document.getElementById('totalUsers').textContent = usersList.length;
                document.getElementById('activeUsers').textContent = usersList.filter(u => u.isActive).length;

                // إحصائيات الأحداث الأمنية
                const events = JSON.parse(localStorage.getItem('securityEvents')) || [];
                document.getElementById('securityEvents').textContent = events.length;

                // الجلسات النشطة
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                const activeSessions = Object.values(sessions).filter(s => new Date() < new Date(s.expiresAt));
                document.getElementById('activeSessions').textContent = activeSessions.length;

            } catch (error) {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
            }
        }

        // تحميل النشاط الأخير
        function loadRecentActivity() {
            try {
                const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
                const recentActivities = activities.slice(0, 10);

                const activityContainer = document.getElementById('recentActivity');

                if (recentActivities.length === 0) {
                    activityContainer.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أنشطة حديثة</div>';
                    return;
                }

                activityContainer.innerHTML = recentActivities.map(activity => {
                    const icon = getActivityIcon(activity.type);
                    const time = new Date(activity.timestamp).toLocaleString('ar-SA');

                    return `
                        <div class="activity-item">
                            <div class="activity-icon activity-${activity.type}">
                                <i class="${icon}"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500;">${activity.description}</div>
                                <div style="font-size: 0.9em; color: #6c757d;">${activity.username} - ${time}</div>
                            </div>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('خطأ في تحميل النشاط الأخير:', error);
            }
        }

        // تحديث حالة النظام
        function updateSystemStatus() {
            try {
                // فحص التنبيهات
                const events = JSON.parse(localStorage.getItem('securityEvents')) || [];
                const openEvents = events.filter(e => e.status === 'open');
                document.getElementById('alertsCount').textContent = openEvents.length;

            } catch (error) {
                console.error('خطأ في تحديث حالة النظام:', error);
            }
        }

        // الحصول على أيقونة النشاط
        function getActivityIcon(type) {
            const icons = {
                'login': 'fas fa-sign-in-alt',
                'logout': 'fas fa-sign-out-alt',
                'user_created': 'fas fa-user-plus',
                'user_updated': 'fas fa-user-edit',
                'user_deleted': 'fas fa-user-minus',
                'event_created': 'fas fa-plus-circle',
                'event_updated': 'fas fa-edit',
                'event_deleted': 'fas fa-trash',
                'system': 'fas fa-cog'
            };
            return icons[type] || 'fas fa-info-circle';
        }

        // الحصول على تسمية الدور
        function getRoleLabel(role) {
            const labels = {
                'admin': 'مدير النظام',
                'analyst': 'محلل أمني',
                'operator': 'مشغل'
            };
            return labels[role] || role;
        }

        // تحديث البيانات
        function refreshData() {
            loadDashboardData();
            loadRecentActivity();
            updateSystemStatus();

            // إظهار رسالة نجاح
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> تم التحديث';
            btn.style.background = '#27ae60';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = '#3498db';
            }, 2000);
        }

        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    users: JSON.parse(localStorage.getItem('systemUsers')) || {},
                    events: JSON.parse(localStorage.getItem('securityEvents')) || [],
                    activities: JSON.parse(localStorage.getItem('activityLog')) || [],
                    sessions: JSON.parse(localStorage.getItem('userSessions')) || {},
                    exportDate: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `isms-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                alert('تم تصدير البيانات بنجاح');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                alert('حدث خطأ في تصدير البيانات');
            }
        }

        // عرض السجلات
        function viewLogs() {
            window.open('activity-logs.html', '_blank');
        }

        // إعدادات النظام
        function systemSettings() {
            window.open('system-settings.html', '_blank');
        }
    </script>
</body>
</html>
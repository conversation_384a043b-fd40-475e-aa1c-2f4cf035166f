#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم HTTP بسيط لتشغيل نظام إدارة أمن المعلومات
Simple HTTP Server for Information Security Management System
"""

import http.server
import socketserver
import webbrowser
import socket
import sys
import os
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # إضافة headers للأمان
        self.send_header('X-Frame-Options', 'SAMEORIGIN')
        self.send_header('X-XSS-Protection', '1; mode=block')
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        super().end_headers()
    
    def do_GET(self):
        # إعادة توجيه الصفحة الرئيسية إلى login.html فقط إذا لم يكن هناك session
        if self.path == '/':
            self.send_response(301)
            self.send_header('Location', '/login.html')
            self.end_headers()
            return

        super().do_GET()

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def find_free_port(start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def get_all_network_interfaces():
    """الحصول على جميع عناوين IP المتاحة"""
    import platform
    import subprocess

    interfaces = {}

    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='utf-8')
            lines = result.stdout.split('\n')
            current_adapter = None

            for line in lines:
                line = line.strip()
                if 'adapter' in line.lower() or 'محول' in line:
                    current_adapter = line
                elif 'IPv4' in line or 'عنوان IPv4' in line:
                    if current_adapter and ':' in line:
                        ip = line.split(':')[-1].strip()
                        if ip and ip != '127.0.0.1' and not ip.startswith('169.254'):
                            interfaces[current_adapter] = ip
        else:
            result = subprocess.run(['ifconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            current_interface = None

            for line in lines:
                if line and not line.startswith(' ') and not line.startswith('\t'):
                    current_interface = line.split(':')[0]
                elif 'inet ' in line and current_interface:
                    parts = line.strip().split()
                    for i, part in enumerate(parts):
                        if part == 'inet' and i + 1 < len(parts):
                            ip = parts[i + 1]
                            if ip != '127.0.0.1' and not ip.startswith('169.254'):
                                interfaces[current_interface] = ip
                            break
    except:
        pass

    return interfaces

def create_access_links_file(port, local_ip, interfaces):
    """إنشاء ملف روابط الوصول"""
    content = f"""# 🌐 روابط الوصول لنظام إدارة أمن المعلومات
# Information Security Management System - Access Links

## 🏠 الوصول المحلي (Local Access)
http://localhost:{port}/login.html
http://127.0.0.1:{port}/login.html

## 🌍 الوصول من الشبكة (Network Access)
http://{local_ip}:{port}/login.html

## 📱 روابط إضافية (Additional Network Interfaces)
"""

    for name, ip in interfaces.items():
        content += f"# {name}\nhttp://{ip}:{port}/login.html\n\n"

    content += f"""
## 🔑 بيانات تسجيل الدخول (Login Credentials)
اسم المستخدم: admin
كلمة المرور: admin123

## 📋 معلومات الخادم (Server Information)
المنفذ: {port}
عنوان IP الرئيسي: {local_ip}
حالة الخادم: يعمل

## 🔒 ملاحظات الأمان (Security Notes)
- تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ {port}
- استخدم شبكة آمنة وموثوقة فقط
- غيّر كلمة المرور الافتراضية بعد أول تسجيل دخول
- راقب سجلات الوصول بانتظام

## 🛠️ استكشاف الأخطاء (Troubleshooting)
إذا لم تتمكن من الوصول من جهاز آخر:
1. تحقق من تشغيل الخادم على هذا الجهاز
2. تحقق من إعدادات جدار الحماية (Windows Firewall)
3. تحقق من اتصال الشبكة بين الأجهزة
4. جرب عنوان IP مختلف من القائمة أعلاه
5. تأكد من أن الأجهزة على نفس الشبكة

## 🌐 للوصول من أنظمة تشغيل مختلفة:
- Windows: افتح أي متصفح واكتب الرابط
- macOS: افتح Safari أو Chrome واكتب الرابط
- Linux: افتح Firefox أو أي متصفح واكتب الرابط
- Android/iOS: افتح متصفح الهاتف واكتب الرابط
"""

    try:
        with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    # البحث عن منفذ متاح
    port = find_free_port(8000)
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        sys.exit(1)

    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()

    # الحصول على جميع واجهات الشبكة
    interfaces = get_all_network_interfaces()

    # إنشاء ملف روابط الوصول
    create_access_links_file(port, local_ip, interfaces)

    # إنشاء الخادم
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print("🔐 نظام إدارة أمن المعلومات")
            print("=" * 60)
            print(f"🌐 الخادم يعمل على المنفذ: {port}")
            print()
            print("🏠 الوصول المحلي:")
            print(f"   📍 http://localhost:{port}/login.html")
            print(f"   📍 http://127.0.0.1:{port}/login.html")
            print()
            print("🌍 الوصول من الشبكة:")
            print(f"   📍 http://{local_ip}:{port}/login.html")

            if interfaces:
                print()
                print("📱 روابط إضافية (واجهات شبكة أخرى):")
                for name, ip in interfaces.items():
                    print(f"   📍 http://{ip}:{port}/login.html")

            print()
            print("=" * 60)
            print("🔑 بيانات الدخول الافتراضية:")
            print("   👤 اسم المستخدم: admin")
            print("   🔒 كلمة المرور: admin123")
            print("=" * 60)
            print("📄 تم إنشاء ملف ACCESS-LINKS.txt مع جميع الروابط")
            print("⚠️  للإيقاف: اضغط Ctrl+C")
            print()

            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(f'http://localhost:{port}/login.html')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️  يرجى فتح المتصفح يدوياً")

            print()
            print("🚀 الخادم جاهز...")

            # تشغيل الخادم
            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد الشبكة التلقائي لنظام إدارة أمن المعلومات
Automatic Network Setup for ISMS
"""

import socket
import subprocess
import platform
import json
import os
from datetime import datetime

class NetworkSetup:
    def __init__(self):
        self.local_ip = None
        self.network_interfaces = []
        self.firewall_rules = []
        
    def print_header(self):
        """طباعة رأس الإعداد"""
        print("=" * 60)
        print("🌐 إعداد الشبكة التلقائي لنظام إدارة أمن المعلومات")
        print("   Automatic Network Setup for ISMS")
        print("=" * 60)
        print()
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            # طريقة موثوقة للحصول على IP المحلي
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            self.local_ip = s.getsockname()[0]
            s.close()
            return self.local_ip
        except Exception as e:
            print(f"⚠️  خطأ في الحصول على IP: {e}")
            return "127.0.0.1"
    
    def get_all_network_interfaces(self):
        """الحصول على جميع واجهات الشبكة"""
        interfaces = []
        
        try:
            if platform.system() == "Windows":
                # Windows
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, shell=True)
                output = result.stdout
                
                current_interface = None
                for line in output.split('\n'):
                    line = line.strip()
                    if 'adapter' in line.lower() or 'ethernet' in line.lower():
                        current_interface = line
                    elif 'IPv4 Address' in line and current_interface:
                        ip = line.split(':')[-1].strip()
                        if ip and not ip.startswith('127.'):
                            interfaces.append({
                                'name': current_interface,
                                'ip': ip,
                                'type': 'ethernet' if 'ethernet' in current_interface.lower() else 'wifi'
                            })
            else:
                # Linux/Mac
                result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True)
                if result.returncode != 0:
                    # Fallback to ifconfig
                    result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                
                output = result.stdout
                current_interface = None
                
                for line in output.split('\n'):
                    line = line.strip()
                    if line and not line.startswith(' '):
                        parts = line.split(':')
                        if len(parts) >= 2:
                            current_interface = parts[0]
                    elif 'inet ' in line and current_interface:
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if part == 'inet' and i + 1 < len(parts):
                                ip = parts[i + 1].split('/')[0]
                                if ip and not ip.startswith('127.'):
                                    interfaces.append({
                                        'name': current_interface,
                                        'ip': ip,
                                        'type': 'ethernet' if 'eth' in current_interface else 'wifi'
                                    })
                                break
        except Exception as e:
            print(f"⚠️  خطأ في الحصول على واجهات الشبكة: {e}")
        
        self.network_interfaces = interfaces
        return interfaces
    
    def check_port_availability(self, port):
        """فحص توفر المنفذ"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                return result != 0  # True if port is available
        except:
            return False
    
    def check_firewall_status(self):
        """فحص حالة جدار الحماية"""
        system = platform.system()
        
        try:
            if system == "Windows":
                result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'], 
                                      capture_output=True, text=True, shell=True)
                return "ON" in result.stdout.upper()
            elif system == "Linux":
                # Check ufw
                result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
                if result.returncode == 0:
                    return "active" in result.stdout.lower()
                
                # Check iptables
                result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
                return result.returncode == 0
            else:
                return False
        except:
            return False
    
    def suggest_firewall_rules(self):
        """اقتراح قواعد جدار الحماية"""
        system = platform.system()
        rules = []
        
        if system == "Windows":
            rules = [
                'netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport=8000',
                'netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport=8001'
            ]
        elif system == "Linux":
            rules = [
                'sudo ufw allow 8000/tcp comment "ISMS Main Server"',
                'sudo ufw allow 8001/tcp comment "ISMS Sync Server"'
            ]
        
        self.firewall_rules = rules
        return rules
    
    def generate_access_links(self):
        """إنشاء روابط الوصول"""
        if not self.local_ip:
            self.get_local_ip()
        
        links = {
            'local': {
                'main': 'http://localhost:8000',
                'login': 'http://localhost:8000/login.html',
                'sync': 'http://localhost:8001/api/sync/status'
            },
            'network': {
                'main': f'http://{self.local_ip}:8000',
                'login': f'http://{self.local_ip}:8000/login.html',
                'sync': f'http://{self.local_ip}:8001/api/sync/status'
            }
        }
        
        return links
    
    def save_network_config(self):
        """حفظ إعدادات الشبكة"""
        config = {
            'timestamp': datetime.now().isoformat(),
            'local_ip': self.local_ip,
            'network_interfaces': self.network_interfaces,
            'firewall_rules': self.firewall_rules,
            'access_links': self.generate_access_links()
        }
        
        try:
            with open('network-config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"⚠️  خطأ في حفظ الإعدادات: {e}")
            return False
    
    def display_network_info(self):
        """عرض معلومات الشبكة"""
        print("🔍 فحص إعدادات الشبكة...")
        print()
        
        # عنوان IP الرئيسي
        ip = self.get_local_ip()
        print(f"🌐 عنوان IP المحلي: {ip}")
        
        # واجهات الشبكة
        interfaces = self.get_all_network_interfaces()
        if interfaces:
            print("\n📡 واجهات الشبكة المتاحة:")
            for interface in interfaces:
                print(f"   • {interface['name']}: {interface['ip']} ({interface['type']})")
        
        # فحص المنافذ
        print("\n🔌 فحص المنافذ:")
        port_8000 = self.check_port_availability(8000)
        port_8001 = self.check_port_availability(8001)
        
        print(f"   • المنفذ 8000 (الخادم الرئيسي): {'✅ متاح' if port_8000 else '❌ مستخدم'}")
        print(f"   • المنفذ 8001 (خادم المزامنة): {'✅ متاح' if port_8001 else '❌ مستخدم'}")
        
        # جدار الحماية
        firewall_active = self.check_firewall_status()
        print(f"\n🛡️  جدار الحماية: {'🔴 مفعل' if firewall_active else '🟢 غير مفعل'}")
        
        if firewall_active:
            print("\n⚠️  جدار الحماية مفعل. قد تحتاج لإضافة قواعد للمنافذ:")
            rules = self.suggest_firewall_rules()
            for rule in rules:
                print(f"   {rule}")
        
        # روابط الوصول
        links = self.generate_access_links()
        print("\n🔗 روابط الوصول:")
        print("   📍 الوصول المحلي:")
        print(f"      • الصفحة الرئيسية: {links['local']['main']}")
        print(f"      • تسجيل الدخول: {links['local']['login']}")
        print(f"      • خادم المزامنة: {links['local']['sync']}")
        
        print("   🌍 الوصول من الشبكة:")
        print(f"      • الصفحة الرئيسية: {links['network']['main']}")
        print(f"      • تسجيل الدخول: {links['network']['login']}")
        print(f"      • خادم المزامنة: {links['network']['sync']}")
        
        return {
            'ip': ip,
            'interfaces': interfaces,
            'ports_available': {'8000': port_8000, '8001': port_8001},
            'firewall_active': firewall_active,
            'links': links
        }
    
    def create_access_file(self):
        """إنشاء ملف روابط الوصول"""
        links = self.generate_access_links()
        
        content = f"""# 🌐 روابط الوصول لنظام إدارة أمن المعلومات
# ISMS Access Links
# تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📍 الوصول المحلي (Local Access)
الصفحة الرئيسية: {links['local']['main']}
تسجيل الدخول: {links['local']['login']}
خادم المزامنة: {links['local']['sync']}

## 🌍 الوصول من الشبكة (Network Access)
الصفحة الرئيسية: {links['network']['main']}
تسجيل الدخول: {links['network']['login']}
خادم المزامنة: {links['network']['sync']}

## 🔑 بيانات الدخول الافتراضية
اسم المستخدم: admin
كلمة المرور: admin123

## 📋 مستخدمون إضافيون
• analyst / analyst123 (محلل أمني)
• operator / operator123 (مشغل)

## 🛠️ استكشاف الأخطاء
إذا لم تتمكن من الوصول:
1. تحقق من تشغيل الخادم
2. تحقق من إعدادات جدار الحماية
3. تحقق من اتصال الشبكة
4. جرب عنوان IP مختلف من القائمة أعلاه

## 🔧 إعدادات جدار الحماية (إذا لزم الأمر)
Windows:
netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport=8001

Linux:
sudo ufw allow 8000/tcp comment "ISMS Main Server"
sudo ufw allow 8001/tcp comment "ISMS Sync Server"
"""
        
        try:
            with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ تم إنشاء ملف ACCESS-LINKS.txt")
            return True
        except Exception as e:
            print(f"⚠️  خطأ في إنشاء ملف الروابط: {e}")
            return False
    
    def run_setup(self):
        """تشغيل الإعداد الكامل"""
        self.print_header()
        
        # عرض معلومات الشبكة
        network_info = self.display_network_info()
        
        # حفظ الإعدادات
        print("\n💾 حفظ إعدادات الشبكة...")
        if self.save_network_config():
            print("✅ تم حفظ network-config.json")
        
        # إنشاء ملف الروابط
        print("\n📄 إنشاء ملف روابط الوصول...")
        self.create_access_file()
        
        print("\n" + "=" * 60)
        print("🎉 تم إعداد الشبكة بنجاح!")
        print("💡 يمكنك الآن مشاركة الروابط مع المستخدمين الآخرين في الشبكة")
        print("📄 راجع ملف ACCESS-LINKS.txt للحصول على جميع الروابط")
        print("=" * 60)
        
        return network_info

def main():
    """الدالة الرئيسية"""
    setup = NetworkSetup()
    return setup.run_setup()

if __name__ == "__main__":
    main()

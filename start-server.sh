#!/bin/bash
# -*- coding: utf-8 -*-
# تشغيل نظام إدارة أمن المعلومات على macOS/Linux
# Start Information Security Management System on macOS/Linux

echo "🔐 نظام إدارة أمن المعلومات"
echo "Information Security Management System"
echo "======================================"
echo

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت"
    echo "❌ Python 3 is not installed"
    echo
    echo "📥 لتثبيت Python على macOS:"
    echo "   brew install python3"
    echo
    echo "📥 لتثبيت Python على Ubuntu/Debian:"
    echo "   sudo apt update && sudo apt install python3"
    echo
    echo "📥 لتثبيت Python على CentOS/RHEL:"
    echo "   sudo yum install python3"
    echo
    exit 1
fi

echo "✅ Python 3 متوفر"
echo "✅ Python 3 is available"
echo

# التحقق من الملفات المطلوبة
required_files=("start-server.py" "index.html" "login.html" "styles.css" "script.js")
missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ ملفات مفقودة:"
    echo "❌ Missing files:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo
    exit 1
fi

echo "✅ جميع الملفات المطلوبة موجودة"
echo "✅ All required files are present"
echo

# الحصول على عنوان IP
if command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep -E "inet.*broadcast" | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
elif command -v ip &> /dev/null; then
    LOCAL_IP=$(ip route get ******* | grep -oP 'src \K\S+')
else
    LOCAL_IP="127.0.0.1"
fi

echo "🌐 عنوان IP المحلي: $LOCAL_IP"
echo "🌐 Local IP Address: $LOCAL_IP"
echo

# إنشاء ملف إعدادات الشبكة
cat > network-info.txt << EOF
# معلومات الشبكة - Network Information
# نظام إدارة أمن المعلومات - ISMS

## الوصول المحلي - Local Access
http://localhost:8000/login.html
http://127.0.0.1:8000/login.html

## الوصول من الشبكة - Network Access
http://$LOCAL_IP:8000/login.html

## بيانات الدخول - Login Credentials
اسم المستخدم / Username: admin
كلمة المرور / Password: admin123

## ملاحظات - Notes
- تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ 8000
- Make sure firewall allows connections on port 8000
- للوصول من أجهزة أخرى، استخدم عنوان IP الشبكة
- To access from other devices, use the network IP address

## استكشاف الأخطاء - Troubleshooting
إذا لم تتمكن من الوصول من جهاز آخر:
If you cannot access from another device:

1. تحقق من تشغيل الخادم - Check if server is running
2. تحقق من جدار الحماية - Check firewall settings
3. تحقق من اتصال الشبكة - Check network connection
4. تأكد من أن الأجهزة على نفس الشبكة - Ensure devices are on same network

## إيقاف الخادم - Stop Server
اضغط Ctrl+C في Terminal - Press Ctrl+C in Terminal
EOF

echo "📄 تم إنشاء ملف network-info.txt"
echo "📄 Created network-info.txt file"
echo

# تشغيل الخادم
echo "🚀 بدء تشغيل الخادم..."
echo "🚀 Starting server..."
echo
echo "⚠️  للإيقاف اضغط Ctrl+C"
echo "⚠️  Press Ctrl+C to stop"
echo

# محاولة فتح المتصفح تلقائياً
if command -v open &> /dev/null; then
    # macOS
    sleep 2 && open "http://localhost:8000/login.html" &
elif command -v xdg-open &> /dev/null; then
    # Linux
    sleep 2 && xdg-open "http://localhost:8000/login.html" &
fi

# تشغيل الخادم
python3 start-server.py

echo
echo "👋 شكراً لاستخدام نظام إدارة أمن المعلومات"
echo "👋 Thank you for using ISMS"

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول فوري</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255,255,255,0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
        }
        .form-group {
            margin: 20px 0;
            text-align: right;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }
        input:focus {
            border-color: #3498db;
            outline: none;
        }
        .login-btn {
            width: 100%;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            margin-top: 20px;
        }
        .login-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }
        .quick-login {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .quick-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
            font-family: inherit;
        }
        .quick-btn:hover {
            background: #229954;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <script src="fixed-auth-system.js"></script>
</head>
<body>
    <div class="login-container">
        <h1>⚡ تسجيل دخول فوري</h1>
        
        <div class="quick-login">
            <h3>تسجيل دخول سريع:</h3>
            <button class="quick-btn" onclick="quickLogin('admin')">مدير النظام</button>
            <button class="quick-btn" onclick="quickLogin('analyst')">محلل أمني</button>
            <button class="quick-btn" onclick="quickLogin('operator')">مشغل</button>
        </div>
        
        <form id="loginForm" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                دخول فوري
            </button>
        </form>
        
        <div id="status" class="status"></div>
        
        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p>بيانات الدخول:</p>
            <p>admin / admin123</p>
            <p>analyst / analyst123</p>
            <p>operator / operator123</p>
        </div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = {"admin": {"id": "admin", "username": "admin", "password": "-969161597", "fullName": "مدير النظام", "email": "<EMAIL>", "role": "admin", "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"], "isActive": true, "createdAt": "2025-07-14T13:01:25.619546", "lastLogin": null}, "analyst": {"id": "analyst", "username": "analyst", "password": "-944571792", "fullName": "محلل أمني", "email": "<EMAIL>", "role": "analyst", "permissions": ["read", "write", "view_analytics"], "isActive": true, "createdAt": "2025-07-14T13:01:25.619572", "lastLogin": null}, "operator": {"id": "operator", "username": "operator", "password": "135275278", "fullName": "مشغل النظام", "email": "<EMAIL>", "role": "operator", "permissions": ["read"], "isActive": true, "createdAt": "2025-07-14T13:01:25.619578", "lastLogin": null}};
        
        // حفظ البيانات في localStorage عند تحميل الصفحة
        window.onload = function() {
            try {
                localStorage.setItem('systemUsers', JSON.stringify(users));
                console.log('تم حفظ بيانات المستخدمين');
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
            }
        };
        
        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }
        
        // تسجيل دخول سريع
        function quickLogin(username) {
            const passwords = {
                'admin': 'admin123',
                'analyst': 'analyst123',
                'operator': 'operator123'
            };
            
            document.getElementById('username').value = username;
            document.getElementById('password').value = passwords[username];
            
            // تسجيل دخول فوري
            performLogin(username, passwords[username]);
        }
        
        // معالج النموذج
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showStatus('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            performLogin(username, password);
        }
        
        // تنفيذ تسجيل الدخول
        function performLogin(username, password) {
            showStatus('جاري تسجيل الدخول...', 'loading');
            
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const user = users[username];
                
                if (!user) {
                    showStatus('اسم المستخدم غير موجود', 'error');
                    return;
                }
                
                if (!user.isActive) {
                    showStatus('هذا الحساب معطل', 'error');
                    return;
                }
                
                const hashedPassword = hashPassword(password);
                
                if (user.password === hashedPassword) {
                    // تسجيل دخول ناجح
                    showStatus('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                    
                    // إنشاء جلسة
                    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    const session = {
                        userId: user.id,
                        username: user.username,
                        role: user.role,
                        permissions: user.permissions,
                        createdAt: new Date().toISOString(),
                        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
                    };
                    
                    // حفظ الجلسة
                    const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                    userSessions[sessionId] = session;
                    localStorage.setItem('userSessions', JSON.stringify(userSessions));
                    localStorage.setItem('currentSession', sessionId);
                    
                    // تحديث آخر دخول
                    user.lastLogin = new Date().toISOString();
                    users[username] = user;
                    localStorage.setItem('systemUsers', JSON.stringify(users));
                    
                    // تسجيل النشاط
                    const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
                    activities.push({
                        id: 'activity_' + Date.now(),
                        type: 'login',
                        description: `تسجيل دخول المستخدم ${user.fullName}`,
                        username: user.username,
                        timestamp: new Date().toISOString(),
                        details: { loginMethod: 'instant' }
                    });
                    localStorage.setItem('activityLog', JSON.stringify(activities));
                    
                    // الانتقال فوراً بدون تأخير
                    window.location.href = 'index.html';
                    
                } else {
                    showStatus('كلمة المرور غير صحيحة', 'error');
                }
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showStatus('حدث خطأ أثناء تسجيل الدخول', 'error');
            }
        }
        
        // عرض حالة
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            if (type === 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        // تفعيل Enter للتسجيل
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const form = document.getElementById('loginForm');
                if (form && (document.activeElement.id === 'username' || document.activeElement.id === 'password')) {
                    e.preventDefault();
                    handleLogin(e);
                }
            }
        });
    </script>
</body>
</html>
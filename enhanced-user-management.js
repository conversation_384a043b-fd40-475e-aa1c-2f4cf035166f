
// Enhanced User Management System
// نظام إدارة المستخدمين المحسن

class EnhancedUserManager {
    constructor() {
        this.users = {};
        this.currentUser = null;
        this.userSessions = {};
        this.activityLog = [];
        this.init();
    }

    init() {
        this.loadUsers();
        this.loadSessions();
        this.loadActivityLog();
        this.setupEventListeners();
        this.checkAuthentication();
    }

    // تحميل المستخدمين
    loadUsers() {
        try {
            const storedUsers = localStorage.getItem('systemUsers');
            if (storedUsers) {
                this.users = JSON.parse(storedUsers);
            } else {
                this.createDefaultUsers();
            }
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
            this.createDefaultUsers();
        }
    }

    // إنشاء المستخدمين الافتراضيين
    createDefaultUsers() {
        this.users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": this.hashPassword("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "إدارة تقنية المعلومات",
                "phone": "+966501234567",
                "notes": "مدير النظام الرئيسي"
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": this.hashPassword("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "الأمن السيبراني",
                "phone": "+966507654321",
                "notes": "محلل أمني متخصص"
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": this.hashPassword("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null,
                "profileImage": null,
                "department": "العمليات",
                "phone": "+966509876543",
                "notes": "مشغل نظام مراقبة"
            }
        };

        this.saveUsers();
    }

    // حفظ المستخدمين
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('خطأ في حفظ المستخدمين:', error);
        }
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    // التحقق من المصادقة
    checkAuthentication() {
        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        const session = sessions[sessionId];

        if (!session) return false;

        // فحص انتهاء الصلاحية
        if (new Date() > new Date(session.expiresAt)) {
            this.logout();
            return false;
        }

        // تحميل المستخدم الحالي
        this.currentUser = this.users[session.username];
        return this.currentUser && this.currentUser.isActive;
    }

    // تسجيل الدخول
    async login(username, password, rememberMe = false) {
        try {
            const user = this.users[username];

            if (!user) {
                throw new Error('اسم المستخدم غير موجود');
            }

            if (!user.isActive) {
                throw new Error('هذا الحساب معطل');
            }

            // فحص محاولات تسجيل الدخول
            if (user.loginAttempts >= 5) {
                const lastAttempt = new Date(user.lastLoginAttempt);
                const now = new Date();
                const timeDiff = (now - lastAttempt) / (1000 * 60); // بالدقائق

                if (timeDiff < 15) {
                    throw new Error('تم حظر الحساب مؤقتاً. حاول مرة أخرى بعد 15 دقيقة');
                } else {
                    // إعادة تعيين محاولات تسجيل الدخول
                    user.loginAttempts = 0;
                }
            }

            // التحقق من كلمة المرور
            const hashedPassword = this.hashPassword(password);
            if (user.password !== hashedPassword) {
                // زيادة محاولات تسجيل الدخول الفاشلة
                user.loginAttempts = (user.loginAttempts || 0) + 1;
                user.lastLoginAttempt = new Date().toISOString();
                this.saveUsers();

                throw new Error('كلمة المرور غير صحيحة');
            }

            // تسجيل دخول ناجح
            user.loginAttempts = 0;
            user.lastLogin = new Date().toISOString();
            user.lastLoginAttempt = new Date().toISOString();

            // إنشاء جلسة
            const sessionId = this.generateSessionId();
            const session = {
                sessionId: sessionId,
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: rememberMe ?
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() :
                    new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                rememberMe: rememberMe,
                ipAddress: 'localhost',
                userAgent: navigator.userAgent
            };

            // حفظ الجلسة
            const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            sessions[sessionId] = session;
            localStorage.setItem('userSessions', JSON.stringify(sessions));
            localStorage.setItem('currentSession', sessionId);

            // تحديث المستخدم
            this.users[username] = user;
            this.saveUsers();
            this.currentUser = user;

            // تسجيل النشاط
            this.logActivity('login', `تسجيل دخول ناجح`, {
                username: user.username,
                sessionId: sessionId
            });

            return { success: true, user: user, session: session };

        } catch (error) {
            // تسجيل محاولة فاشلة
            this.logActivity('login_failed', `محاولة تسجيل دخول فاشلة: ${error.message}`, {
                username: username
            });

            throw error;
        }
    }

    // تسجيل الخروج
    logout() {
        try {
            const sessionId = localStorage.getItem('currentSession');

            if (sessionId) {
                // تسجيل النشاط
                this.logActivity('logout', 'تسجيل خروج', {
                    username: this.currentUser ? this.currentUser.username : 'unknown',
                    sessionId: sessionId
                });

                // حذف الجلسة
                const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                delete sessions[sessionId];
                localStorage.setItem('userSessions', JSON.stringify(sessions));
            }

            // مسح البيانات المحلية
            localStorage.removeItem('currentSession');
            this.currentUser = null;

            // الانتقال لصفحة تسجيل الدخول
            window.location.href = 'login.html';

        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        }
    }

    // إنشاء معرف جلسة
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تسجيل النشاط
    logActivity(type, description, details = {}) {
        try {
            const activities = JSON.parse(localStorage.getItem('activityLog')) || [];

            const activity = {
                id: 'activity_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
                type: type,
                description: description,
                timestamp: new Date().toISOString(),
                userId: this.currentUser ? this.currentUser.id : null,
                username: this.currentUser ? this.currentUser.username : 'system',
                details: details
            };

            activities.unshift(activity);

            // الاحتفاظ بآخر 1000 نشاط
            if (activities.length > 1000) {
                activities.splice(1000);
            }

            localStorage.setItem('activityLog', JSON.stringify(activities));

        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    // تحميل سجل الأنشطة
    loadActivityLog() {
        try {
            const activities = localStorage.getItem('activityLog');
            if (activities) {
                this.activityLog = JSON.parse(activities);
            }
        } catch (error) {
            console.error('خطأ في تحميل سجل الأنشطة:', error);
            this.activityLog = [];
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === 'systemUsers') {
                this.loadUsers();
            } else if (e.key === 'userSessions') {
                this.loadSessions();
            }
        });

        // مراقبة عدم النشاط
        this.setupInactivityMonitor();
    }

    // مراقبة عدم النشاط
    setupInactivityMonitor() {
        let inactivityTimer;
        const inactivityTimeout = 30 * 60 * 1000; // 30 دقيقة

        const resetTimer = () => {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(() => {
                if (this.currentUser) {
                    alert('انتهت مهلة الجلسة بسبب عدم النشاط');
                    this.logout();
                }
            }, inactivityTimeout);
        };

        // إعادة تعيين المؤقت عند النشاط
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });

        resetTimer();
    }

    // تحميل الجلسات
    loadSessions() {
        try {
            const sessions = localStorage.getItem('userSessions');
            if (sessions) {
                this.userSessions = JSON.parse(sessions);
            }
        } catch (error) {
            console.error('خطأ في تحميل الجلسات:', error);
            this.userSessions = {};
        }
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // إدارة المستخدمين - إضافة مستخدم جديد
    addUser(userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            // التحقق من البيانات المطلوبة
            if (!userData.username || !userData.password || !userData.fullName || !userData.email) {
                throw new Error('جميع الحقول مطلوبة');
            }

            // التحقق من عدم وجود المستخدم
            if (this.users[userData.username]) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }

            // إنشاء المستخدم الجديد
            const newUser = {
                id: userData.username,
                username: userData.username,
                password: this.hashPassword(userData.password),
                fullName: userData.fullName,
                email: userData.email,
                role: userData.role || 'operator',
                permissions: userData.permissions || ['read'],
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lastLoginAttempt: null,
                profileImage: userData.profileImage || null,
                department: userData.department || '',
                phone: userData.phone || '',
                notes: userData.notes || ''
            };

            // حفظ المستخدم
            this.users[userData.username] = newUser;
            this.saveUsers();

            // تسجيل النشاط
            this.logActivity('user_created', `إضافة مستخدم جديد: ${newUser.fullName}`, {
                newUsername: newUser.username,
                role: newUser.role
            });

            return { success: true, user: newUser };

        } catch (error) {
            throw error;
        }
    }

    // تحديث مستخدم
    updateUser(username, userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            const user = this.users[username];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }

            // تحديث البيانات
            const updatedUser = {
                ...user,
                fullName: userData.fullName || user.fullName,
                email: userData.email || user.email,
                role: userData.role || user.role,
                permissions: userData.permissions || user.permissions,
                department: userData.department || user.department,
                phone: userData.phone || user.phone,
                notes: userData.notes || user.notes,
                isActive: userData.isActive !== undefined ? userData.isActive : user.isActive
            };

            // تحديث كلمة المرور إذا تم توفيرها
            if (userData.password) {
                updatedUser.password = this.hashPassword(userData.password);
            }

            // حفظ التحديثات
            this.users[username] = updatedUser;
            this.saveUsers();

            // تحديث المستخدم الحالي إذا كان هو نفسه
            if (this.currentUser && this.currentUser.username === username) {
                this.currentUser = updatedUser;
            }

            // تسجيل النشاط
            this.logActivity('user_updated', `تحديث بيانات المستخدم: ${updatedUser.fullName}`, {
                username: username,
                changes: Object.keys(userData)
            });

            return { success: true, user: updatedUser };

        } catch (error) {
            throw error;
        }
    }

    // حذف مستخدم
    deleteUser(username) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }

            if (username === this.currentUser.username) {
                throw new Error('لا يمكن حذف حسابك الخاص');
            }

            const user = this.users[username];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }

            // حذف المستخدم
            delete this.users[username];
            this.saveUsers();

            // حذف جلسات المستخدم
            const sessions = JSON.parse(localStorage.getItem('userSessions')) || {};
            Object.keys(sessions).forEach(sessionId => {
                if (sessions[sessionId].username === username) {
                    delete sessions[sessionId];
                }
            });
            localStorage.setItem('userSessions', JSON.stringify(sessions));

            // تسجيل النشاط
            this.logActivity('user_deleted', `حذف المستخدم: ${user.fullName}`, {
                deletedUsername: username
            });

            return { success: true };

        } catch (error) {
            throw error;
        }
    }

    // الحصول على جميع المستخدمين
    getAllUsers() {
        return Object.values(this.users);
    }

    // البحث في المستخدمين
    searchUsers(query) {
        const users = this.getAllUsers();
        const lowerQuery = query.toLowerCase();

        return users.filter(user =>
            user.fullName.toLowerCase().includes(lowerQuery) ||
            user.username.toLowerCase().includes(lowerQuery) ||
            user.email.toLowerCase().includes(lowerQuery) ||
            user.department.toLowerCase().includes(lowerQuery)
        );
    }

    // إحصائيات المستخدمين
    getUserStats() {
        const users = this.getAllUsers();

        return {
            total: users.length,
            active: users.filter(u => u.isActive).length,
            inactive: users.filter(u => !u.isActive).length,
            admins: users.filter(u => u.role === 'admin').length,
            analysts: users.filter(u => u.role === 'analyst').length,
            operators: users.filter(u => u.role === 'operator').length,
            recentLogins: users.filter(u => {
                if (!u.lastLogin) return false;
                const lastLogin = new Date(u.lastLogin);
                const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                return lastLogin > dayAgo;
            }).length
        };
    }
}

// إنشاء مثيل عام من مدير المستخدمين
window.enhancedUserManager = new EnhancedUserManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedUserManager;
}

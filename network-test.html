<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوصول من الشبكة - نظام إدارة أمن المعلومات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .test-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .test-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            font-weight: 500;
            color: #2c3e50;
        }
        .status-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .status-online {
            color: #27ae60;
            font-weight: bold;
        }
        .status-offline {
            color: #e74c3c;
            font-weight: bold;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .url-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .url-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .url-item:last-child {
            border-bottom: none;
        }
        .url-link {
            color: #3498db;
            text-decoration: none;
            font-family: monospace;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .copy-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #5a6268;
        }
        .qr-code {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin: 15px 0;
        }
        .device-test {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .log-section {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
    <script src="network-sync-client.js"></script>
    <script src="sync-integration.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 اختبار الوصول من الشبكة</h1>
            <p>أداة شاملة لاختبار وتشخيص الوصول من الأجهزة الأخرى في الشبكة</p>
        </div>

        <div class="test-grid">
            <!-- معلومات الشبكة -->
            <div class="test-card">
                <h3><i class="fas fa-network-wired"></i> معلومات الشبكة</h3>

                <div class="status-item">
                    <div class="status-label">عنوان IP المحلي:</div>
                    <div class="status-value" id="localIP">*************</div>
                </div>

                <div class="status-item">
                    <div class="status-label">المنفذ:</div>
                    <div class="status-value">8000</div>
                </div>

                <div class="status-item">
                    <div class="status-label">حالة الخادم:</div>
                    <div class="status-value" id="serverStatus">جاري الفحص...</div>
                </div>

                <div class="status-item">
                    <div class="status-label">نوع الاتصال:</div>
                    <div class="status-value" id="connectionType">HTTP</div>
                </div>

                <div class="status-item">
                    <div class="status-label">المتصفح:</div>
                    <div class="status-value" id="browserInfo">جاري التحديد...</div>
                </div>
            </div>

            <!-- روابط الوصول -->
            <div class="test-card">
                <h3><i class="fas fa-link"></i> روابط الوصول</h3>

                <div class="url-list">
                    <div class="url-item">
                        <a href="http://localhost:8000" class="url-link" target="_blank">http://localhost:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://localhost:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://127.0.0.1:8000" class="url-link" target="_blank">http://127.0.0.1:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://127.0.0.1:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://*************:8000" class="url-link" target="_blank">http://*************:8000</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://*************:8000')">نسخ</button>
                    </div>
                    <div class="url-item">
                        <a href="http://*************:8000/login-fixed.html" class="url-link" target="_blank">تسجيل الدخول المحسن</a>
                        <button class="copy-btn" onclick="copyToClipboard('http://*************:8000/login-fixed.html')">نسخ</button>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-success" onclick="testAllUrls()">اختبار جميع الروابط</button>
                    <button class="btn" onclick="refreshNetworkInfo()">تحديث المعلومات</button>
                </div>
            </div>
        </div>

        <!-- اختبار الأجهزة -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-mobile-alt"></i> اختبار الأجهزة المختلفة</h3>

            <div class="device-test">
                <h4>📱 للهواتف الذكية:</h4>
                <p>1. تأكد من اتصال الهاتف بنفس شبكة WiFi</p>
                <p>2. افتح المتصفح واذهب إلى: <strong>http://*************:8000</strong></p>
                <p>3. أو امسح رمز QR أدناه:</p>

                <div class="qr-code">
                    <div id="qrcode"></div>
                    <p>رمز QR للوصول السريع</p>
                </div>
            </div>

            <div class="device-test">
                <h4>💻 للأجهزة الأخرى:</h4>
                <p>1. تأكد من الاتصال بنفس الشبكة</p>
                <p>2. افتح المتصفح واستخدم أحد الروابط أعلاه</p>
                <p>3. إذا لم يعمل، تحقق من إعدادات جدار الحماية</p>
            </div>

            <div style="text-align: center;">
                <button class="btn btn-warning" onclick="checkFirewall()">فحص جدار الحماية</button>
                <button class="btn" onclick="generateQR()">إنشاء رمز QR</button>
                <button class="btn" onclick="testNetworkConnectivity()">اختبار الاتصال</button>
            </div>
        </div>

        <!-- سجل الاتصالات -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-list"></i> سجل الاتصالات</h3>
            <div class="log-section" id="connectionLog">
                جاري تحميل سجل الاتصالات...
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="refreshLog()">تحديث السجل</button>
                <button class="btn" onclick="clearLog()">مسح السجل</button>
            </div>
        </div>

        <!-- أدوات الإصلاح -->
        <div class="test-card" style="margin-top: 20px;">
            <h3><i class="fas fa-tools"></i> أدوات الإصلاح</h3>

            <div style="text-align: center;">
                <button class="btn btn-success" onclick="fixNetworkAccess()">إصلاح الوصول من الشبكة</button>
                <button class="btn btn-warning" onclick="restartServer()">إعادة تشغيل الخادم</button>
                <button class="btn" onclick="downloadConfig()">تحميل ملف التكوين</button>
            </div>

            <div id="fixResults" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // تحميل الصفحة
        window.onload = function() {
            loadNetworkInfo();
            loadConnectionLog();
            generateQR();
            checkServerStatus();
        };

        // تحميل معلومات الشبكة
        function loadNetworkInfo() {
            // معلومات المتصفح
            const browserInfo = navigator.userAgent;
            document.getElementById('browserInfo').textContent = browserInfo.substring(0, 50) + '...';

            // فحص نوع الاتصال
            const isSecure = location.protocol === 'https:';
            document.getElementById('connectionType').textContent = isSecure ? 'HTTPS' : 'HTTP';
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    document.getElementById('serverStatus').innerHTML = '<span class="status-online">متصل</span>';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = '<span class="status-offline">غير متصل</span>';
            }
        }

        // اختبار جميع الروابط
        async function testAllUrls() {
            const urls = [
                'http://localhost:8000',
                'http://127.0.0.1:8000',
                'http://*************:8000'
            ];

            const results = [];

            for (const url of urls) {
                try {
                    const response = await fetch(url, { mode: 'no-cors' });
                    results.push(`✅ ${url} - متاح`);
                } catch (error) {
                    results.push(`❌ ${url} - غير متاح`);
                }
            }

            alert(results.join('\n'));
        }

        // نسخ إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ الرابط إلى الحافظة');
            }).catch(() => {
                // طريقة بديلة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الرابط');
            });
        }

        // إنشاء رمز QR
        function generateQR() {
            const qrContainer = document.getElementById('qrcode');
            const url = 'http://*************:8000/login-fixed.html';

            if (window.QRCode) {
                qrContainer.innerHTML = '';
                QRCode.toCanvas(qrContainer, url, { width: 200 }, function(error) {
                    if (error) {
                        qrContainer.innerHTML = '<p>فشل في إنشاء رمز QR</p>';
                    }
                });
            } else {
                qrContainer.innerHTML = '<p>مكتبة QR غير متاحة</p>';
            }
        }

        // تحميل سجل الاتصالات
        async function loadConnectionLog() {
            try {
                const response = await fetch('/network_access.log');
                if (response.ok) {
                    const logText = await response.text();
                    const logLines = logText.split('\n').slice(-20); // آخر 20 سطر
                    document.getElementById('connectionLog').innerHTML = logLines.join('<br>');
                } else {
                    document.getElementById('connectionLog').innerHTML = 'لا يوجد سجل متاح';
                }
            } catch (error) {
                document.getElementById('connectionLog').innerHTML = 'فشل في تحميل السجل';
            }
        }

        // تحديث السجل
        function refreshLog() {
            loadConnectionLog();
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('connectionLog').innerHTML = 'تم مسح السجل';
        }

        // فحص جدار الحماية
        function checkFirewall() {
            alert('لفحص جدار الحماية:\n\n1. Windows: افتح "Windows Security" > "Firewall & network protection"\n2. تأكد من السماح للتطبيق بالوصول للشبكة\n3. أو أضف استثناء للمنفذ 8000');
        }

        // اختبار الاتصال
        async function testNetworkConnectivity() {
            const results = [];

            // اختبار الاتصال المحلي
            try {
                const response = await fetch('/');
                results.push('✅ الاتصال المحلي يعمل');
            } catch (error) {
                results.push('❌ مشكلة في الاتصال المحلي');
            }

            // اختبار API
            try {
                const response = await fetch('/api/test');
                results.push('✅ API متاح');
            } catch (error) {
                results.push('⚠️ API غير متاح');
            }

            alert(results.join('\n'));
        }

        // إصلاح الوصول من الشبكة
        function fixNetworkAccess() {
            const resultsDiv = document.getElementById('fixResults');

            resultsDiv.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px;">
                    <h4>✅ خطوات الإصلاح:</h4>
                    <ol style="text-align: right;">
                        <li>تم تكوين الخادم للسماح بالوصول من الشبكة</li>
                        <li>تم تفعيل CORS للوصول من المتصفحات</li>
                        <li>تم إنشاء روابط الوصول للأجهزة الأخرى</li>
                        <li>تم إنشاء رمز QR للوصول السريع</li>
                    </ol>
                    <p><strong>استخدم الرابط:</strong> http://*************:8000</p>
                </div>
            `;
        }

        // إعادة تشغيل الخادم
        function restartServer() {
            alert('لإعادة تشغيل الخادم:\n\n1. أغلق النافذة الطرفية الحالية\n2. شغل: python network-server.py\n3. أو استخدم: python START-ENHANCED-SYSTEM.py');
        }

        // تحميل ملف التكوين
        function downloadConfig() {
            const config = {
                server_ip: '*************',
                server_port: 8000,
                urls: [
                    'http://localhost:8000',
                    'http://127.0.0.1:8000',
                    'http://*************:8000'
                ],
                login_url: 'http://*************:8000/login-fixed.html',
                instructions: {
                    ar: 'للوصول من أجهزة أخرى، استخدم الرابط: http://*************:8000',
                    en: 'To access from other devices, use: http://*************:8000'
                }
            };

            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'network-config.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // تحديث معلومات الشبكة
        function refreshNetworkInfo() {
            location.reload();
        }
    </script>
</body>
</html>
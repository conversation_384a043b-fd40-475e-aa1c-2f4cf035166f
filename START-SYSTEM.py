#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة أمن المعلومات
Start ISMS System
"""

import subprocess
import time
import sys
import os
import webbrowser
import socket

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def main():
    local_ip = get_local_ip()
    
    print("=" * 60)
    print("تشغيل نظام إدارة أمن المعلومات")
    print("Starting Information Security Management System")
    print("=" * 60)
    print(f"عنوان IP المحلي: {local_ip}")
    print()
    
    # تشغيل الخادم الرئيسي
    print("1. تشغيل الخادم الرئيسي...")
    try:
        main_process = subprocess.Popen([
            sys.executable, 'start-server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("   ✅ تم تشغيل الخادم الرئيسي على المنفذ 8000")
    except Exception as e:
        print(f"   ❌ فشل في تشغيل الخادم الرئيسي: {e}")
        return
    
    # انتظار قصير
    time.sleep(3)
    
    # تشغيل خادم المزامنة
    print("2. تشغيل خادم المزامنة...")
    try:
        sync_process = subprocess.Popen([
            sys.executable, 'sync-server-simple.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("   ✅ تم تشغيل خادم المزامنة على المنفذ 8001")
    except Exception as e:
        print(f"   ❌ فشل في تشغيل خادم المزامنة: {e}")
        print("   ⚠️  النظام سيعمل بدون مزامنة")
    
    # انتظار تشغيل الخوادم
    print("3. انتظار تشغيل الخوادم...")
    time.sleep(5)
    
    # فحص حالة الخوادم
    print("4. فحص حالة الخوادم...")
    
    # فحص الخادم الرئيسي
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(2)
            result = s.connect_ex(('localhost', 8000))
            if result == 0:
                print("   ✅ الخادم الرئيسي يعمل")
                main_server_ok = True
            else:
                print("   ❌ الخادم الرئيسي لا يستجيب")
                main_server_ok = False
    except:
        print("   ❌ خطأ في فحص الخادم الرئيسي")
        main_server_ok = False
    
    # فحص خادم المزامنة
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(2)
            result = s.connect_ex(('localhost', 8001))
            if result == 0:
                print("   ✅ خادم المزامنة يعمل")
                sync_server_ok = True
            else:
                print("   ❌ خادم المزامنة لا يستجيب")
                sync_server_ok = False
    except:
        print("   ❌ خطأ في فحص خادم المزامنة")
        sync_server_ok = False
    
    # فتح المتصفح
    if main_server_ok:
        print("5. فتح المتصفح...")
        try:
            webbrowser.open("http://localhost:8000/login.html")
            print("   ✅ تم فتح المتصفح")
        except Exception as e:
            print(f"   ❌ فشل في فتح المتصفح: {e}")
    
    print()
    print("=" * 60)
    print("حالة النظام:")
    print("=" * 60)
    
    if main_server_ok:
        print("✅ النظام يعمل بنجاح!")
        print()
        print("روابط الوصول:")
        print(f"   محلي: http://localhost:8000/login.html")
        print(f"   شبكة: http://{local_ip}:8000/login.html")
        print()
        print("بيانات الدخول:")
        print("   👤 admin / admin123 (مدير النظام)")
        print("   👤 analyst / analyst123 (محلل أمني)")
        print("   👤 operator / operator123 (مشغل)")
        print()
        
        if sync_server_ok:
            print("🔄 المزامنة تعمل:")
            print(f"   خادم المزامنة: http://{local_ip}:8001/api/sync/status")
            print("   يمكن للأجهزة الأخرى في الشبكة الوصول والمزامنة")
        else:
            print("⚠️  المزامنة غير متاحة:")
            print("   النظام يعمل محلياً فقط")
            print("   لتفعيل المزامنة: شغل sync-server-simple.py منفصلاً")
        
        print()
        print("أدوات مفيدة:")
        print("   🔍 http://localhost:8000/login-debug.html - تشخيص تسجيل الدخول")
        print("   🧪 http://localhost:8000/sync-test.html - اختبار المزامنة")
        
    else:
        print("❌ فشل في تشغيل النظام!")
        print()
        print("خطوات الإصلاح:")
        print("   1. تأكد من أن المنفذ 8000 غير مستخدم")
        print("   2. شغل: python start-server.py")
        print("   3. تحقق من رسائل الخطأ")
    
    print()
    print("للإيقاف: اضغط Ctrl+C في نوافذ Terminal")
    print("=" * 60)
    
    # انتظار الإيقاف
    try:
        input("\nاضغط Enter للإيقاف...")
    except KeyboardInterrupt:
        pass
    
    # إيقاف العمليات
    print("\nإيقاف النظام...")
    try:
        main_process.terminate()
        if 'sync_process' in locals():
            sync_process.terminate()
        print("تم إيقاف النظام بنجاح")
    except:
        print("تم إيقاف النظام")

if __name__ == "__main__":
    main()

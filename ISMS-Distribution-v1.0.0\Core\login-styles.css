/* CSS Variables for Blue & Gray Theme */
:root {
    --primary-blue: #1e40af;
    --secondary-blue: #2563eb;
    --accent-blue: #3b82f6;
    --light-blue: #dbeafe;
    --dark-blue: #1d4ed8;
    --navy-blue: #1e3a8a;
    --primary-gray: #6b7280;
    --light-gray: #f8fafc;
    --medium-gray: #9ca3af;
    --dark-gray: #374151;
    --darker-gray: #1f2937;
    --white: #ffffff;
    --black: #111827;
    --success: #059669;
    --warning: #d97706;
    --danger: #dc2626;
    --info: #0284c7;

    /* Enhanced Blue & Gray Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    --gradient-blue: linear-gradient(135deg, #1e3a8a 0%, #1e40af 30%, #2563eb 70%, #3b82f6 100%);
    --gradient-blue-light: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

    /* Background and Text */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e5e7eb;
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-card: rgba(255, 255, 255, 0.95);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #d1d5db;
    --border-light: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--gradient-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    color: var(--text-primary);
}

/* Animated Background */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 15s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    bottom: 30%;
    right: 40%;
    animation-delay: 8s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Login Container */
.login-container {
    width: 100%;
    max-width: 450px;
    margin: 2rem;
    position: relative;
    z-index: 1;
}

.login-card {
    background: var(--bg-card);
    backdrop-filter: blur(25px);
    border-radius: 24px;
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Header Section */
.login-header {
    padding: 3rem 2rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    position: relative;
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.logo-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-image:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 12px 35px rgba(30, 64, 175, 0.3);
}

.logo-icon {
    font-size: 4rem;
    color: var(--primary-blue);
    filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.3));
    transition: all 0.3s ease;
}

.logo-icon:hover {
    transform: scale(1.1) rotate(5deg);
    color: var(--accent-blue);
}

.system-title {
    font-size: 2rem;
    font-weight: 800;
    background: var(--gradient-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Form Section */
.login-form-section {
    padding: 2rem;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.form-header h2 i {
    color: var(--primary-blue);
}

.form-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.form-group label i {
    color: var(--primary-blue);
    width: 16px;
}

.form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    font-family: inherit;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
    transform: translateY(-2px);
    background: var(--bg-primary);
}

.form-group input:hover {
    border-color: var(--accent-blue);
    transform: translateY(-1px);
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-blue);
    transition: width 0.4s ease;
    border-radius: 1px;
}

.form-group input:focus + .input-border {
    width: 100%;
}

/* Password Input */
.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: rgba(30, 64, 175, 0.1);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.remember-me:hover {
    color: var(--text-primary);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    background: var(--bg-primary);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    transform: scale(1.1);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.forgot-password:hover {
    color: var(--dark-blue);
    text-decoration: underline;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--gradient-blue);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin-top: 1rem;
    font-family: inherit;
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.3);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(30, 64, 175, 0.4);
}

.login-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Error Message */
.error-message {
    background: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.3);
    color: var(--danger);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Footer */
.login-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    background: rgba(30, 64, 175, 0.05);
    border-top: 1px solid rgba(30, 64, 175, 0.1);
}

.login-footer p {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        margin: 1rem;
    }

    .login-header {
        padding: 2rem 1.5rem 1.5rem;
    }

    .login-form-section {
        padding: 1.5rem;
    }

    .system-title {
        font-size: 1.6rem;
    }

    .logo-image {
        width: 60px;
        height: 60px;
    }

    .logo-icon {
        font-size: 3rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .form-group input {
        padding: 0.875rem 1rem;
    }

    .login-btn {
        padding: 0.875rem 1.5rem;
    }
}

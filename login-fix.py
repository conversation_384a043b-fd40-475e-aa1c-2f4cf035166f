#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل تسجيل الدخول
Login Issues Fix Tool
"""

import os
import json
import webbrowser
import subprocess
import sys
from datetime import datetime

class LoginFixer:
    def __init__(self):
        self.fixes_applied = []
        
    def print_header(self):
        """طباعة رأس الإصلاح"""
        print("=" * 60)
        print("إصلاح مشاكل تسجيل الدخول")
        print("Login Issues Fix Tool")
        print("=" * 60)
        print()
    
    def reset_user_data(self):
        """إعادة تعيين بيانات المستخدمين"""
        print("إعادة تعيين بيانات المستخدمين...")
        
        # إنشاء مجلد البيانات
        os.makedirs("data", exist_ok=True)
        
        # دالة تشفير بسيطة
        def hash_password(password):
            hash_val = 0
            for char in password:
                hash_val = ((hash_val << 5) - hash_val) + ord(char)
                hash_val = hash_val & 0xFFFFFFFF
                if hash_val > 0x7FFFFFFF:
                    hash_val -= 0x100000000
            return str(hash_val)
        
        # المستخدمين الافتراضيين مع كلمات مرور محدثة
        default_users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": hash_password("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": hash_password("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": hash_password("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": True,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": None,
                "loginAttempts": 0,
                "lastLoginAttempt": None
            }
        }
        
        try:
            # حفظ في ملف data/users.json
            with open(os.path.join("data", "users.json"), 'w', encoding='utf-8') as f:
                json.dump(default_users, f, ensure_ascii=False, indent=2)
            
            print("   ✅ تم إعادة تعيين بيانات المستخدمين")
            print("   👤 admin / admin123")
            print("   👤 analyst / analyst123")
            print("   👤 operator / operator123")
            
            self.fixes_applied.append("إعادة تعيين بيانات المستخدمين")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إعادة تعيين البيانات: {e}")
            return False
    
    def clear_browser_data(self):
        """مسح بيانات المتصفح"""
        print("مسح بيانات المتصفح...")
        
        clear_script = '''
        // مسح جميع بيانات localStorage
        try {
            localStorage.clear();
            console.log("تم مسح localStorage");
        } catch (e) {
            console.error("خطأ في مسح localStorage:", e);
        }
        
        // مسح جميع بيانات sessionStorage
        try {
            sessionStorage.clear();
            console.log("تم مسح sessionStorage");
        } catch (e) {
            console.error("خطأ في مسح sessionStorage:", e);
        }
        
        // إعادة تحميل الصفحة
        window.location.reload();
        '''
        
        # إنشاء صفحة مسح البيانات
        clear_page = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مسح بيانات المتصفح</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
        .container {{ max-width: 500px; margin: 0 auto; }}
        button {{ padding: 15px 30px; font-size: 16px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }}
        .btn-danger {{ background: #dc3545; color: white; }}
        .btn-success {{ background: #28a745; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 مسح بيانات المتصفح</h1>
        <p>سيتم مسح جميع البيانات المحفوظة في المتصفح</p>
        <button class="btn-danger" onclick="clearData()">مسح البيانات</button>
        <button class="btn-success" onclick="window.location.href='login.html'">الذهاب لتسجيل الدخول</button>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        function clearData() {{
            const result = document.getElementById('result');
            try {{
                localStorage.clear();
                sessionStorage.clear();
                result.innerHTML = '<p style="color: green;">✅ تم مسح البيانات بنجاح</p>';
                setTimeout(() => {{
                    window.location.href = 'login.html';
                }}, 2000);
            }} catch (e) {{
                result.innerHTML = '<p style="color: red;">❌ خطأ في مسح البيانات: ' + e.message + '</p>';
            }}
        }}
    </script>
</body>
</html>'''
        
        try:
            with open('clear-browser-data.html', 'w', encoding='utf-8') as f:
                f.write(clear_page)
            
            print("   ✅ تم إنشاء صفحة مسح البيانات: clear-browser-data.html")
            self.fixes_applied.append("إنشاء صفحة مسح بيانات المتصفح")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إنشاء صفحة مسح البيانات: {e}")
            return False
    
    def fix_auth_js(self):
        """إصلاح ملف المصادقة"""
        print("فحص وإصلاح ملف المصادقة...")
        
        if not os.path.exists('auth.js'):
            print("   ❌ ملف auth.js غير موجود")
            return False
        
        try:
            with open('auth.js', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محتملة
            fixes_needed = []
            
            # التأكد من وجود دالة loadUsers
            if 'loadUsers()' not in content:
                fixes_needed.append("إضافة دالة loadUsers")
            
            # التأكد من وجود معالج الأخطاء
            if 'catch (error)' not in content:
                fixes_needed.append("إضافة معالج الأخطاء")
            
            if fixes_needed:
                print(f"   ⚠️  يحتاج إصلاحات: {', '.join(fixes_needed)}")
                # يمكن إضافة إصلاحات تلقائية هنا
            else:
                print("   ✅ ملف المصادقة سليم")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص ملف المصادقة: {e}")
            return False
    
    def create_login_debug_page(self):
        """إنشاء صفحة تشخيص تسجيل الدخول"""
        print("إنشاء صفحة تشخيص تسجيل الدخول...")
        
        debug_page = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        .info { border-left: 4px solid #17a2b8; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص تسجيل الدخول</h1>
        
        <div class="card info">
            <h3>معلومات النظام</h3>
            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
            <p><strong>localStorage:</strong> <span id="localStorageStatus"></span></p>
            <p><strong>JavaScript:</strong> <span id="jsStatus">✅ مفعل</span></p>
            <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
        </div>
        
        <div class="card">
            <h3>اختبار تسجيل الدخول</h3>
            <div>
                <input type="text" id="testUser" placeholder="اسم المستخدم" value="admin">
                <input type="password" id="testPass" placeholder="كلمة المرور" value="admin123">
                <button class="btn-primary" onclick="testLogin()">اختبار</button>
            </div>
            <div id="loginResult" style="margin-top: 10px;"></div>
        </div>
        
        <div class="card">
            <h3>بيانات المستخدمين</h3>
            <button class="btn-success" onclick="loadUsers()">تحميل المستخدمين</button>
            <button class="btn-danger" onclick="resetUsers()">إعادة تعيين</button>
            <div id="usersData" class="log" style="margin-top: 10px;"></div>
        </div>
        
        <div class="card">
            <h3>سجل الأحداث</h3>
            <button class="btn-primary" onclick="clearLog()">مسح السجل</button>
            <div id="debugLog" class="log"></div>
        </div>
        
        <div class="card">
            <h3>أدوات الإصلاح</h3>
            <button class="btn-danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <button class="btn-success" onclick="window.location.href='login.html'">صفحة تسجيل الدخول</button>
            <button class="btn-success" onclick="window.location.href='index.html'">الصفحة الرئيسية</button>
        </div>
    </div>
    
    <script>
        // تحديث معلومات النظام
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
        
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            document.getElementById('localStorageStatus').innerHTML = '✅ يعمل';
        } catch (e) {
            document.getElementById('localStorageStatus').innerHTML = '❌ لا يعمل';
        }
        
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // دالة التشفير
        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }
        
        // اختبار تسجيل الدخول
        function testLogin() {
            const username = document.getElementById('testUser').value;
            const password = document.getElementById('testPass').value;
            const result = document.getElementById('loginResult');
            
            log(`اختبار تسجيل الدخول: ${username}`);
            
            try {
                // تحميل المستخدمين من data/users.json أو localStorage
                let users = {};
                
                // محاولة تحميل من localStorage أولاً
                const storedUsers = localStorage.getItem('systemUsers');
                if (storedUsers) {
                    users = JSON.parse(storedUsers);
                    log('تم تحميل المستخدمين من localStorage');
                } else {
                    log('لم يتم العثور على مستخدمين في localStorage');
                }
                
                const user = users[username];
                
                if (!user) {
                    result.innerHTML = '<div class="error">❌ المستخدم غير موجود</div>';
                    log(`المستخدم ${username} غير موجود`);
                    return;
                }
                
                if (!user.isActive) {
                    result.innerHTML = '<div class="error">❌ الحساب معطل</div>';
                    log(`الحساب ${username} معطل`);
                    return;
                }
                
                const hashedPassword = hashPassword(password);
                log(`كلمة المرور المشفرة: ${hashedPassword}`);
                log(`كلمة المرور المحفوظة: ${user.password}`);
                
                if (user.password === hashedPassword) {
                    result.innerHTML = '<div class="success">✅ تسجيل الدخول نجح</div>';
                    log(`تسجيل دخول ناجح للمستخدم ${username}`);
                } else {
                    result.innerHTML = '<div class="error">❌ كلمة المرور خاطئة</div>';
                    log(`كلمة مرور خاطئة للمستخدم ${username}`);
                }
                
            } catch (error) {
                result.innerHTML = '<div class="error">❌ خطأ: ' + error.message + '</div>';
                log(`خطأ في اختبار تسجيل الدخول: ${error.message}`);
            }
        }
        
        // تحميل المستخدمين
        function loadUsers() {
            const usersDiv = document.getElementById('usersData');
            
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
                const usersList = Object.keys(users).map(username => {
                    const user = users[username];
                    return `${username} (${user.role}) - ${user.isActive ? 'نشط' : 'معطل'}`;
                }).join('\\n');
                
                usersDiv.textContent = usersList || 'لا توجد مستخدمين';
                log(`تم تحميل ${Object.keys(users).length} مستخدم`);
                
            } catch (error) {
                usersDiv.textContent = 'خطأ في تحميل المستخدمين: ' + error.message;
                log(`خطأ في تحميل المستخدمين: ${error.message}`);
            }
        }
        
        // إعادة تعيين المستخدمين
        function resetUsers() {
            const defaultUsers = {
                "admin": {
                    "id": "admin",
                    "username": "admin",
                    "password": hashPassword("admin123"),
                    "fullName": "مدير النظام",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "analyst": {
                    "id": "analyst",
                    "username": "analyst",
                    "password": hashPassword("analyst123"),
                    "fullName": "محلل أمني",
                    "email": "<EMAIL>",
                    "role": "analyst",
                    "permissions": ["read", "write", "view_analytics"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                },
                "operator": {
                    "id": "operator",
                    "username": "operator",
                    "password": hashPassword("operator123"),
                    "fullName": "مشغل النظام",
                    "email": "<EMAIL>",
                    "role": "operator",
                    "permissions": ["read"],
                    "isActive": true,
                    "createdAt": new Date().toISOString(),
                    "lastLogin": null
                }
            };
            
            try {
                localStorage.setItem('systemUsers', JSON.stringify(defaultUsers));
                log('تم إعادة تعيين المستخدمين الافتراضيين');
                loadUsers();
            } catch (error) {
                log(`خطأ في إعادة تعيين المستخدمين: ${error.message}`);
            }
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                    log('تم مسح جميع البيانات');
                    setTimeout(() => window.location.reload(), 1000);
                } catch (error) {
                    log(`خطأ في مسح البيانات: ${error.message}`);
                }
            }
        }
        
        // سجل الأحداث
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logDiv.innerHTML += `[${timestamp}] ${message}\\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // تحميل المستخدمين عند بدء التشغيل
        loadUsers();
        log('تم تحميل صفحة التشخيص');
    </script>
</body>
</html>'''
        
        try:
            with open('login-debug.html', 'w', encoding='utf-8') as f:
                f.write(debug_page)
            
            print("   ✅ تم إنشاء صفحة التشخيص: login-debug.html")
            self.fixes_applied.append("إنشاء صفحة تشخيص تسجيل الدخول")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إنشاء صفحة التشخيص: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        print("إعادة تشغيل الخادم...")
        
        try:
            # إنهاء العمليات الموجودة
            import platform
            
            if platform.system() == "Windows":
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                             capture_output=True, shell=True)
            else:
                subprocess.run(['pkill', '-f', 'python'], capture_output=True)
            
            print("   ✅ تم إنهاء العمليات السابقة")
            
            # تشغيل الخادم الجديد
            subprocess.Popen([sys.executable, 'start-server.py'])
            print("   ✅ تم تشغيل الخادم الجديد")
            
            self.fixes_applied.append("إعادة تشغيل الخادم")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في إعادة تشغيل الخادم: {e}")
            return False
    
    def generate_fix_report(self):
        """إنشاء تقرير الإصلاح"""
        print("\nتقرير إصلاح تسجيل الدخول:")
        print("=" * 50)
        
        print(f"الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for i, fix in enumerate(self.fixes_applied, 1):
            print(f"   {i}. {fix}")
        
        print("\nالملفات المنشأة:")
        files = ['clear-browser-data.html', 'login-debug.html']
        for file in files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
        
        print("\nبيانات الدخول المحدثة:")
        print("   👤 admin / admin123 (مدير النظام)")
        print("   👤 analyst / analyst123 (محلل أمني)")
        print("   👤 operator / operator123 (مشغل)")
        
        print("\nأدوات التشخيص:")
        print("   🔍 login-debug.html - تشخيص شامل")
        print("   🧹 clear-browser-data.html - مسح بيانات المتصفح")
        
        print("=" * 50)
    
    def run_comprehensive_fix(self):
        """تشغيل الإصلاح الشامل"""
        self.print_header()
        
        print("بدء إصلاح مشاكل تسجيل الدخول...")
        print()
        
        # إعادة تعيين بيانات المستخدمين
        self.reset_user_data()
        
        # مسح بيانات المتصفح
        self.clear_browser_data()
        
        # فحص وإصلاح ملف المصادقة
        self.fix_auth_js()
        
        # إنشاء صفحة التشخيص
        self.create_login_debug_page()
        
        # إعادة تشغيل الخادم
        self.restart_server()
        
        # إنشاء التقرير
        self.generate_fix_report()
        
        # فتح صفحة التشخيص
        print("\nفتح صفحة التشخيص...")
        try:
            webbrowser.open('login-debug.html')
            print("✅ تم فتح صفحة التشخيص")
        except Exception as e:
            print(f"⚠️  فشل في فتح صفحة التشخيص: {e}")
        
        print("\n🎉 تم إصلاح جميع مشاكل تسجيل الدخول!")
        print("يمكنك الآن تسجيل الدخول باستخدام:")
        print("   admin / admin123")
        
        return True

def main():
    """الدالة الرئيسية"""
    fixer = LoginFixer()
    success = fixer.run_comprehensive_fix()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تغيير كلمة المرور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #2563eb;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #374151;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .test-steps {
            list-style: none;
            padding: 0;
        }
        
        .test-steps li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .test-steps li:last-child {
            border-bottom: none;
        }
        
        .step-number {
            background: #2563eb;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            margin-left: 10px;
        }
        
        .success {
            color: #059669;
            font-weight: bold;
        }
        
        .warning {
            color: #d97706;
            font-weight: bold;
        }
        
        .error {
            color: #dc2626;
            font-weight: bold;
        }
        
        .feature-list {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .feature-list ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .feature-list li {
            margin: 8px 0;
            color: #374151;
        }
        
        .icon {
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔐 اختبار ميزة تغيير كلمة المرور السريعة</h1>
            <p>دليل اختبار شامل للميزة الجديدة المضافة في القائمة العلوية</p>
        </div>

        <div class="test-section">
            <div class="test-title">📋 نظرة عامة على الميزة</div>
            <div class="test-description">
                تم إضافة خيار "تغيير كلمة المرور" في القائمة المنسدلة للمستخدم في أعلى الصفحة، 
                مما يوفر طريقة سريعة ومباشرة لتغيير كلمة المرور دون الحاجة لفتح الملف الشخصي الكامل.
            </div>
            
            <div class="feature-list">
                <strong>الميزات المضافة:</strong>
                <ul>
                    <li>✅ زر "تغيير كلمة المرور" في القائمة العلوية</li>
                    <li>✅ نافذة منبثقة مخصصة لتغيير كلمة المرور</li>
                    <li>✅ مؤشر قوة كلمة المرور</li>
                    <li>✅ التحقق من كلمة المرور الحالية</li>
                    <li>✅ حفظ كلمة المرور الجديدة في localStorage</li>
                    <li>✅ تسجيل النشاط في سجل الأنشطة</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 خطوات الاختبار</div>
            <ol class="test-steps">
                <li>
                    <span class="step-number">1</span>
                    افتح الصفحة الرئيسية للنظام (index.html)
                </li>
                <li>
                    <span class="step-number">2</span>
                    سجل الدخول بأي حساب مستخدم
                </li>
                <li>
                    <span class="step-number">3</span>
                    انقر على اسم المستخدم في أعلى يمين الصفحة
                </li>
                <li>
                    <span class="step-number">4</span>
                    تحقق من وجود خيار <span class="success">"تغيير كلمة المرور"</span> مع أيقونة المفتاح 🔑
                </li>
                <li>
                    <span class="step-number">5</span>
                    انقر على "تغيير كلمة المرور"
                </li>
                <li>
                    <span class="step-number">6</span>
                    تحقق من فتح النافذة المنبثقة بالحقول التالية:
                    <ul style="margin-top: 10px;">
                        <li>كلمة المرور الحالية</li>
                        <li>كلمة المرور الجديدة</li>
                        <li>تأكيد كلمة المرور الجديدة</li>
                        <li>مؤشر قوة كلمة المرور</li>
                    </ul>
                </li>
                <li>
                    <span class="step-number">7</span>
                    اختبر إدخال كلمة مرور حالية خاطئة - يجب أن تظهر رسالة خطأ
                </li>
                <li>
                    <span class="step-number">8</span>
                    اختبر إدخال كلمة مرور جديدة قصيرة (أقل من 6 أحرف) - يجب أن تظهر رسالة خطأ
                </li>
                <li>
                    <span class="step-number">9</span>
                    اختبر عدم تطابق كلمة المرور الجديدة مع التأكيد - يجب أن تظهر رسالة خطأ
                </li>
                <li>
                    <span class="step-number">10</span>
                    أدخل بيانات صحيحة وانقر "تغيير كلمة المرور"
                </li>
                <li>
                    <span class="step-number">11</span>
                    تحقق من ظهور رسالة نجاح وإغلاق النافذة تلقائياً
                </li>
                <li>
                    <span class="step-number">12</span>
                    سجل الخروج وحاول تسجيل الدخول بكلمة المرور الجديدة
                </li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">⚠️ اختبارات إضافية</div>
            <div class="test-description">
                اختبر السيناريوهات التالية للتأكد من عمل الميزة بشكل صحيح:
            </div>
            <ul class="test-steps">
                <li>إغلاق النافذة بالضغط على زر X</li>
                <li>إغلاق النافذة بالضغط على مفتاح Escape</li>
                <li>إغلاق النافذة بالنقر خارجها</li>
                <li>إغلاق النافذة بزر "إلغاء"</li>
                <li>اختبار مؤشر قوة كلمة المرور مع كلمات مرور مختلفة</li>
                <li>اختبار الميزة على الهاتف المحمول</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">📁 الملفات المحدثة</div>
            <div class="feature-list">
                <strong>تم تحديث الملفات التالية:</strong>
                <ul>
                    <li><code>index.html</code> - إضافة الزر والنافذة المنبثقة</li>
                    <li><code>script.js</code> - إضافة الوظائف الجديدة</li>
                    <li><code>styles.css</code> - إضافة الأنماط للنافذة المنبثقة</li>
                    <li><code>ISMS-Portable/index.html</code> - نفس التحديثات للنسخة المحمولة</li>
                    <li><code>ISMS-Portable/script.js</code> - نفس الوظائف للنسخة المحمولة</li>
                    <li><code>ISMS-Portable/styles.css</code> - نفس الأنماط للنسخة المحمولة</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ النتائج المتوقعة</div>
            <div class="test-description">
                عند اكتمال الاختبار بنجاح، يجب أن تحصل على:
            </div>
            <ul class="test-steps">
                <li><span class="success">✓</span> زر تغيير كلمة المرور يظهر في القائمة العلوية</li>
                <li><span class="success">✓</span> النافذة المنبثقة تفتح وتغلق بشكل صحيح</li>
                <li><span class="success">✓</span> التحقق من كلمة المرور الحالية يعمل</li>
                <li><span class="success">✓</span> مؤشر قوة كلمة المرور يعمل بشكل تفاعلي</li>
                <li><span class="success">✓</span> كلمة المرور الجديدة تُحفظ في localStorage</li>
                <li><span class="success">✓</span> يمكن تسجيل الدخول بكلمة المرور الجديدة</li>
                <li><span class="success">✓</span> النشاط يُسجل في سجل الأنشطة</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 استكشاف الأخطاء</div>
            <div class="test-description">
                إذا واجهت مشاكل، تحقق من:
            </div>
            <ul class="test-steps">
                <li><span class="warning">⚠</span> تأكد من تحديث جميع الملفات</li>
                <li><span class="warning">⚠</span> امسح cache المتصفح</li>
                <li><span class="warning">⚠</span> تحقق من وحدة التحكم للأخطاء</li>
                <li><span class="warning">⚠</span> تأكد من تسجيل الدخول قبل الاختبار</li>
                <li><span class="error">✗</span> إذا لم يظهر الزر، تحقق من ملف index.html</li>
                <li><span class="error">✗</span> إذا لم تعمل الوظائف، تحقق من ملف script.js</li>
            </ul>
        </div>
    </div>
</body>
</html>

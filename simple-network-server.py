#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم شبكة مبسط لنظام إدارة أمن المعلومات
Simple Network Server for ISMS
"""

import os
import sys
import json
import socket
import http.server
import socketserver
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

class NetworkHandler(http.server.SimpleHTTPRequestHandler):
    """معالج طلبات HTTP للشبكة"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        """إضافة headers للسماح بالوصول من الشبكة"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        super().end_headers()
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        """معالجة طلبات GET"""
        # تسجيل الطلب
        client_ip = self.client_address[0]
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {client_ip} - GET {self.path}")
        
        # معالجة API
        if self.path == '/api/status':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {'status': 'online', 'timestamp': timestamp}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return
        
        # معالجة عادية
        super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        client_ip = self.client_address[0]
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {client_ip} - POST {self.path}")
        
        # قراءة البيانات
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
        
        # معالجة API
        if self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {'success': True, 'timestamp': timestamp}
            self.wfile.write(json.dumps(response).encode('utf-8'))
            return
        
        # معالجة عادية
        super().do_POST()

def main():
    """الوظيفة الرئيسية"""
    
    # الحصول على IP المحلي
    local_ip = get_local_ip()
    port = 8000
    
    print("🌐 خادم الشبكة المبسط لنظام إدارة أمن المعلومات")
    print("=" * 60)
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print(f"🔌 المنفذ: {port}")
    
    try:
        # إنشاء الخادم
        with socketserver.TCPServer(("0.0.0.0", port), NetworkHandler) as httpd:
            httpd.allow_reuse_address = True
            
            print(f"\n🚀 تم بدء الخادم بنجاح!")
            print(f"🌐 روابط الوصول:")
            print(f"   • المحلي: http://localhost:{port}")
            print(f"   • الشبكة: http://{local_ip}:{port}")
            print(f"   • تسجيل الدخول: http://{local_ip}:{port}/login-fixed.html")
            print(f"   • اختبار الشبكة: http://{local_ip}:{port}/network-test.html")
            
            print(f"\n📱 للوصول من الهواتف:")
            print(f"   1. تأكد من الاتصال بنفس شبكة WiFi")
            print(f"   2. افتح المتصفح واذهب إلى: http://{local_ip}:{port}")
            print(f"   3. استخدم بيانات الدخول: admin/admin123")
            
            print(f"\n💡 نصائح:")
            print(f"   • إذا لم يعمل من الأجهزة الأخرى، شغل: fix-firewall.bat")
            print(f"   • استخدم صفحة اختبار الشبكة للتشخيص")
            print(f"   • اضغط Ctrl+C لإيقاف الخادم")
            
            print(f"\n✅ الخادم جاهز ويقبل الاتصالات...")
            print(f"📊 سيتم عرض سجل الطلبات أدناه:")
            print("-" * 60)
            
            # بدء الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ المنفذ {port} مستخدم بالفعل")
            print(f"💡 جرب إيقاف الخادم الآخر أو استخدم منفذ مختلف")
        else:
            print(f"❌ خطأ في بدء الخادم: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()

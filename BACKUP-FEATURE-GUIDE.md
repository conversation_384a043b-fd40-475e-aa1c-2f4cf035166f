# 💾 دليل ميزة النسخة الاحتياطية - نظام إدارة أمن المعلومات

## 📋 نظرة عامة
تم إضافة ميزة النسخة الاحتياطية الشاملة لحماية جميع بيانات النظام وضمان عدم فقدانها.

---

## 🎯 مواقع أزرار النسخة الاحتياطية

### 1. في القائمة المنسدلة للمستخدم
- **الموقع:** أعلى يمين الصفحة → اسم المستخدم → "نسخة احتياطية"
- **الأيقونة:** 📥 (أيقونة التحميل)
- **الاستخدام:** للوصول السريع من أي مكان في النظام

### 2. في أسفل الصفحة (زر عائم)
- **الموقع:** أسفل يسار الصفحة (بجانب زر تسجيل الخروج)
- **الأيقونة:** 📥 (أيقونة التحميل)
- **اللون:** أخضر
- **الاستخدام:** وصول سريع دائم

### 3. عند تسجيل الخروج (تلقائي)
- **التفعيل:** يظهر تلقائياً عند الضغط على تسجيل الخروج
- **الخيار:** اختياري - يمكن تخطيه
- **الهدف:** ضمان حفظ البيانات قبل الخروج

---

## 📦 البيانات المشمولة في النسخة الاحتياطية

### البيانات الأساسية:
- ✅ **بيانات المستخدمين** (systemUsers)
- ✅ **الجلسات النشطة** (userSessions)
- ✅ **الجلسة الحالية** (currentSession)
- ✅ **الأحداث الأمنية** (securityEvents)
- ✅ **إعدادات النظام** (systemSettings)

### البيانات المتقدمة:
- ✅ **سجلات الأنشطة** (activityLogs)
- ✅ **تقييمات المخاطر** (riskAssessments)
- ✅ **تقارير الحوادث** (incidentReports)
- ✅ **بيانات الامتثال** (complianceData)
- ✅ **مسارات التدقيق** (auditTrails)

### البيانات المتزامنة:
- ✅ **البيانات المتزامنة** من خادم الشبكة
- ✅ **حالة المزامنة** والإعدادات

---

## 🔧 كيفية الاستخدام

### الطريقة الأولى: من القائمة المنسدلة
1. اضغط على اسم المستخدم أعلى يمين الصفحة
2. اختر "نسخة احتياطية" من القائمة
3. اضغط "موافق" في رسالة التأكيد
4. سيتم تحميل الملف تلقائياً

### الطريقة الثانية: من الزر العائم
1. اضغط على الزر الأخضر أسفل يسار الصفحة
2. اقرأ تفاصيل البيانات المشمولة
3. اضغط "موافق" للمتابعة
4. سيتم تحميل الملف تلقائياً

### الطريقة الثالثة: عند تسجيل الخروج
1. اضغط على زر تسجيل الخروج
2. ستظهر رسالة تسأل عن النسخة الاحتياطية
3. اختر "موافق" لإنشاء نسخة احتياطية أولاً
4. أو "إلغاء" لتسجيل الخروج مباشرة

---

## 📄 تفاصيل ملف النسخة الاحتياطية

### تنسيق اسم الملف:
```
ISMS-Backup-YYYY-MM-DD-HH-MM-SS.json
```
**مثال:** `ISMS-Backup-2024-07-14-15-30-45.json`

### محتويات الملف:
```json
{
  "timestamp": "2024-07-14T15:30:45.123Z",
  "version": "2.0",
  "systemInfo": {
    "userAgent": "معلومات المتصفح",
    "url": "رابط الصفحة",
    "language": "ar"
  },
  "data": {
    "systemUsers": { ... },
    "userSessions": { ... },
    "securityEvents": { ... },
    ...
  },
  "syncData": {
    "users": { ... },
    "sessions": { ... },
    ...
  }
}
```

---

## 🔒 الأمان والخصوصية

### حماية البيانات:
- 🔐 **تشفير محلي:** البيانات محمية في المتصفح
- 📱 **عدم الإرسال:** لا يتم إرسال البيانات لخوادم خارجية
- 💾 **حفظ محلي:** الملف يُحفظ على جهازك فقط

### التوصيات الأمنية:
- 🔒 احفظ ملف النسخة الاحتياطية في مكان آمن
- 🚫 لا تشارك الملف مع أشخاص غير مخولين
- 🔄 أنشئ نسخ احتياطية دورية (يومية أو أسبوعية)
- 🗑️ احذف النسخ القديمة غير المطلوبة

---

## 🔄 استعادة النسخة الاحتياطية

### الوظيفة متاحة (للمطورين):
```javascript
// استعادة من ملف
const fileInput = document.createElement('input');
fileInput.type = 'file';
fileInput.accept = '.json';
fileInput.onchange = function(e) {
    const file = e.target.files[0];
    if (file) {
        restoreBackup(file);
    }
};
fileInput.click();
```

### خطوات الاستعادة:
1. تحديد ملف النسخة الاحتياطية
2. التحقق من صحة البيانات
3. استعادة البيانات إلى localStorage
4. إعادة تحميل الصفحة

---

## 📊 الإحصائيات والمراقبة

### معلومات تُسجل:
- ✅ **وقت إنشاء النسخة** الاحتياطية
- ✅ **حجم البيانات** المحفوظة
- ✅ **عدد أنواع البيانات** المشمولة
- ✅ **حالة العملية** (نجح/فشل)

### تسجيل في سجل الأنشطة:
- 📝 يتم تسجيل كل عملية نسخ احتياطي
- 🕐 مع الوقت والتاريخ
- 👤 ومعرف المستخدم
- 📊 وتفاصيل العملية

---

## 🚨 حل المشاكل الشائعة

### مشكلة: لا يتم تحميل الملف
**الحلول:**
1. تحقق من إعدادات المتصفح للتحميلات
2. تأكد من وجود مساحة كافية على القرص
3. جرب متصفح آخر
4. تحقق من برامج مكافحة الفيروسات

### مشكلة: الملف فارغ أو تالف
**الحلول:**
1. تأكد من وجود بيانات في النظام
2. جرب إنشاء النسخة مرة أخرى
3. تحقق من وحدة التحكم للأخطاء
4. أعد تحميل الصفحة وحاول مرة أخرى

### مشكلة: رسالة خطأ عند الإنشاء
**الحلول:**
1. تحقق من اتصال الإنترنت
2. أعد تحميل الصفحة
3. امسح cache المتصفح
4. تحقق من إعدادات JavaScript

---

## 💡 نصائح للاستخدام الأمثل

### للاستخدام اليومي:
- 📅 أنشئ نسخة احتياطية يومياً
- 🕐 اختر وقت ثابت (مثل نهاية العمل)
- 📁 نظم الملفات في مجلدات بالتاريخ
- 🔍 راجع محتوى النسخة دورياً

### للمديرين:
- 👥 ادرب المستخدمين على الميزة
- 📋 ضع سياسة للنسخ الاحتياطية
- 🔒 حدد مكان حفظ آمن
- 📊 راقب استخدام الميزة

### للمطورين:
- 🔧 يمكن تخصيص البيانات المشمولة
- 📝 إضافة أنواع بيانات جديدة
- 🔄 تطوير ميزة الاستعادة التلقائية
- 📊 إضافة إحصائيات متقدمة

---

## ✅ الخلاصة

### تم إضافة ميزة النسخة الاحتياطية بنجاح:
- 🎯 **3 طرق للوصول** للميزة
- 📦 **حفظ شامل** لجميع البيانات المهمة
- 🔒 **أمان عالي** وحماية للخصوصية
- 🔄 **سهولة الاستخدام** مع واجهة بديهية
- 📊 **مراقبة وتسجيل** للعمليات
- 🚨 **معالجة الأخطاء** والمشاكل

### 🚀 الميزة جاهزة للاستخدام الفوري!

**💡 نصيحة:** ابدأ بإنشاء أول نسخة احتياطية الآن لضمان حماية بياناتك!

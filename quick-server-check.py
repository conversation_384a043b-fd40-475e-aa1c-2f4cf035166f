#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص سريع لحالة الخادم
Quick Server Status Check
"""

import socket
import requests
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_port(host, port):
    """فحص المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(2)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception:
        return False

def check_http(url):
    """فحص HTTP"""
    try:
        response = requests.get(url, timeout=3)
        return response.status_code == 200
    except Exception:
        return False

def main():
    """فحص سريع للخادم"""
    print("🔍 فحص سريع لحالة الخادم")
    print("=" * 40)
    print(f"⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}")
    
    local_ip = get_local_ip()
    port = 8000
    
    print(f"📍 IP المحلي: {local_ip}")
    print(f"🔌 المنفذ: {port}")
    
    # فحص المنافذ
    localhost_port = check_port('localhost', port)
    network_port = check_port(local_ip, port)
    
    print(f"\n🔍 حالة المنافذ:")
    print(f"   {'✅' if localhost_port else '❌'} localhost:{port}")
    print(f"   {'✅' if network_port else '❌'} {local_ip}:{port}")
    
    # فحص HTTP
    urls = {
        'المحلي': f'http://localhost:{port}',
        'الشبكة': f'http://{local_ip}:{port}',
        'تسجيل الدخول': f'http://{local_ip}:{port}/login-fixed.html',
        'اختبار الشبكة': f'http://{local_ip}:{port}/network-test.html'
    }
    
    print(f"\n🌐 حالة HTTP:")
    for name, url in urls.items():
        status = check_http(url)
        print(f"   {'✅' if status else '❌'} {name}")
    
    # النتيجة النهائية
    if localhost_port and network_port:
        print(f"\n🎉 الخادم يعمل بشكل طبيعي!")
        print(f"🔗 الرابط الرئيسي: http://{local_ip}:{port}")
    elif localhost_port:
        print(f"\n⚠️  الخادم يعمل محلياً فقط")
        print(f"💡 قد تحتاج لإصلاح إعدادات الشبكة")
    else:
        print(f"\n❌ الخادم لا يعمل")
        print(f"💡 يرجى تشغيل الخادم أولاً")
    
    print(f"\n📱 للوصول من الهواتف:")
    print(f"   http://{local_ip}:{port}")

if __name__ == "__main__":
    main()

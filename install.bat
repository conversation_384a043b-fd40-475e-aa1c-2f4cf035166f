@echo off
chcp 65001 >nul
title نظام إدارة أمن المعلومات - التثبيت

echo.
echo 🔐 مرحباً بك في نظام إدارة أمن المعلومات
echo ================================================
echo 🪟 إصدار ويندوز المحسن
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python أولاً من: https://python.org
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ تم العثور على Python: %PYTHON_VERSION%

REM تحديد المنفذ المتاح
set PORT=8000
:check_port
netstat -an | find ":%PORT%" >nul
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ %PORT% مستخدم، جاري البحث عن منفذ آخر...
    set /a PORT+=1
    goto check_port
)

echo 🌐 سيتم تشغيل الخادم على المنفذ: %PORT%

REM إنشاء ملف بدء التشغيل
echo @echo off > start-server.bat
echo chcp 65001 ^>nul >> start-server.bat
echo title نظام إدارة أمن المعلومات - الخادم >> start-server.bat
echo echo 🚀 بدء تشغيل نظام إدارة أمن المعلومات... >> start-server.bat
echo echo 📍 الرابط: http://localhost:%PORT%/login.html >> start-server.bat
echo echo ⏹️  لإيقاف الخادم اضغط Ctrl+C >> start-server.bat
echo echo. >> start-server.bat
echo python start-server.py >> start-server.bat
echo pause >> start-server.bat

REM إنشاء ملف إيقاف التشغيل
echo @echo off > stop-server.bat
echo chcp 65001 ^>nul >> stop-server.bat
echo title إيقاف الخادم >> stop-server.bat
echo echo ⏹️  إيقاف خادم نظام إدارة أمن المعلومات... >> stop-server.bat
echo taskkill /f /im python.exe /fi "WINDOWTITLE eq نظام إدارة أمن المعلومات - الخادم" 2^>nul >> stop-server.bat
echo echo ✅ تم إيقاف الخادم بنجاح >> stop-server.bat
echo timeout /t 3 >> stop-server.bat

REM إنشاء اختصار سطح المكتب
set DESKTOP=%USERPROFILE%\Desktop
set SHORTCUT=%DESKTOP%\نظام أمن المعلومات.lnk

REM إنشاء ملف VBS لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%SHORTCUT%" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\start-server.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> CreateShortcut.vbs
echo oLink.Description = "نظام إدارة أمن المعلومات" >> CreateShortcut.vbs
echo oLink.IconLocation = "%CD%\logo.jpg" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
del CreateShortcut.vbs >nul 2>&1

if exist "%SHORTCUT%" (
    echo 🖥️  تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️  لم يتم إنشاء اختصار سطح المكتب
)

REM إنشاء ملف README للتشغيل
echo # 🚀 كيفية تشغيل نظام إدارة أمن المعلومات > START-HERE.txt
echo. >> START-HERE.txt
echo ## الطريقة الأولى: التشغيل التلقائي >> START-HERE.txt
echo انقر نقراً مزدوجاً على: start-server.bat >> START-HERE.txt
echo. >> START-HERE.txt
echo ## الطريقة الثانية: التشغيل اليدوي >> START-HERE.txt
echo افتح موجه الأوامر واكتب: >> START-HERE.txt
echo python start-server.py >> START-HERE.txt
echo. >> START-HERE.txt
echo ## الوصول للنظام >> START-HERE.txt
echo افتح المتصفح على: http://localhost:%PORT%/login.html >> START-HERE.txt
echo. >> START-HERE.txt
echo ## بيانات تسجيل الدخول >> START-HERE.txt
echo اسم المستخدم: admin >> START-HERE.txt
echo كلمة المرور: admin123 >> START-HERE.txt
echo. >> START-HERE.txt
echo ## إيقاف الخادم >> START-HERE.txt
echo انقر نقراً مزدوجاً على: stop-server.bat >> START-HERE.txt
echo أو اضغط Ctrl+C في نافذة موجه الأوامر >> START-HERE.txt
echo. >> START-HERE.txt
echo ## المتطلبات >> START-HERE.txt
echo - Python 3.x أو أحدث >> START-HERE.txt
echo - متصفح حديث (Chrome, Firefox, Safari, Edge) >> START-HERE.txt
echo - منفذ %PORT% متاح >> START-HERE.txt
echo. >> START-HERE.txt
echo ## الدعم الفني >> START-HERE.txt
echo راجع ملف install-guide.md للمزيد من التفاصيل >> START-HERE.txt

echo.
echo ✅ تم تثبيت النظام بنجاح!
echo.
echo 📋 الملفات المنشأة:
echo    • start-server.bat - لبدء تشغيل الخادم
echo    • stop-server.bat - لإيقاف الخادم
echo    • START-HERE.txt - دليل التشغيل
echo.
echo 🚀 لبدء التشغيل الآن:
echo    انقر نقراً مزدوجاً على start-server.bat
echo.
echo 🌐 ثم افتح المتصفح على:
echo    http://localhost:%PORT%/login.html
echo.
echo 🔑 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

REM إنشاء تطبيق ويندوز
echo 🪟 إنشاء تطبيق ويندوز...
echo @echo off > windows-app.bat
echo chcp 65001 ^>nul >> windows-app.bat
echo title نظام إدارة أمن المعلومات - تطبيق ويندوز >> windows-app.bat
echo python windows-app.py >> windows-app.bat
echo pause >> windows-app.bat

REM سؤال المستخدم عن طريقة التشغيل
echo.
echo 🚀 اختر طريقة التشغيل:
echo    1. تطبيق ويندوز (موصى به)
echo    2. خادم ويب تقليدي
echo    3. لا تشغل الآن
echo.
set /p CHOICE="اختر (1/2/3): "

if "%CHOICE%"=="1" (
    echo 🪟 جاري تشغيل تطبيق ويندوز...
    start windows-app.bat
) else if "%CHOICE%"=="2" (
    echo 🌐 جاري تشغيل الخادم التقليدي...
    start start-server.bat
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul

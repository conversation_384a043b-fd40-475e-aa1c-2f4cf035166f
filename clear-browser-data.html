<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مسح بيانات المتصفح</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 { color: #fff; margin-bottom: 30px; }
        button {
            padding: 15px 30px;
            font-size: 16px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        .btn-info {
            background: #3498db;
            color: white;
        }
        .btn-info:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .warning {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid #e74c3c;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        #result { margin-top: 20px; padding: 15px; border-radius: 8px; }
        .success { background: rgba(39, 174, 96, 0.2); border: 1px solid #27ae60; }
        .error { background: rgba(231, 76, 60, 0.2); border: 1px solid #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 مسح بيانات المتصفح</h1>

        <div class="warning">
            <h3>⚠️ تحذير</h3>
            <p>سيتم مسح جميع البيانات المحفوظة في المتصفح لهذا الموقع</p>
            <p>هذا يشمل: الجلسات، البيانات المحفوظة، والإعدادات</p>
        </div>

        <button class="btn-danger" onclick="clearAllData()">
            🗑️ مسح جميع البيانات
        </button>

        <button class="btn-info" onclick="clearSessionsOnly()">
            🔄 مسح الجلسات فقط
        </button>

        <button class="btn-success" onclick="window.location.href='login.html'">
            🔐 الذهاب لتسجيل الدخول
        </button>

        <button class="btn-info" onclick="window.location.href='login-debug.html'">
            🔍 صفحة التشخيص
        </button>

        <div id="result"></div>
    </div>

    <script>
        function clearAllData() {
            const result = document.getElementById('result');
            try {
                // مسح localStorage
                localStorage.clear();

                // مسح sessionStorage
                sessionStorage.clear();

                // مسح الكوكيز
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });

                result.className = 'success';
                result.innerHTML = '<h3>✅ تم مسح جميع البيانات بنجاح</h3><p>سيتم إعادة تحميل الصفحة...</p>';

                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);

            } catch (e) {
                result.className = 'error';
                result.innerHTML = '<h3>❌ خطأ في مسح البيانات</h3><p>' + e.message + '</p>';
            }
        }

        function clearSessionsOnly() {
            const result = document.getElementById('result');
            try {
                // مسح الجلسات فقط
                localStorage.removeItem('currentSession');
                localStorage.removeItem('userSessions');
                sessionStorage.clear();

                result.className = 'success';
                result.innerHTML = '<h3>✅ تم مسح الجلسات بنجاح</h3><p>يمكنك الآن تسجيل الدخول مرة أخرى</p>';

            } catch (e) {
                result.className = 'error';
                result.innerHTML = '<h3>❌ خطأ في مسح الجلسات</h3><p>' + e.message + '</p>';
            }
        }

        // عرض معلومات البيانات المحفوظة
        window.onload = function() {
            try {
                const sessions = localStorage.getItem('userSessions');
                const currentSession = localStorage.getItem('currentSession');
                const users = localStorage.getItem('systemUsers');

                let info = '<h4>البيانات المحفوظة حالياً:</h4><ul>';
                if (sessions) info += '<li>جلسات المستخدمين ✓</li>';
                if (currentSession) info += '<li>الجلسة الحالية ✓</li>';
                if (users) info += '<li>بيانات المستخدمين ✓</li>';
                info += '</ul>';

                if (!sessions && !currentSession && !users) {
                    info = '<p>لا توجد بيانات محفوظة</p>';
                }

                document.getElementById('result').innerHTML = info;
            } catch (e) {
                console.error('خطأ في عرض المعلومات:', e);
            }
        };
    </script>
</body>
</html>
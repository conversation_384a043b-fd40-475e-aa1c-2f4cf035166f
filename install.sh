#!/bin/bash

# نص تثبيت نظام إدارة أمن المعلومات
# Information Security Management System Installation Script

echo "🔐 مرحباً بك في نظام إدارة أمن المعلومات"
echo "================================================"
echo ""

# التحقق من وجود Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ خطأ: Python غير مثبت على النظام"
    echo "يرجى تثبيت Python أولاً من: https://python.org"
    exit 1
fi

echo "✅ تم العثور على Python: $($PYTHON_CMD --version)"

# التحقق من المنفذ المتاح
PORT=8000
while lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; do
    echo "⚠️  المنفذ $PORT مستخدم، جاري البحث عن منفذ آخر..."
    PORT=$((PORT + 1))
done

echo "🌐 سيتم تشغيل الخادم على المنفذ: $PORT"

# إنشاء ملف بدء التشغيل
cat > start-server.sh << EOF
#!/bin/bash
echo "🚀 بدء تشغيل نظام إدارة أمن المعلومات..."
echo "📍 الرابط: http://localhost:$PORT/login.html"
echo "⏹️  لإيقاف الخادم اضغط Ctrl+C"
echo ""
$PYTHON_CMD start-server.py
EOF

chmod +x start-server.sh

# إنشاء ملف إيقاف التشغيل
cat > stop-server.sh << EOF
#!/bin/bash
echo "⏹️  إيقاف خادم نظام إدارة أمن المعلومات..."
pkill -f "python.*start-server.py" 2>/dev/null
echo "✅ تم إيقاف الخادم بنجاح"
EOF

chmod +x stop-server.sh

# إنشاء ملف تشغيل سريع
cat > quick-start.command << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
./start-server.sh
EOF

chmod +x quick-start.command

# إنشاء اختصار لسطح المكتب (Linux/Mac)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    DESKTOP_FILE="$HOME/Desktop/نظام-أمن-المعلومات.desktop"
    cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=نظام إدارة أمن المعلومات
Comment=Information Security Management System
Exec=$(pwd)/start-server.sh
Icon=$(pwd)/logo.jpg
Terminal=true
Categories=Office;Security;
EOF
    chmod +x "$DESKTOP_FILE"
    echo "🖥️  تم إنشاء اختصار على سطح المكتب"
fi

# إنشاء ملف README للتشغيل
cat > START-HERE.md << EOF
# 🚀 كيفية تشغيل نظام إدارة أمن المعلومات

## الطريقة الأولى: التشغيل التلقائي
\`\`\`bash
./start-server.sh
\`\`\`

## الطريقة الثانية: التشغيل اليدوي
\`\`\`bash
$PYTHON_CMD -m http.server $PORT
\`\`\`

## الوصول للنظام
افتح المتصفح على: http://localhost:$PORT/login.html

## بيانات تسجيل الدخول
- اسم المستخدم: admin
- كلمة المرور: admin123

## إيقاف الخادم
\`\`\`bash
./stop-server.sh
\`\`\`
أو اضغط Ctrl+C في نافذة الطرفية

## المتطلبات
- Python 3.x أو أحدث
- متصفح حديث (Chrome, Firefox, Safari, Edge)
- منفذ $PORT متاح

## الدعم الفني
راجع ملف install-guide.md للمزيد من التفاصيل
EOF

echo ""
echo "✅ تم تثبيت النظام بنجاح!"
echo ""
echo "📋 الملفات المنشأة:"
echo "   • start-server.sh - لبدء تشغيل الخادم"
echo "   • stop-server.sh - لإيقاف الخادم"
echo "   • quick-start.command - تشغيل سريع"
echo "   • START-HERE.md - دليل التشغيل"
echo ""
echo "🚀 لبدء التشغيل الآن:"
echo "   ./start-server.sh"
echo ""
echo "🌐 ثم افتح المتصفح على:"
echo "   http://localhost:$PORT/login.html"
echo ""
echo "🔑 بيانات تسجيل الدخول:"
echo "   اسم المستخدم: admin"
echo "   كلمة المرور: admin123"
echo ""

# سؤال المستخدم إذا كان يريد التشغيل الآن
read -p "هل تريد تشغيل النظام الآن؟ (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 جاري تشغيل النظام..."
    ./start-server.sh
fi

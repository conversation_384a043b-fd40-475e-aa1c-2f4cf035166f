#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوصول للشبكة لنظام إدارة أمن المعلومات
Network Access Test for Information Security Management System
"""

import socket
import subprocess
import platform
import requests
import json
import sys
from urllib.parse import urlparse

class NetworkAccessTester:
    def __init__(self):
        self.server_port = 8000
        self.test_results = {
            'local_access': False,
            'network_interfaces': {},
            'firewall_status': 'unknown',
            'recommendations': []
        }
    
    def print_header(self):
        """طباعة رأس الاختبار"""
        print("=" * 60)
        print("🌐 اختبار الوصول للشبكة - Network Access Test")
        print("   نظام إدارة أمن المعلومات - ISMS")
        print("=" * 60)
        print()
    
    def test_local_access(self):
        """اختبار الوصول المحلي"""
        print("🏠 اختبار الوصول المحلي...")
        
        urls_to_test = [
            f"http://localhost:{self.server_port}",
            f"http://127.0.0.1:{self.server_port}"
        ]
        
        for url in urls_to_test:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code in [200, 301, 302]:
                    print(f"   ✅ {url} - يعمل")
                    self.test_results['local_access'] = True
                else:
                    print(f"   ⚠️  {url} - رمز الاستجابة: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ❌ {url} - خطأ: {str(e)[:50]}...")
        
        print()
    
    def get_network_interfaces(self):
        """الحصول على واجهات الشبكة"""
        print("🔍 فحص واجهات الشبكة...")
        
        interfaces = {}
        
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='utf-8')
                lines = result.stdout.split('\n')
                current_adapter = None
                
                for line in lines:
                    line = line.strip()
                    if 'adapter' in line.lower() or 'محول' in line:
                        current_adapter = line
                    elif 'IPv4' in line or 'عنوان IPv4' in line:
                        if current_adapter and ':' in line:
                            ip = line.split(':')[-1].strip()
                            if ip and ip != '127.0.0.1' and not ip.startswith('169.254'):
                                interfaces[current_adapter] = ip
            else:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                current_interface = None
                
                for line in lines:
                    if line and not line.startswith(' ') and not line.startswith('\t'):
                        current_interface = line.split(':')[0]
                    elif 'inet ' in line and current_interface:
                        parts = line.strip().split()
                        for i, part in enumerate(parts):
                            if part == 'inet' and i + 1 < len(parts):
                                ip = parts[i + 1]
                                if ip != '127.0.0.1' and not ip.startswith('169.254'):
                                    interfaces[current_interface] = ip
                                break
        except Exception as e:
            print(f"   ⚠️  خطأ في الحصول على واجهات الشبكة: {e}")
        
        print(f"   📊 تم العثور على {len(interfaces)} واجهة شبكة")
        for name, ip in interfaces.items():
            print(f"   📍 {name}: {ip}")
        
        print()
        return interfaces
    
    def test_network_access(self, interfaces):
        """اختبار الوصول من الشبكة"""
        print("🌍 اختبار الوصول من الشبكة...")
        
        for name, ip in interfaces.items():
            url = f"http://{ip}:{self.server_port}"
            try:
                response = requests.get(url, timeout=3)
                if response.status_code in [200, 301, 302]:
                    print(f"   ✅ {url} - يعمل")
                    self.test_results['network_interfaces'][ip] = True
                else:
                    print(f"   ⚠️  {url} - رمز الاستجابة: {response.status_code}")
                    self.test_results['network_interfaces'][ip] = False
            except requests.exceptions.RequestException as e:
                print(f"   ❌ {url} - خطأ: {str(e)[:30]}...")
                self.test_results['network_interfaces'][ip] = False
        
        print()
    
    def check_firewall_status(self):
        """التحقق من حالة جدار الحماية"""
        print("🔒 التحقق من جدار الحماية...")
        
        if platform.system() == "Windows":
            try:
                # التحقق من قاعدة جدار الحماية
                result = subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=ISMS Server'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print("   ✅ قاعدة جدار الحماية موجودة")
                    self.test_results['firewall_status'] = 'configured'
                else:
                    print("   ⚠️  قاعدة جدار الحماية غير موجودة")
                    self.test_results['firewall_status'] = 'not_configured'
                    self.test_results['recommendations'].append(
                        "إضافة قاعدة جدار الحماية للمنفذ 8000"
                    )
            except Exception as e:
                print(f"   ⚠️  لا يمكن التحقق من جدار الحماية: {e}")
                self.test_results['firewall_status'] = 'unknown'
        else:
            print("   ℹ️  فحص جدار الحماية متاح على Windows فقط")
        
        print()
    
    def test_port_availability(self):
        """اختبار توفر المنفذ"""
        print(f"🔌 اختبار المنفذ {self.server_port}...")
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', self.server_port))
                if result == 0:
                    print(f"   ✅ المنفذ {self.server_port} مفتوح ويستجيب")
                else:
                    print(f"   ❌ المنفذ {self.server_port} غير متاح")
                    self.test_results['recommendations'].append(
                        "تأكد من تشغيل الخادم أولاً"
                    )
        except Exception as e:
            print(f"   ❌ خطأ في اختبار المنفذ: {e}")
        
        print()
    
    def generate_qr_codes(self, interfaces):
        """إنشاء رموز QR للوصول السريع"""
        print("📱 إنشاء رموز QR للوصول السريع...")
        
        try:
            import qrcode
            
            # إنشاء QR code للرابط الرئيسي
            main_ip = list(interfaces.values())[0] if interfaces else 'localhost'
            main_url = f"http://{main_ip}:{self.server_port}/login.html"
            
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(main_url)
            qr.make(fit=True)
            
            # حفظ كصورة
            img = qr.make_image(fill_color="black", back_color="white")
            img.save("isms-qr-code.png")
            
            print(f"   ✅ تم إنشاء QR code: isms-qr-code.png")
            print(f"   🔗 الرابط: {main_url}")
            
        except ImportError:
            print("   ℹ️  لإنشاء QR codes، ثبت: pip install qrcode[pil]")
        except Exception as e:
            print(f"   ⚠️  خطأ في إنشاء QR code: {e}")
        
        print()
    
    def provide_recommendations(self):
        """تقديم التوصيات"""
        print("💡 التوصيات والحلول:")
        print("-" * 30)
        
        if not self.test_results['local_access']:
            print("❌ الوصول المحلي لا يعمل:")
            print("   • تأكد من تشغيل الخادم")
            print("   • تحقق من المنفذ 8000")
            print("   • أعد تشغيل الخادم")
        
        if not any(self.test_results['network_interfaces'].values()):
            print("❌ الوصول من الشبكة لا يعمل:")
            print("   • تحقق من إعدادات جدار الحماية")
            print("   • تأكد من اتصال الأجهزة بنفس الشبكة")
            print("   • جرب إيقاف جدار الحماية مؤقتاً للاختبار")
        
        if self.test_results['firewall_status'] == 'not_configured':
            print("⚠️  جدار الحماية غير مُعد:")
            print("   • شغل الأمر التالي كمدير:")
            print("   netsh advfirewall firewall add rule name=\"ISMS Server\" dir=in action=allow protocol=TCP localport=8000")
        
        for recommendation in self.test_results['recommendations']:
            print(f"   • {recommendation}")
        
        print()
    
    def create_access_guide(self, interfaces):
        """إنشاء دليل الوصول"""
        print("📄 إنشاء دليل الوصول...")
        
        guide_content = f"""# 🌐 دليل الوصول السريع - Quick Access Guide

## 📊 نتائج الاختبار - Test Results
- الوصول المحلي: {'✅ يعمل' if self.test_results['local_access'] else '❌ لا يعمل'}
- جدار الحماية: {self.test_results['firewall_status']}

## 🔗 روابط الوصول - Access Links

### للوصول من نفس الجهاز:
- http://localhost:{self.server_port}/login.html
- http://127.0.0.1:{self.server_port}/login.html

### للوصول من أجهزة أخرى:
"""
        
        for name, ip in interfaces.items():
            status = "✅ يعمل" if self.test_results['network_interfaces'].get(ip, False) else "❌ لا يعمل"
            guide_content += f"- http://{ip}:{self.server_port}/login.html ({status})\n"
        
        guide_content += f"""
## 🔑 بيانات الدخول - Login Credentials
- اسم المستخدم: admin
- كلمة المرور: admin123

## 📱 للأجهزة المحمولة:
1. تأكد من اتصال الجهاز بنفس شبكة Wi-Fi
2. افتح متصفح الهاتف
3. اكتب أحد الروابط أعلاه
4. استخدم بيانات الدخول

## 🛠️ في حالة عدم العمل:
1. تأكد من تشغيل الخادم
2. تحقق من جدار الحماية
3. تأكد من اتصال الشبكة
4. جرب رابط مختلف من القائمة

## 📞 للدعم الفني:
راجع ملف "دليل-الوصول-متعدد-الأنظمة.md"
"""
        
        try:
            with open('quick-access-guide.md', 'w', encoding='utf-8') as f:
                f.write(guide_content)
            print("   ✅ تم إنشاء دليل الوصول السريع: quick-access-guide.md")
        except Exception as e:
            print(f"   ⚠️  خطأ في إنشاء الدليل: {e}")
        
        print()
    
    def run_test(self):
        """تشغيل جميع الاختبارات"""
        self.print_header()
        
        # اختبار الوصول المحلي
        self.test_local_access()
        
        # اختبار المنفذ
        self.test_port_availability()
        
        # فحص واجهات الشبكة
        interfaces = self.get_network_interfaces()
        
        # اختبار الوصول من الشبكة
        if interfaces:
            self.test_network_access(interfaces)
        
        # فحص جدار الحماية
        self.check_firewall_status()
        
        # إنشاء QR codes
        if interfaces:
            self.generate_qr_codes(interfaces)
        
        # إنشاء دليل الوصول
        if interfaces:
            self.create_access_guide(interfaces)
        
        # تقديم التوصيات
        self.provide_recommendations()
        
        print("=" * 60)
        print("🎉 انتهى الاختبار!")
        print("📄 راجع الملفات المُنشأة للحصول على معلومات إضافية")
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    try:
        tester = NetworkAccessTester()
        tester.run_test()
    except KeyboardInterrupt:
        print("\n\n⚠️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")

if __name__ == "__main__":
    main()

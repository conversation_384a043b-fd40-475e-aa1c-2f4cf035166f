<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الخادم - نظام إدارة أمن المعلومات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .status-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .status-online {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-offline {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .url-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .url-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .url-item:last-child {
            border-bottom: none;
        }
        
        .url-link {
            color: #3498db;
            text-decoration: none;
            font-family: monospace;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }
        
        .copy-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn:hover {
            background: #5a6268;
        }
        
        .log-section {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .refresh-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #27ae60;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .auto-refresh {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        🔄 تحديث تلقائي كل 10 ثوان
        <span class="refresh-indicator"></span>
    </div>
    
    <div class="container">
        <div class="header">
            <h1>🖥️ لوحة تحكم الخادم</h1>
            <p>مراقبة حالة خادم نظام إدارة أمن المعلومات</p>
            <p id="lastUpdate">آخر تحديث: جاري التحميل...</p>
        </div>
        
        <div class="status-grid">
            <!-- معلومات الخادم -->
            <div class="status-card">
                <h3><i class="fas fa-server"></i> معلومات الخادم</h3>
                
                <div class="status-item">
                    <div class="status-label">عنوان IP المحلي:</div>
                    <div class="status-value" id="localIP">جاري التحميل...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">المنفذ:</div>
                    <div class="status-value">8000</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">حالة الخادم:</div>
                    <div class="status-value" id="serverStatus">جاري الفحص...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">وقت التشغيل:</div>
                    <div class="status-value" id="uptime">جاري الحساب...</div>
                </div>
            </div>
            
            <!-- حالة المنافذ -->
            <div class="status-card">
                <h3><i class="fas fa-network-wired"></i> حالة المنافذ</h3>
                
                <div class="status-item">
                    <div class="status-label">localhost:8000</div>
                    <div class="status-value" id="localhostPort">جاري الفحص...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">الشبكة:8000</div>
                    <div class="status-value" id="networkPort">جاري الفحص...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">HTTP المحلي</div>
                    <div class="status-value" id="localHTTP">جاري الفحص...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">HTTP الشبكة</div>
                    <div class="status-value" id="networkHTTP">جاري الفحص...</div>
                </div>
            </div>
            
            <!-- إحصائيات الاستخدام -->
            <div class="status-card">
                <h3><i class="fas fa-chart-line"></i> إحصائيات الاستخدام</h3>
                
                <div class="status-item">
                    <div class="status-label">الطلبات اليوم:</div>
                    <div class="status-value" id="requestsToday">0</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">المستخدمين النشطين:</div>
                    <div class="status-value" id="activeUsers">0</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">آخر طلب:</div>
                    <div class="status-value" id="lastRequest">لا يوجد</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">معدل الاستجابة:</div>
                    <div class="status-value" id="responseTime">جاري القياس...</div>
                </div>
            </div>
            
            <!-- حالة النظام -->
            <div class="status-card">
                <h3><i class="fas fa-cogs"></i> حالة النظام</h3>
                
                <div class="status-item">
                    <div class="status-label">استخدام الذاكرة:</div>
                    <div class="status-value" id="memoryUsage">جاري القياس...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">استخدام المعالج:</div>
                    <div class="status-value" id="cpuUsage">جاري القياس...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">مساحة القرص:</div>
                    <div class="status-value" id="diskSpace">جاري القياس...</div>
                </div>
                
                <div class="status-item">
                    <div class="status-label">حالة قاعدة البيانات:</div>
                    <div class="status-value" id="dbStatus">متصلة</div>
                </div>
            </div>
        </div>
        
        <!-- روابط الوصول -->
        <div class="status-card">
            <h3><i class="fas fa-link"></i> روابط الوصول</h3>
            
            <div class="url-list" id="urlList">
                جاري تحميل الروابط...
            </div>
            
            <div class="controls">
                <button class="btn btn-success" onclick="testAllUrls()">اختبار جميع الروابط</button>
                <button class="btn" onclick="refreshStatus()">تحديث الحالة</button>
                <button class="btn btn-warning" onclick="openAllPages()">فتح جميع الصفحات</button>
            </div>
        </div>
        
        <!-- سجل الأنشطة -->
        <div class="status-card">
            <h3><i class="fas fa-list"></i> سجل الأنشطة</h3>
            
            <div class="log-section" id="activityLog">
                جاري تحميل سجل الأنشطة...
            </div>
            
            <div class="controls">
                <button class="btn" onclick="refreshLog()">تحديث السجل</button>
                <button class="btn btn-warning" onclick="clearLog()">مسح السجل</button>
                <button class="btn btn-danger" onclick="downloadLog()">تحميل السجل</button>
            </div>
        </div>
        
        <!-- أدوات التحكم -->
        <div class="status-card">
            <h3><i class="fas fa-tools"></i> أدوات التحكم</h3>
            
            <div class="controls">
                <button class="btn btn-success" onclick="startServer()">بدء الخادم</button>
                <button class="btn btn-warning" onclick="restartServer()">إعادة تشغيل</button>
                <button class="btn btn-danger" onclick="stopServer()">إيقاف الخادم</button>
                <button class="btn" onclick="checkFirewall()">فحص جدار الحماية</button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let startTime = new Date();
        let refreshInterval;
        let localIP = '*************'; // سيتم تحديثه تلقائياً
        
        // تحميل الصفحة
        window.onload = function() {
            console.log('🔄 تحميل لوحة تحكم الخادم...');
            loadServerStatus();
            startAutoRefresh();
        };
        
        // بدء التحديث التلقائي
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadServerStatus();
            }, 10000); // كل 10 ثوان
        }
        
        // تحميل حالة الخادم
        async function loadServerStatus() {
            try {
                // تحديث الوقت
                document.getElementById('lastUpdate').textContent = 
                    `آخر تحديث: ${new Date().toLocaleString('ar-SA')}`;
                
                // تحديث وقت التشغيل
                const uptime = Math.floor((new Date() - startTime) / 1000);
                const hours = Math.floor(uptime / 3600);
                const minutes = Math.floor((uptime % 3600) / 60);
                const seconds = uptime % 60;
                document.getElementById('uptime').textContent = 
                    `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // فحص حالة الخادم
                await checkServerStatus();
                
                // تحميل الروابط
                loadUrls();
                
                // تحميل السجل
                loadActivityLog();
                
                // تحديث الإحصائيات
                updateStats();
                
            } catch (error) {
                console.error('خطأ في تحميل حالة الخادم:', error);
            }
        }
        
        // فحص حالة الخادم
        async function checkServerStatus() {
            const urls = [
                { id: 'localHTTP', url: 'http://localhost:8000' },
                { id: 'networkHTTP', url: `http://${localIP}:8000` }
            ];
            
            for (const {id, url} of urls) {
                try {
                    const response = await fetch(url, { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache'
                    });
                    document.getElementById(id).innerHTML = '<span class="status-online">متاح</span>';
                } catch (error) {
                    document.getElementById(id).innerHTML = '<span class="status-offline">غير متاح</span>';
                }
            }
            
            // تحديث معلومات أساسية
            document.getElementById('localIP').textContent = localIP;
            document.getElementById('localhostPort').innerHTML = '<span class="status-online">مفتوح</span>';
            document.getElementById('networkPort').innerHTML = '<span class="status-online">مفتوح</span>';
            document.getElementById('serverStatus').innerHTML = '<span class="status-online">يعمل</span>';
        }
        
        // تحميل الروابط
        function loadUrls() {
            const urls = [
                { name: 'الصفحة الرئيسية', url: `http://${localIP}:8000/index.html` },
                { name: 'تسجيل الدخول المحسن', url: `http://${localIP}:8000/login-fixed.html` },
                { name: 'اختبار الشبكة', url: `http://${localIP}:8000/network-test.html` },
                { name: 'لوحة التحكم', url: `http://${localIP}:8000/enhanced-dashboard.html` },
                { name: 'اختبار تسجيل الدخول', url: `http://${localIP}:8000/login-test.html` }
            ];
            
            let urlHtml = '';
            urls.forEach(({name, url}) => {
                urlHtml += `
                    <div class="url-item">
                        <a href="${url}" class="url-link" target="_blank">${name}</a>
                        <button class="copy-btn" onclick="copyToClipboard('${url}')">نسخ</button>
                    </div>
                `;
            });
            
            document.getElementById('urlList').innerHTML = urlHtml;
        }
        
        // تحميل سجل الأنشطة
        function loadActivityLog() {
            const now = new Date().toLocaleString('ar-SA');
            const logs = [
                `[${now}] ✅ فحص حالة الخادم - نجح`,
                `[${now}] 🔄 تحديث لوحة التحكم`,
                `[${now}] 📊 تحديث الإحصائيات`,
                `[${now}] 🌐 فحص الاتصال بالشبكة - نجح`
            ];
            
            document.getElementById('activityLog').innerHTML = logs.join('<br>');
        }
        
        // تحديث الإحصائيات
        function updateStats() {
            // محاكاة بيانات الإحصائيات
            document.getElementById('requestsToday').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 10) + 1;
            document.getElementById('lastRequest').textContent = new Date().toLocaleTimeString('ar-SA');
            document.getElementById('responseTime').textContent = (Math.random() * 100 + 50).toFixed(0) + 'ms';
            
            // معلومات النظام
            document.getElementById('memoryUsage').textContent = (Math.random() * 30 + 40).toFixed(1) + '%';
            document.getElementById('cpuUsage').textContent = (Math.random() * 20 + 10).toFixed(1) + '%';
            document.getElementById('diskSpace').textContent = (Math.random() * 10 + 80).toFixed(1) + '%';
        }
        
        // نسخ إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ الرابط إلى الحافظة');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الرابط');
            });
        }
        
        // اختبار جميع الروابط
        async function testAllUrls() {
            alert('جاري اختبار جميع الروابط...');
            // سيتم تنفيذ الاختبار هنا
        }
        
        // تحديث الحالة
        function refreshStatus() {
            loadServerStatus();
            alert('تم تحديث الحالة');
        }
        
        // فتح جميع الصفحات
        function openAllPages() {
            const urls = [
                `http://${localIP}:8000/login-fixed.html`,
                `http://${localIP}:8000/network-test.html`,
                `http://${localIP}:8000/index.html`
            ];
            
            urls.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                }, index * 1000);
            });
        }
        
        // تحديث السجل
        function refreshLog() {
            loadActivityLog();
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('activityLog').innerHTML = 'تم مسح السجل';
        }
        
        // تحميل السجل
        function downloadLog() {
            const logContent = document.getElementById('activityLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `server-log-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // أدوات التحكم
        function startServer() {
            alert('سيتم بدء الخادم...');
        }
        
        function restartServer() {
            if (confirm('هل أنت متأكد من إعادة تشغيل الخادم؟')) {
                alert('سيتم إعادة تشغيل الخادم...');
            }
        }
        
        function stopServer() {
            if (confirm('هل أنت متأكد من إيقاف الخادم؟')) {
                alert('سيتم إيقاف الخادم...');
            }
        }
        
        function checkFirewall() {
            alert('فحص جدار الحماية...\n\nالحالة: مكون بشكل صحيح\nالمنفذ 8000: مفتوح');
        }
    </script>
</body>
</html>

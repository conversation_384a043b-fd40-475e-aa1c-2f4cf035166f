# 🔄 دليل نظام المزامنة المركزي

## نظرة عامة
تم تطوير نظام مزامنة مركزي متقدم لنظام إدارة أمن المعلومات يسمح بمشاركة البيانات والحركات بين جميع المستخدمين عبر الشبكة في الوقت الفعلي.

## 🎯 الميزات الرئيسية

### ✅ المزامنة التلقائية
- مزامنة تلقائية كل 30 ثانية
- مزامنة فورية عند تغيير البيانات
- مزامنة عند العودة للاتصال بالإنترنت

### ✅ مشاركة البيانات
- **المستخدمين:** مشاركة حسابات المستخدمين وصلاحياتهم
- **الأحداث الأمنية:** مزامنة جميع الأحداث المسجلة
- **الأنشطة:** تتبع جميع أنشطة المستخدمين
- **الحركات:** مراقبة جميع العمليات في النظام

### ✅ النسخ الاحتياطي
- نسخ احتياطي تلقائي كل ساعة
- الاحتفاظ بآخر 24 نسخة احتياطية
- استرداد البيانات عند الحاجة

## 🚀 طرق التشغيل

### 1. التشغيل الكامل (مستحسن)
```bash
# تشغيل النظام الكامل مع المزامنة
python start-complete-system.py
```

### 2. التشغيل المنفصل
```bash
# تشغيل الخادم الرئيسي
python start-server.py

# تشغيل خادم المزامنة (في terminal منفصل)
python data-sync-server.py
```

### 3. التشغيل البسيط
```bash
# تشغيل الخادم الأساسي فقط (بدون مزامنة)
python simple-server.py
```

## 🌐 معلومات الخوادم

### الخادم الرئيسي
- **المنفذ:** 8000
- **الوصول:** http://localhost:8000
- **الوظيفة:** واجهة المستخدم والتطبيق الرئيسي

### خادم المزامنة
- **المنفذ:** 8001
- **الوصول:** http://localhost:8001
- **الوظيفة:** API المزامنة وتخزين البيانات المركزي

## 📡 نقاط النهاية (API Endpoints)

### معلومات الخادم
```
GET /api/sync/status
```
عرض حالة خادم المزامنة وإحصائيات البيانات

### المستخدمين
```
GET  /api/sync/users     - جلب جميع المستخدمين
POST /api/sync/users     - مزامنة المستخدمين
```

### الأحداث الأمنية
```
GET  /api/sync/events    - جلب جميع الأحداث
POST /api/sync/events    - مزامنة الأحداث
```

### الأنشطة
```
GET  /api/sync/activities - جلب جميع الأنشطة
POST /api/sync/activities - مزامنة الأنشطة
```

### المزامنة الشاملة
```
GET  /api/sync/all       - جلب جميع البيانات
POST /api/sync/full      - مزامنة شاملة لجميع البيانات
```

## 🔧 كيفية عمل النظام

### من جانب العميل (Client-side)
1. **تتبع التغييرات:** يتم تتبع جميع التغييرات محلياً
2. **الإرسال التلقائي:** إرسال التغييرات لخادم المزامنة
3. **الاستلام التلقائي:** استلام التحديثات من الخادم
4. **التحديث المحلي:** تحديث البيانات المحلية

### من جانب الخادم (Server-side)
1. **استلام البيانات:** استلام البيانات من العملاء
2. **دمج البيانات:** دمج البيانات الجديدة مع الموجودة
3. **التخزين:** حفظ البيانات في ملفات JSON
4. **التوزيع:** إرسال البيانات المحدثة للعملاء

## 📊 مراقبة النظام

### مؤشر الحالة
- **أخضر:** متصل ومتزامن
- **أزرق:** جاري المزامنة
- **أصفر:** غير متصل (وضع محلي)
- **أحمر:** خطأ في المزامنة

### إحصائيات المزامنة
- عدد المستخدمين المتزامنين
- عدد الأحداث المتزامنة
- عدد الأنشطة المتزامنة
- وقت آخر مزامنة

## 🛠️ الاختبار والتشخيص

### اختبار النظام الكامل
```bash
# اختبار جميع وظائف المزامنة
python test-data-sync.py
```

### اختبار الاتصال
```bash
# اختبار الاتصال بخادم المزامنة
curl http://localhost:8001/api/sync/status
```

### اختبار تسجيل الدخول
```bash
# اختبار النظام الأساسي
python test-login.py
```

## 📁 هيكل البيانات

### مجلد البيانات
```
data/
├── users.json          # بيانات المستخدمين
├── events.json         # الأحداث الأمنية
├── activities.json     # أنشطة المستخدمين
├── sync_log.json       # سجل المزامنة
└── backups/            # النسخ الاحتياطية
    ├── backup_20241210_120000.json
    ├── backup_20241210_130000.json
    └── ...
```

### تنسيق البيانات

#### المستخدمين
```json
{
  "username": {
    "fullName": "الاسم الكامل",
    "email": "<EMAIL>",
    "role": "admin|analyst|operator",
    "isActive": true,
    "lastModified": 1702123456789
  }
}
```

#### الأحداث
```json
[
  {
    "id": "unique-id",
    "serial": "SEC-001",
    "title": "عنوان الحدث",
    "type": "نوع الحدث",
    "severity": "low|medium|high|critical",
    "timestamp": "2024-12-10T12:00:00Z",
    "status": "open|closed|investigating"
  }
]
```

#### الأنشطة
```json
[
  {
    "id": "unique-id",
    "userId": "user-id",
    "username": "اسم المستخدم",
    "action": "نوع العملية",
    "description": "وصف العملية",
    "timestamp": "2024-12-10T12:00:00Z",
    "ip": "*************"
  }
]
```

## 🔒 الأمان والخصوصية

### حماية البيانات
- تشفير الاتصالات عبر HTTPS (عند التفعيل)
- التحقق من صحة البيانات
- حماية من هجمات CORS

### التحكم في الوصول
- مصادقة المستخدمين
- تتبع جميع العمليات
- سجلات مفصلة للأنشطة

### النسخ الاحتياطي
- نسخ احتياطي تلقائي منتظم
- تشفير النسخ الاحتياطية
- استرداد سريع عند الحاجة

## 🌍 الوصول من الشبكة

### إعداد الشبكة
1. **تأكد من تشغيل النظام** على الجهاز المضيف
2. **اكتشف عنوان IP** للجهاز المضيف
3. **اضبط جدار الحماية** للسماح بالمنافذ 8000 و 8001
4. **شارك الروابط** مع المستخدمين الآخرين

### روابط الوصول
- **الخادم الرئيسي:** `http://[IP]:8000`
- **خادم المزامنة:** `http://[IP]:8001`

### مثال للوصول
إذا كان عنوان IP هو `*************`:
- **التطبيق:** http://*************:8000
- **المزامنة:** http://*************:8001

## 🆘 استكشاف الأخطاء

### مشاكل شائعة

#### خادم المزامنة لا يعمل
```bash
# تحقق من تشغيل الخادم
python data-sync-server.py

# أو شغل النظام الكامل
python start-complete-system.py
```

#### البيانات لا تتزامن
1. تحقق من الاتصال بالإنترنت
2. تحقق من تشغيل خادم المزامنة
3. امسح cache المتصفح
4. أعد تحميل الصفحة

#### خطأ في الصلاحيات
```bash
# تأكد من صلاحيات الكتابة في مجلد data
chmod 755 data/
```

### أدوات التشخيص
```bash
# فحص شامل للنظام
python verify-installation.py

# اختبار المزامنة
python test-data-sync.py

# اختبار الشبكة
python test-network-access.py
```

## 📞 الدعم الفني

### الحصول على المساعدة
1. راجع ملفات السجل في مجلد `data/`
2. شغل أدوات التشخيص المتاحة
3. تحقق من Developer Tools في المتصفح
4. تواصل مع فريق الدعم الفني

### معلومات مفيدة للدعم
- نظام التشغيل والإصدار
- إصدار Python
- رسائل الخطأ الدقيقة
- لقطات شاشة للمشكلة

---

## 🎉 الخلاصة

نظام المزامنة المركزي يوفر:
- **مشاركة فورية** للبيانات والحركات
- **تزامن تلقائي** بين جميع المستخدمين
- **نسخ احتياطي آمن** للبيانات
- **مراقبة شاملة** للأنشطة
- **وصول عبر الشبكة** من أي جهاز

النظام الآن جاهز للاستخدام في بيئة متعددة المستخدمين! 🚀

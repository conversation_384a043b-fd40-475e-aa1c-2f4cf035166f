<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأنشطة - نظام إدارة أمن المعلومات</title>
    <style>
        body { font-family: 'Cairo', Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .filters { display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; gap: 5px; }
        .filter-group label { font-weight: 500; color: #2c3e50; }
        .filter-group input, .filter-group select { padding: 8px; border: 2px solid #ddd; border-radius: 5px; }
        .logs-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #eee; }
        th { background: #3498db; color: white; font-weight: 600; }
        .activity-type { padding: 4px 8px; border-radius: 15px; font-size: 0.9em; color: white; }
        .type-login { background: #27ae60; }
        .type-logout { background: #e74c3c; }
        .type-user { background: #f39c12; }
        .type-event { background: #9b59b6; }
        .type-system { background: #34495e; }
        .pagination { display: flex; justify-content: center; gap: 10px; margin-top: 20px; }
        .page-btn { padding: 8px 12px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 5px; }
        .page-btn.active { background: #3498db; color: white; }
    </style>
    <script src="fixed-auth-system.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 سجل الأنشطة</h1>
            <p>عرض شامل لجميع أنشطة النظام والمستخدمين</p>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label>نوع النشاط:</label>
                <select id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="login">تسجيل دخول</option>
                    <option value="logout">تسجيل خروج</option>
                    <option value="user">إدارة مستخدمين</option>
                    <option value="event">الأحداث الأمنية</option>
                    <option value="system">النظام</option>
                </select>
            </div>
            <div class="filter-group">
                <label>المستخدم:</label>
                <select id="userFilter">
                    <option value="">جميع المستخدمين</option>
                </select>
            </div>
            <div class="filter-group">
                <label>من تاريخ:</label>
                <input type="date" id="fromDate">
            </div>
            <div class="filter-group">
                <label>إلى تاريخ:</label>
                <input type="date" id="toDate">
            </div>
            <div class="filter-group">
                <label>البحث:</label>
                <input type="text" id="searchInput" placeholder="ابحث في الوصف...">
            </div>
        </div>

        <div class="logs-table">
            <table>
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>نوع النشاط</th>
                        <th>المستخدم</th>
                        <th>الوصف</th>
                        <th>التفاصيل</th>
                    </tr>
                </thead>
                <tbody id="logsTableBody">
                    <tr><td colspan="5" style="text-align: center; padding: 40px;">جاري تحميل السجلات...</td></tr>
                </tbody>
            </table>
        </div>

        <div class="pagination" id="pagination"></div>
    </div>

    <script>
        let allLogs = [];
        let filteredLogs = [];
        let currentPage = 1;
        const logsPerPage = 50;

        window.onload = function() {
            loadLogs();
            setupFilters();
        };

        function loadLogs() {
            try {
                allLogs = JSON.parse(localStorage.getItem('activityLog')) || [];
                filteredLogs = [...allLogs];
                populateUserFilter();
                displayLogs();
            } catch (error) {
                console.error('خطأ في تحميل السجلات:', error);
            }
        }

        function populateUserFilter() {
            const userFilter = document.getElementById('userFilter');
            const users = [...new Set(allLogs.map(log => log.username))];

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user;
                option.textContent = user;
                userFilter.appendChild(option);
            });
        }

        function setupFilters() {
            ['typeFilter', 'userFilter', 'fromDate', 'toDate', 'searchInput'].forEach(id => {
                document.getElementById(id).addEventListener('change', applyFilters);
                document.getElementById(id).addEventListener('input', applyFilters);
            });
        }

        function applyFilters() {
            const typeFilter = document.getElementById('typeFilter').value;
            const userFilter = document.getElementById('userFilter').value;
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();

            filteredLogs = allLogs.filter(log => {
                if (typeFilter && !log.type.includes(typeFilter)) return false;
                if (userFilter && log.username !== userFilter) return false;
                if (fromDate && new Date(log.timestamp) < new Date(fromDate)) return false;
                if (toDate && new Date(log.timestamp) > new Date(toDate + 'T23:59:59')) return false;
                if (searchInput && !log.description.toLowerCase().includes(searchInput)) return false;
                return true;
            });

            currentPage = 1;
            displayLogs();
        }

        function displayLogs() {
            const tbody = document.getElementById('logsTableBody');
            const startIndex = (currentPage - 1) * logsPerPage;
            const endIndex = startIndex + logsPerPage;
            const pageData = filteredLogs.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px;">لا توجد سجلات تطابق المعايير المحددة</td></tr>';
                return;
            }

            tbody.innerHTML = pageData.map(log => {
                const date = new Date(log.timestamp).toLocaleString('ar-SA');
                const typeClass = getTypeClass(log.type);
                const typeLabel = getTypeLabel(log.type);

                return `
                    <tr>
                        <td>${date}</td>
                        <td><span class="activity-type ${typeClass}">${typeLabel}</span></td>
                        <td>${log.username}</td>
                        <td>${log.description}</td>
                        <td>${JSON.stringify(log.details || {})}</td>
                    </tr>
                `;
            }).join('');

            updatePagination();
        }

        function getTypeClass(type) {
            if (type.includes('login')) return 'type-login';
            if (type.includes('logout')) return 'type-logout';
            if (type.includes('user')) return 'type-user';
            if (type.includes('event')) return 'type-event';
            return 'type-system';
        }

        function getTypeLabel(type) {
            const labels = {
                'login': 'تسجيل دخول',
                'logout': 'تسجيل خروج',
                'user_created': 'إضافة مستخدم',
                'user_updated': 'تحديث مستخدم',
                'user_deleted': 'حذف مستخدم',
                'event_created': 'إضافة حدث',
                'event_updated': 'تحديث حدث',
                'event_deleted': 'حذف حدث'
            };
            return labels[type] || type;
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredLogs.length / logsPerPage);

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            pagination.innerHTML = paginationHTML;
        }

        function goToPage(page) {
            currentPage = page;
            displayLogs();
        }
    </script>
</body>
</html>
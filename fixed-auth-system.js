
// Fixed Authentication System - نظام مصادقة محسن
// يحل جميع مشاكل تسجيل الدخول

class FixedAuthManager {
    constructor() {
        this.users = {};
        this.currentUser = null;
        this.userSessions = {};
        this.isInitialized = false;
        this.initPromise = this.init();
    }
    
    async init() {
        try {
            console.log('🔧 بدء تهيئة نظام المصادقة المحسن...');
            
            // تحميل المستخدمين
            await this.loadUsers();
            
            // تحميل الجلسات
            await this.loadSessions();
            
            // فحص الجلسة الحالية
            await this.checkCurrentSession();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
            this.createEmergencyUser();
            this.isInitialized = true;
            return false;
        }
    }
    
    // انتظار التهيئة
    async waitForInit() {
        if (!this.isInitialized) {
            await this.initPromise;
        }
        return this.isInitialized;
    }
    
    // تحميل المستخدمين
    async loadUsers() {
        try {
            const storedUsers = localStorage.getItem('systemUsers');
            if (storedUsers) {
                this.users = JSON.parse(storedUsers);
                console.log('📥 تم تحميل المستخدمين من localStorage');
            } else {
                console.log('⚠️ لا توجد بيانات مستخدمين، إنشاء المستخدمين الافتراضيين...');
                await this.createDefaultUsers();
            }
            
            // التحقق من وجود مستخدمين
            if (Object.keys(this.users).length === 0) {
                throw new Error('لا يوجد مستخدمين في النظام');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
            await this.createDefaultUsers();
        }
    }
    
    // إنشاء المستخدمين الافتراضيين
    async createDefaultUsers() {
        console.log('🔨 إنشاء المستخدمين الافتراضيين...');
        
        this.users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": this.hashPassword("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": this.hashPassword("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": this.hashPassword("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null,
                "loginAttempts": 0,
                "lastLoginAttempt": null
            }
        };
        
        await this.saveUsers();
        console.log('✅ تم إنشاء المستخدمين الافتراضيين');
    }
    
    // إنشاء مستخدم طوارئ
    createEmergencyUser() {
        console.log('🚨 إنشاء مستخدم طوارئ...');
        
        const emergencyUser = {
            "id": "emergency",
            "username": "emergency",
            "password": this.hashPassword("emergency123"),
            "fullName": "مستخدم طوارئ",
            "email": "<EMAIL>",
            "role": "admin",
            "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
            "isActive": true,
            "createdAt": new Date().toISOString(),
            "lastLogin": null,
            "loginAttempts": 0,
            "lastLoginAttempt": null
        };
        
        this.users = { "emergency": emergencyUser };
        this.saveUsers();
        
        console.log('✅ تم إنشاء مستخدم طوارئ: emergency / emergency123');
    }
    
    // حفظ المستخدمين
    async saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
            console.log('💾 تم حفظ بيانات المستخدمين');
        } catch (error) {
            console.error('خطأ في حفظ المستخدمين:', error);
            throw error;
        }
    }
    
    // تحميل الجلسات
    async loadSessions() {
        try {
            const storedSessions = localStorage.getItem('userSessions');
            if (storedSessions) {
                this.userSessions = JSON.parse(storedSessions);
                console.log('📥 تم تحميل الجلسات من localStorage');
            } else {
                this.userSessions = {};
                console.log('📝 تم إنشاء مخزن جلسات جديد');
            }
        } catch (error) {
            console.error('خطأ في تحميل الجلسات:', error);
            this.userSessions = {};
        }
    }
    
    // حفظ الجلسات
    async saveSessions() {
        try {
            localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
            console.log('💾 تم حفظ بيانات الجلسات');
        } catch (error) {
            console.error('خطأ في حفظ الجلسات:', error);
        }
    }
    
    // تشفير كلمة المرور
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }
    
    // فحص الجلسة الحالية
    async checkCurrentSession() {
        try {
            const sessionId = localStorage.getItem('currentSession');
            if (!sessionId) {
                console.log('ℹ️ لا توجد جلسة حالية');
                return false;
            }
            
            const session = this.userSessions[sessionId];
            if (!session) {
                console.log('⚠️ جلسة غير صالحة، مسح الجلسة...');
                localStorage.removeItem('currentSession');
                return false;
            }
            
            // فحص انتهاء الصلاحية
            const now = new Date();
            const expiresAt = new Date(session.expiresAt);
            
            if (now > expiresAt) {
                console.log('⏰ انتهت صلاحية الجلسة، مسح الجلسة...');
                await this.clearSession(sessionId);
                return false;
            }
            
            // التحقق من وجود المستخدم
            const user = this.users[session.username];
            if (!user || !user.isActive) {
                console.log('❌ المستخدم غير موجود أو معطل، مسح الجلسة...');
                await this.clearSession(sessionId);
                return false;
            }
            
            // الجلسة صالحة
            this.currentUser = user;
            console.log('✅ جلسة صالحة للمستخدم:', user.username);
            
            // إذا كنا في صفحة تسجيل الدخول، انتقل للصفحة الرئيسية
            if (this.isLoginPage()) {
                console.log('🔄 انتقال للصفحة الرئيسية...');
                window.location.href = 'index.html';
            }
            
            return true;
            
        } catch (error) {
            console.error('خطأ في فحص الجلسة:', error);
            return false;
        }
    }
    
    // فحص إذا كنا في صفحة تسجيل الدخول
    isLoginPage() {
        const path = window.location.pathname;
        return path.includes('login.html') || 
               path.includes('instant-login.html') || 
               path.includes('login-flow-manager.html');
    }
    
    // تسجيل الدخول
    async login(username, password, rememberMe = false) {
        try {
            console.log('🔐 محاولة تسجيل دخول للمستخدم:', username);
            
            // انتظار التهيئة
            await this.waitForInit();
            
            // التحقق من البيانات
            if (!username || !password) {
                throw new Error('يرجى إدخال اسم المستخدم وكلمة المرور');
            }
            
            // البحث عن المستخدم
            const user = this.users[username.toLowerCase()];
            if (!user) {
                console.log('❌ المستخدم غير موجود:', username);
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيح');
            }
            
            // فحص حالة الحساب
            if (!user.isActive) {
                console.log('❌ الحساب معطل:', username);
                throw new Error('هذا الحساب معطل');
            }
            
            // فحص محاولات تسجيل الدخول
            if (user.loginAttempts >= 5) {
                const lastAttempt = user.lastLoginAttempt ? new Date(user.lastLoginAttempt) : new Date(0);
                const now = new Date();
                const timeDiff = (now - lastAttempt) / (1000 * 60); // بالدقائق
                
                if (timeDiff < 15) {
                    throw new Error('تم حظر الحساب مؤقتاً. حاول مرة أخرى بعد 15 دقيقة');
                } else {
                    // إعادة تعيين محاولات تسجيل الدخول
                    user.loginAttempts = 0;
                    await this.saveUsers();
                }
            }
            
            // التحقق من كلمة المرور
            const hashedPassword = this.hashPassword(password);
            if (user.password !== hashedPassword) {
                console.log('❌ كلمة مرور خاطئة للمستخدم:', username);
                
                // زيادة محاولات تسجيل الدخول الفاشلة
                user.loginAttempts = (user.loginAttempts || 0) + 1;
                user.lastLoginAttempt = new Date().toISOString();
                await this.saveUsers();
                
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيح');
            }
            
            // تسجيل دخول ناجح
            console.log('✅ تسجيل دخول ناجح للمستخدم:', username);
            
            // إعادة تعيين محاولات تسجيل الدخول
            user.loginAttempts = 0;
            user.lastLogin = new Date().toISOString();
            user.lastLoginAttempt = new Date().toISOString();
            
            // إنشاء جلسة جديدة
            const sessionId = this.generateSessionId();
            const session = {
                sessionId: sessionId,
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: rememberMe ? 
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : 
                    new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                rememberMe: rememberMe,
                ipAddress: 'localhost',
                userAgent: navigator.userAgent
            };
            
            // حفظ الجلسة
            this.userSessions[sessionId] = session;
            await this.saveSessions();
            localStorage.setItem('currentSession', sessionId);
            
            // تحديث المستخدم
            this.users[username.toLowerCase()] = user;
            await this.saveUsers();
            this.currentUser = user;
            
            // تسجيل النشاط
            this.logActivity('login', 'تسجيل دخول ناجح', {
                username: user.username,
                sessionId: sessionId,
                rememberMe: rememberMe
            });
            
            console.log('🎉 تم إنشاء الجلسة بنجاح:', sessionId);
            
            // الانتقال للصفحة الرئيسية
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 100);
            
            return { success: true, user: user, session: session };
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            
            // تسجيل محاولة فاشلة
            this.logActivity('login_failed', 'محاولة تسجيل دخول فاشلة', {
                username: username,
                error: error.message
            });
            
            throw error;
        }
    }
    
    // تسجيل الخروج
    async logout() {
        try {
            console.log('🚪 بدء عملية تسجيل الخروج...');
            
            const sessionId = localStorage.getItem('currentSession');
            
            if (sessionId && this.userSessions[sessionId]) {
                // تسجيل النشاط
                this.logActivity('logout', 'تسجيل خروج', {
                    username: this.currentUser ? this.currentUser.username : 'unknown',
                    sessionId: sessionId
                });
                
                // حذف الجلسة
                delete this.userSessions[sessionId];
                await this.saveSessions();
            }
            
            // مسح البيانات المحلية
            localStorage.removeItem('currentSession');
            this.currentUser = null;
            
            console.log('✅ تم تسجيل الخروج بنجاح');
            
            // الانتقال لصفحة تسجيل الدخول
            window.location.href = 'login.html';
            
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            // في حالة الخطأ، انتقل لصفحة تسجيل الدخول على أي حال
            window.location.href = 'login.html';
        }
    }
    
    // مسح جلسة
    async clearSession(sessionId) {
        try {
            if (this.userSessions[sessionId]) {
                delete this.userSessions[sessionId];
                await this.saveSessions();
            }
            
            if (localStorage.getItem('currentSession') === sessionId) {
                localStorage.removeItem('currentSession');
            }
            
            this.currentUser = null;
            
        } catch (error) {
            console.error('خطأ في مسح الجلسة:', error);
        }
    }
    
    // إنشاء معرف جلسة
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // تسجيل النشاط
    logActivity(type, description, details = {}) {
        try {
            const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
            
            const activity = {
                id: 'activity_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
                type: type,
                description: description,
                timestamp: new Date().toISOString(),
                userId: this.currentUser ? this.currentUser.id : null,
                username: this.currentUser ? this.currentUser.username : details.username || 'system',
                details: details
            };
            
            activities.unshift(activity);
            
            // الاحتفاظ بآخر 1000 نشاط
            if (activities.length > 1000) {
                activities.splice(1000);
            }
            
            localStorage.setItem('activityLog', JSON.stringify(activities));
            
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }
    
    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }
    
    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }
    
    // فحص إذا كان المستخدم مسجل دخول
    isLoggedIn() {
        return this.currentUser !== null;
    }
    
    // الحصول على جميع المستخدمين
    getAllUsers() {
        return Object.values(this.users);
    }
    
    // إضافة مستخدم جديد
    async addUser(userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }
            
            const username = userData.username.toLowerCase();
            
            if (this.users[username]) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }
            
            const newUser = {
                id: username,
                username: username,
                password: this.hashPassword(userData.password),
                fullName: userData.fullName,
                email: userData.email,
                role: userData.role || 'operator',
                permissions: userData.permissions || ['read'],
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                loginAttempts: 0,
                lastLoginAttempt: null
            };
            
            this.users[username] = newUser;
            await this.saveUsers();
            
            this.logActivity('user_created', `إضافة مستخدم جديد: ${newUser.fullName}`, {
                newUsername: username,
                role: newUser.role
            });
            
            return { success: true, user: newUser };
            
        } catch (error) {
            console.error('خطأ في إضافة المستخدم:', error);
            throw error;
        }
    }
    
    // تحديث مستخدم
    async updateUser(username, userData) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }
            
            const user = this.users[username.toLowerCase()];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }
            
            // تحديث البيانات
            if (userData.fullName) user.fullName = userData.fullName;
            if (userData.email) user.email = userData.email;
            if (userData.role) user.role = userData.role;
            if (userData.permissions) user.permissions = userData.permissions;
            if (userData.password) user.password = this.hashPassword(userData.password);
            if (userData.isActive !== undefined) user.isActive = userData.isActive;
            
            this.users[username.toLowerCase()] = user;
            await this.saveUsers();
            
            // تحديث المستخدم الحالي إذا كان هو نفسه
            if (this.currentUser && this.currentUser.username === username) {
                this.currentUser = user;
            }
            
            this.logActivity('user_updated', `تحديث بيانات المستخدم: ${user.fullName}`, {
                username: username,
                changes: Object.keys(userData)
            });
            
            return { success: true, user: user };
            
        } catch (error) {
            console.error('خطأ في تحديث المستخدم:', error);
            throw error;
        }
    }
    
    // حذف مستخدم
    async deleteUser(username) {
        try {
            if (!this.hasPermission('manage_users')) {
                throw new Error('ليس لديك صلاحية لإدارة المستخدمين');
            }
            
            if (username.toLowerCase() === this.currentUser.username.toLowerCase()) {
                throw new Error('لا يمكن حذف حسابك الخاص');
            }
            
            const user = this.users[username.toLowerCase()];
            if (!user) {
                throw new Error('المستخدم غير موجود');
            }
            
            delete this.users[username.toLowerCase()];
            await this.saveUsers();
            
            // حذف جلسات المستخدم
            Object.keys(this.userSessions).forEach(sessionId => {
                if (this.userSessions[sessionId].username === username) {
                    delete this.userSessions[sessionId];
                }
            });
            await this.saveSessions();
            
            this.logActivity('user_deleted', `حذف المستخدم: ${user.fullName}`, {
                deletedUsername: username
            });
            
            return { success: true };
            
        } catch (error) {
            console.error('خطأ في حذف المستخدم:', error);
            throw error;
        }
    }
}

// إنشاء مثيل عام من نظام المصادقة المحسن
window.fixedAuthManager = new FixedAuthManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FixedAuthManager;
}

console.log('🔧 تم تحميل نظام المصادقة المحسن');

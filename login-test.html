<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
    <script src="network-sync-client.js"></script>
    <script src="sync-integration.js"></script>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تسجيل الدخول</h1>
        <p>أداة شاملة لاختبار وتشخيص مشاكل تسجيل الدخول</p>

        <div class="test-section info">
            <h3>📋 حالة النظام</h3>
            <div id="systemStatus">جاري فحص النظام...</div>
        </div>

        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <div>
                <button onclick="testLogin('admin', 'admin123')">اختبار admin</button>
                <button onclick="testLogin('analyst', 'analyst123')">اختبار analyst</button>
                <button onclick="testLogin('operator', 'operator123')">اختبار operator</button>
                <button onclick="testLogin('emergency', 'emergency123')">اختبار emergency</button>
            </div>
            <div id="loginTestResults"></div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات الإصلاح</h3>
            <div>
                <button class="btn-success" onclick="fixUserData()">إصلاح بيانات المستخدمين</button>
                <button class="btn-success" onclick="clearSessions()">مسح جميع الجلسات</button>
                <button class="btn-danger" onclick="resetSystem()">إعادة تعيين النظام</button>
            </div>
            <div id="fixResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 معلومات النظام</h3>
            <div id="systemInfo">جاري تحميل المعلومات...</div>
        </div>

        <div class="test-section">
            <h3>🌐 روابط سريعة</h3>
            <div>
                <button onclick="window.open('login-fixed.html', '_blank')">صفحة تسجيل الدخول المحسنة</button>
                <button onclick="window.open('login.html', '_blank')">صفحة تسجيل الدخول الأصلية</button>
                <button onclick="window.open('index.html', '_blank')">الصفحة الرئيسية</button>
                <button onclick="window.open('enhanced-dashboard.html', '_blank')">لوحة التحكم</button>
            </div>
        </div>
    </div>

    <script src="fixed-auth-system.js"></script>
    <script>
        window.onload = async function() {
            await checkSystemStatus();
            loadSystemInfo();
        };

        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let status = [];

            try {
                // فحص localStorage
                const users = localStorage.getItem('systemUsers');
                if (users) {
                    const userCount = Object.keys(JSON.parse(users)).length;
                    status.push(`✅ بيانات المستخدمين: ${userCount} مستخدم`);
                } else {
                    status.push('❌ بيانات المستخدمين غير موجودة');
                }

                // فحص نظام المصادقة
                if (window.fixedAuthManager) {
                    await window.fixedAuthManager.waitForInit();
                    status.push('✅ نظام المصادقة المحسن: متاح');
                } else {
                    status.push('❌ نظام المصادقة المحسن: غير متاح');
                }

                // فحص الجلسات
                const sessions = localStorage.getItem('userSessions');
                if (sessions) {
                    const sessionCount = Object.keys(JSON.parse(sessions)).length;
                    status.push(`ℹ️ الجلسات المحفوظة: ${sessionCount}`);
                } else {
                    status.push('ℹ️ لا توجد جلسات محفوظة');
                }

            } catch (error) {
                status.push(`❌ خطأ في فحص النظام: ${error.message}`);
            }

            statusDiv.innerHTML = status.join('<br>');
        }

        async function testLogin(username, password) {
            const resultsDiv = document.getElementById('loginTestResults');
            resultsDiv.innerHTML = `<p>جاري اختبار تسجيل الدخول للمستخدم: ${username}...</p>`;

            try {
                if (!window.fixedAuthManager) {
                    throw new Error('نظام المصادقة غير متاح');
                }

                await window.fixedAuthManager.waitForInit();

                const result = await window.fixedAuthManager.login(username, password, false);

                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ نجح تسجيل الدخول</h4>
                            <p><strong>المستخدم:</strong> ${result.user.fullName}</p>
                            <p><strong>الدور:</strong> ${result.user.role}</p>
                            <p><strong>الصلاحيات:</strong> ${result.user.permissions.join(', ')}</p>
                            <p><strong>معرف الجلسة:</strong> ${result.session.sessionId}</p>
                        </div>
                    `;

                    // تسجيل خروج تلقائي بعد 3 ثوان
                    setTimeout(async () => {
                        await window.fixedAuthManager.logout();
                        resultsDiv.innerHTML += '<p>تم تسجيل الخروج تلقائياً</p>';
                    }, 3000);
                } else {
                    throw new Error('فشل في تسجيل الدخول');
                }

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ فشل تسجيل الدخول</h4>
                        <p><strong>الخطأ:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function fixUserData() {
            const resultsDiv = document.getElementById('fixResults');

            try {
                const users = {
                    "admin": {
                        "id": "admin",
                        "username": "admin",
                        "password": hashPassword("admin123"),
                        "fullName": "مدير النظام",
                        "email": "<EMAIL>",
                        "role": "admin",
                        "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                        "isActive": true,
                        "createdAt": new Date().toISOString(),
                        "lastLogin": null,
                        "loginAttempts": 0,
                        "lastLoginAttempt": null
                    },
                    "analyst": {
                        "id": "analyst",
                        "username": "analyst",
                        "password": hashPassword("analyst123"),
                        "fullName": "محلل أمني",
                        "email": "<EMAIL>",
                        "role": "analyst",
                        "permissions": ["read", "write", "view_analytics"],
                        "isActive": true,
                        "createdAt": new Date().toISOString(),
                        "lastLogin": null,
                        "loginAttempts": 0,
                        "lastLoginAttempt": null
                    },
                    "operator": {
                        "id": "operator",
                        "username": "operator",
                        "password": hashPassword("operator123"),
                        "fullName": "مشغل النظام",
                        "email": "<EMAIL>",
                        "role": "operator",
                        "permissions": ["read"],
                        "isActive": true,
                        "createdAt": new Date().toISOString(),
                        "lastLogin": null,
                        "loginAttempts": 0,
                        "lastLoginAttempt": null
                    },
                    "emergency": {
                        "id": "emergency",
                        "username": "emergency",
                        "password": hashPassword("emergency123"),
                        "fullName": "مستخدم طوارئ",
                        "email": "<EMAIL>",
                        "role": "admin",
                        "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                        "isActive": true,
                        "createdAt": new Date().toISOString(),
                        "lastLogin": null,
                        "loginAttempts": 0,
                        "lastLoginAttempt": null
                    }
                };

                localStorage.setItem('systemUsers', JSON.stringify(users));

                resultsDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ تم إصلاح بيانات المستخدمين</h4>
                        <p>تم إنشاء ${Object.keys(users).length} مستخدم</p>
                    </div>
                `;

                // تحديث حالة النظام
                setTimeout(checkSystemStatus, 1000);

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ فشل في إصلاح البيانات</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function clearSessions() {
            const resultsDiv = document.getElementById('fixResults');

            try {
                localStorage.removeItem('userSessions');
                localStorage.removeItem('currentSession');

                resultsDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ تم مسح جميع الجلسات</h4>
                        <p>تم مسح جميع الجلسات المحفوظة</p>
                    </div>
                `;

                setTimeout(checkSystemStatus, 1000);

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ فشل في مسح الجلسات</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function resetSystem() {
            if (confirm('هل أنت متأكد من إعادة تعيين النظام بالكامل؟')) {
                const resultsDiv = document.getElementById('fixResults');

                try {
                    localStorage.clear();
                    sessionStorage.clear();

                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ تم إعادة تعيين النظام</h4>
                            <p>تم مسح جميع البيانات المحلية</p>
                        </div>
                    `;

                    setTimeout(() => {
                        location.reload();
                    }, 2000);

                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ فشل في إعادة التعيين</h4>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }
        }

        function loadSystemInfo() {
            const infoDiv = document.getElementById('systemInfo');

            try {
                let info = [];

                // معلومات المتصفح
                info.push(`<strong>المتصفح:</strong> ${navigator.userAgent}`);
                info.push(`<strong>اللغة:</strong> ${navigator.language}`);

                // معلومات localStorage
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                info.push(`<strong>حجم localStorage:</strong> ${(totalSize / 1024).toFixed(2)} KB`);

                // معلومات الصفحة
                info.push(`<strong>URL الحالي:</strong> ${window.location.href}`);
                info.push(`<strong>وقت التحميل:</strong> ${new Date().toLocaleString('ar-SA')}`);

                infoDiv.innerHTML = info.join('<br>');

            } catch (error) {
                infoDiv.innerHTML = `خطأ في تحميل المعلومات: ${error.message}`;
            }
        }

        function hashPassword(password) {
            let hash = 0;
            for (let i = 0; i < password.length; i++) {
                const char = password.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return hash.toString();
        }
    </script>
</body>
</html>
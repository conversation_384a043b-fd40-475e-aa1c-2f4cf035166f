/**
 * نظام مزامنة البيانات من جانب العميل
 * Client-side Data Synchronization System
 */

class DataSyncClient {
    constructor(serverUrl = null) {
        // Auto-detect sync server URL based on current location
        this.serverUrl = serverUrl || this.detectSyncServerUrl();
        this.syncInterval = 30000; // 30 seconds
        this.isOnline = navigator.onLine;
        this.syncTimer = null;
        this.lastSyncTime = null;
        this.pendingChanges = {
            users: {},
            events: [],
            activities: []
        };

        this.init();
    }

    detectSyncServerUrl() {
        // Get current host and port, then use sync server port
        const currentHost = window.location.hostname;
        const syncPort = 8001;
        const protocol = window.location.protocol;

        // If accessing via IP or domain, use same host for sync server
        if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
            return `${protocol}//${currentHost}:${syncPort}`;
        } else {
            // For network access, use the same host
            return `${protocol}//${currentHost}:${syncPort}`;
        }
    }
    
    init() {
        this.setupEventListeners();
        this.loadPendingChanges();
        this.startAutoSync();
        this.checkServerConnection();
    }
    
    setupEventListeners() {
        // Monitor online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showSyncStatus('متصل', 'success');
            this.syncNow();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showSyncStatus('غير متصل', 'warning');
        });
        
        // Monitor page visibility for sync on focus
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline) {
                this.syncNow();
            }
        });
    }
    
    async checkServerConnection() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch(`${this.serverUrl}/api/sync/status`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const status = await response.json();
                this.showSyncStatus('متصل بالخادم', 'success');
                console.log('Sync server status:', status);
                console.log(`Connected to sync server: ${this.serverUrl}`);
                return true;
            } else {
                this.showSyncStatus('خطأ في الاتصال', 'error');
                console.error(`Server responded with status: ${response.status}`);
                return false;
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                this.showSyncStatus('انتهت مهلة الاتصال', 'error');
                console.error('Connection timeout to sync server');
            } else {
                this.showSyncStatus('الخادم غير متاح', 'error');
                console.error('Server connection error:', error);
                console.log(`Trying to connect to: ${this.serverUrl}`);
            }
            return false;
        }
    }
    
    startAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }
        
        this.syncTimer = setInterval(() => {
            if (this.isOnline) {
                this.syncNow();
            }
        }, this.syncInterval);
    }
    
    async syncNow() {
        if (!this.isOnline) {
            this.showSyncStatus('غير متصل', 'warning');
            return false;
        }
        
        try {
            this.showSyncStatus('جاري المزامنة...', 'info');
            
            // Get local data
            const localData = this.getLocalData();
            
            // Send to server
            const syncResult = await this.sendToServer(localData);
            
            if (syncResult.success) {
                // Get updated data from server
                const serverData = await this.getFromServer();
                
                if (serverData.success) {
                    // Update local storage
                    this.updateLocalData(serverData.data);
                    
                    // Clear pending changes
                    this.clearPendingChanges();
                    
                    this.lastSyncTime = new Date();
                    this.showSyncStatus('تم التحديث', 'success');
                    
                    // Notify other components
                    this.notifyDataUpdate();
                    
                    return true;
                }
            }
            
            this.showSyncStatus('فشل في المزامنة', 'error');
            return false;
            
        } catch (error) {
            console.error('Sync error:', error);
            this.showSyncStatus('خطأ في المزامنة', 'error');
            return false;
        }
    }
    
    getLocalData() {
        try {
            return {
                users: JSON.parse(localStorage.getItem('systemUsers') || '{}'),
                events: JSON.parse(localStorage.getItem('securityEvents') || '[]'),
                activities: JSON.parse(localStorage.getItem('userActivities') || '[]'),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error getting local data:', error);
            return {
                users: {},
                events: [],
                activities: [],
                timestamp: new Date().toISOString()
            };
        }
    }
    
    async sendToServer(data) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch(`${this.serverUrl}/api/sync/full`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const result = await response.json();
                console.log('Data sent to sync server successfully');
                return { success: true, data: result };
            } else {
                console.error(`Server error: ${response.status} - ${response.statusText}`);
                return { success: false, error: `Server error: ${response.status}` };
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('Request timeout when sending to server');
                return { success: false, error: 'Request timeout' };
            } else {
                console.error('Error sending to server:', error);
                return { success: false, error: error.message };
            }
        }
    }
    
    async getFromServer() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch(`${this.serverUrl}/api/sync/all`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.json();
                console.log('Data received from sync server successfully');
                return { success: true, data: data };
            } else {
                console.error(`Server error: ${response.status} - ${response.statusText}`);
                return { success: false, error: `Server error: ${response.status}` };
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('Request timeout when getting from server');
                return { success: false, error: 'Request timeout' };
            } else {
                console.error('Error getting from server:', error);
                return { success: false, error: error.message };
            }
        }
    }
    
    updateLocalData(serverData) {
        try {
            // Update users
            if (serverData.users) {
                localStorage.setItem('systemUsers', JSON.stringify(serverData.users));
            }
            
            // Update events
            if (serverData.events) {
                localStorage.setItem('securityEvents', JSON.stringify(serverData.events));
            }
            
            // Update activities
            if (serverData.activities) {
                localStorage.setItem('userActivities', JSON.stringify(serverData.activities));
            }
            
            // Update sync timestamp
            localStorage.setItem('lastSyncTime', new Date().toISOString());
            
        } catch (error) {
            console.error('Error updating local data:', error);
        }
    }
    
    // Methods for tracking changes
    trackUserChange(username, userData) {
        this.pendingChanges.users[username] = {
            ...userData,
            lastModified: Date.now()
        };
        this.savePendingChanges();
    }
    
    trackEventChange(event) {
        if (!event.id) {
            event.id = this.generateId();
        }
        event.timestamp = new Date().toISOString();
        this.pendingChanges.events.push(event);
        this.savePendingChanges();
    }
    
    trackActivityChange(activity) {
        if (!activity.id) {
            activity.id = this.generateId();
        }
        activity.timestamp = new Date().toISOString();
        this.pendingChanges.activities.push(activity);
        this.savePendingChanges();
    }
    
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    savePendingChanges() {
        try {
            localStorage.setItem('pendingChanges', JSON.stringify(this.pendingChanges));
        } catch (error) {
            console.error('Error saving pending changes:', error);
        }
    }
    
    loadPendingChanges() {
        try {
            const saved = localStorage.getItem('pendingChanges');
            if (saved) {
                this.pendingChanges = JSON.parse(saved);
            }
        } catch (error) {
            console.error('Error loading pending changes:', error);
        }
    }
    
    clearPendingChanges() {
        this.pendingChanges = {
            users: {},
            events: [],
            activities: []
        };
        localStorage.removeItem('pendingChanges');
    }
    
    showSyncStatus(message, type = 'info') {
        // Create or update sync status indicator
        let statusElement = document.getElementById('sync-status');
        
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'sync-status';
            statusElement.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                z-index: 10000;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;
            document.body.appendChild(statusElement);
        }
        
        // Set colors based on type
        const colors = {
            success: { bg: '#10b981', text: '#ffffff' },
            error: { bg: '#ef4444', text: '#ffffff' },
            warning: { bg: '#f59e0b', text: '#ffffff' },
            info: { bg: '#3b82f6', text: '#ffffff' }
        };
        
        const color = colors[type] || colors.info;
        statusElement.style.backgroundColor = color.bg;
        statusElement.style.color = color.text;
        statusElement.textContent = message;
        
        // Auto hide after 3 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                if (statusElement.textContent === message) {
                    statusElement.style.opacity = '0';
                    setTimeout(() => {
                        if (statusElement.style.opacity === '0') {
                            statusElement.remove();
                        }
                    }, 300);
                }
            }, 3000);
        }
    }
    
    notifyDataUpdate() {
        // Dispatch custom event for other components to listen
        const event = new CustomEvent('dataSync', {
            detail: {
                timestamp: new Date().toISOString(),
                type: 'update'
            }
        });
        window.dispatchEvent(event);
    }
    
    // Public methods for manual sync
    async syncUsers() {
        const users = JSON.parse(localStorage.getItem('systemUsers') || '{}');
        return await this.sendToServer({ users });
    }
    
    async syncEvents() {
        const events = JSON.parse(localStorage.getItem('securityEvents') || '[]');
        return await this.sendToServer({ events });
    }
    
    async syncActivities() {
        const activities = JSON.parse(localStorage.getItem('userActivities') || '[]');
        return await this.sendToServer({ activities });
    }
    
    // Get sync statistics
    getSyncStats() {
        return {
            lastSyncTime: this.lastSyncTime,
            isOnline: this.isOnline,
            pendingChanges: {
                users: Object.keys(this.pendingChanges.users).length,
                events: this.pendingChanges.events.length,
                activities: this.pendingChanges.activities.length
            }
        };
    }
    
    // Force sync
    forcSync() {
        return this.syncNow();
    }
    
    // Stop auto sync
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
    }
    
    // Restart auto sync
    restartAutoSync() {
        this.stopAutoSync();
        this.startAutoSync();
    }
}

// Initialize data sync client
let dataSyncClient;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initDataSync);
} else {
    initDataSync();
}

function initDataSync() {
    // Check if we're on the main application page
    if (window.location.pathname.includes('index.html') || 
        window.location.pathname === '/' ||
        document.getElementById('app')) {
        
        dataSyncClient = new DataSyncClient();
        
        // Make it globally available
        window.dataSyncClient = dataSyncClient;
        
        console.log('Data sync client initialized');
    }
}


    async syncNowWithRetry(maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const result = await this.syncNow();
                if (result) {
                    return true;
                }
            } catch (error) {
                console.error(`Sync attempt ${attempt} failed:`, error);
            }
            
            if (attempt < maxRetries) {
                console.log(`Retrying sync in ${attempt * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, attempt * 2000));
            }
        }
        
        console.error('All sync attempts failed');
        this.showSyncStatus('فشل في المزامنة بعد عدة محاولات', 'error');
        return false;
    }

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataSyncClient;
}

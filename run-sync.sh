#!/bin/bash

echo "======================================================"
echo "🚀 تشغيل نظام إدارة أمن المعلومات مع المزامنة"
echo "   Starting ISMS with Data Synchronization"
echo "======================================================"
echo

echo "📁 إعداد مجلد البيانات..."
mkdir -p data
mkdir -p data/backups

# إنشاء الملفات الأساسية إذا لم تكن موجودة
[ ! -f "data/users.json" ] && echo '{}' > data/users.json
[ ! -f "data/events.json" ] && echo '[]' > data/events.json
[ ! -f "data/activities.json" ] && echo '[]' > data/activities.json
[ ! -f "data/sync_log.json" ] && echo '[]' > data/sync_log.json

echo "✅ تم إعداد مجلد البيانات"
echo

# الحصول على عنوان IP المحلي
LOCAL_IP=$(hostname -I | awk '{print $1}')
if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP="127.0.0.1"
fi

echo "🌐 عنوان IP المحلي: $LOCAL_IP"
echo

echo "🌐 تشغيل الخادم الرئيسي..."
python3 start-server.py &
MAIN_PID=$!

echo "🔄 تشغيل خادم المزامنة..."
python3 data-sync-server.py &
SYNC_PID=$!

echo "⏳ انتظار تشغيل الخوادم..."
sleep 5

echo
echo "🌐 فتح المتصفح..."
if command -v xdg-open > /dev/null; then
    xdg-open "http://localhost:8000/login.html"
elif command -v open > /dev/null; then
    open "http://localhost:8000/login.html"
else
    echo "افتح المتصفح يدوياً على: http://localhost:8000/login.html"
fi

echo
echo "======================================================"
echo "🎉 تم تشغيل النظام بنجاح!"
echo
echo "🔗 روابط الوصول:"
echo "   📍 محلي: http://localhost:8000/login.html"
echo "   🌍 شبكة: http://$LOCAL_IP:8000/login.html"
echo
echo "🔑 بيانات الدخول:"
echo "   👤 اسم المستخدم: admin"
echo "   🔒 كلمة المرور: admin123"
echo
echo "🔧 معرفات العمليات:"
echo "   الخادم الرئيسي: $MAIN_PID"
echo "   خادم المزامنة: $SYNC_PID"
echo
echo "💡 لإيقاف النظام: اضغط Ctrl+C"
echo "======================================================"

# دالة إيقاف النظام
cleanup() {
    echo
    echo "🛑 إيقاف النظام..."
    kill $MAIN_PID 2>/dev/null
    kill $SYNC_PID 2>/dev/null
    echo "✅ تم إيقاف النظام"
    exit 0
}

# التقاط إشارة الإيقاف
trap cleanup SIGINT SIGTERM

# انتظار إيقاف النظام
wait

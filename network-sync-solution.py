#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل مشكلة المزامنة في الشبكة بين الأجهزة الأخرى
Network Synchronization Solution for Multiple Devices
"""

import os
import sys
import json
import time
import socket
import threading
import websocket
import http.server
import socketserver
from datetime import datetime
from urllib.parse import urlparse, parse_qs

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

class NetworkSyncServer:
    """خادم المزامنة للشبكة"""
    
    def __init__(self, host='0.0.0.0', port=8001):
        self.host = host
        self.port = port
        self.clients = {}  # قائمة العملاء المتصلين
        self.data_store = {}  # مخزن البيانات المشتركة
        self.sync_log = []  # سجل المزامنة
        self.running = False
        
        # إنشاء مجلد البيانات
        os.makedirs("sync_data", exist_ok=True)
        
        # تحميل البيانات المحفوظة
        self.load_data()
    
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            if os.path.exists("sync_data/shared_data.json"):
                with open("sync_data/shared_data.json", 'r', encoding='utf-8') as f:
                    self.data_store = json.load(f)
            
            if os.path.exists("sync_data/sync_log.json"):
                with open("sync_data/sync_log.json", 'r', encoding='utf-8') as f:
                    self.sync_log = json.load(f)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            with open("sync_data/shared_data.json", 'w', encoding='utf-8') as f:
                json.dump(self.data_store, f, ensure_ascii=False, indent=2)
            
            # حفظ آخر 1000 سجل فقط
            recent_logs = self.sync_log[-1000:] if len(self.sync_log) > 1000 else self.sync_log
            with open("sync_data/sync_log.json", 'w', encoding='utf-8') as f:
                json.dump(recent_logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
    
    def log_action(self, action, client_id, data=None):
        """تسجيل العمليات"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'client_id': client_id,
            'data': data
        }
        self.sync_log.append(log_entry)
        print(f"[{log_entry['timestamp']}] {action} - {client_id}")
    
    def register_client(self, client_id, client_info):
        """تسجيل عميل جديد"""
        self.clients[client_id] = {
            'info': client_info,
            'last_seen': datetime.now().isoformat(),
            'status': 'connected'
        }
        self.log_action('CLIENT_CONNECTED', client_id, client_info)
        return True
    
    def unregister_client(self, client_id):
        """إلغاء تسجيل عميل"""
        if client_id in self.clients:
            self.clients[client_id]['status'] = 'disconnected'
            self.log_action('CLIENT_DISCONNECTED', client_id)
    
    def sync_data(self, client_id, data_type, data, operation='update'):
        """مزامنة البيانات"""
        try:
            if data_type not in self.data_store:
                self.data_store[data_type] = {}
            
            if operation == 'update':
                self.data_store[data_type].update(data)
            elif operation == 'replace':
                self.data_store[data_type] = data
            elif operation == 'delete':
                for key in data:
                    if key in self.data_store[data_type]:
                        del self.data_store[data_type][key]
            
            # تحديث آخر مزامنة
            if client_id in self.clients:
                self.clients[client_id]['last_seen'] = datetime.now().isoformat()
            
            self.log_action(f'DATA_SYNC_{operation.upper()}', client_id, {
                'data_type': data_type,
                'keys': list(data.keys()) if isinstance(data, dict) else str(data)
            })
            
            # حفظ البيانات
            self.save_data()
            
            # إشعار العملاء الآخرين
            self.notify_clients(client_id, data_type, data, operation)
            
            return True
            
        except Exception as e:
            print(f"خطأ في مزامنة البيانات: {e}")
            return False
    
    def notify_clients(self, sender_id, data_type, data, operation):
        """إشعار العملاء الآخرين بالتغييرات"""
        notification = {
            'type': 'data_update',
            'sender': sender_id,
            'data_type': data_type,
            'data': data,
            'operation': operation,
            'timestamp': datetime.now().isoformat()
        }
        
        # هنا يمكن إضافة إشعارات WebSocket أو HTTP callbacks
        self.log_action('NOTIFICATION_SENT', 'server', {
            'recipients': len(self.clients) - 1,
            'data_type': data_type
        })
    
    def get_data(self, data_type=None):
        """الحصول على البيانات"""
        if data_type:
            return self.data_store.get(data_type, {})
        return self.data_store
    
    def get_clients_status(self):
        """الحصول على حالة العملاء"""
        return self.clients
    
    def get_sync_stats(self):
        """الحصول على إحصائيات المزامنة"""
        total_syncs = len([log for log in self.sync_log if 'DATA_SYNC' in log['action']])
        active_clients = len([c for c in self.clients.values() if c['status'] == 'connected'])
        
        return {
            'total_clients': len(self.clients),
            'active_clients': active_clients,
            'total_syncs': total_syncs,
            'data_types': list(self.data_store.keys()),
            'last_sync': self.sync_log[-1]['timestamp'] if self.sync_log else None
        }

class SyncHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """معالج HTTP للمزامنة"""
    
    def __init__(self, *args, sync_server=None, **kwargs):
        self.sync_server = sync_server
        super().__init__(*args, **kwargs)
    
    def end_headers(self):
        """إضافة headers للسماح بالوصول من الشبكة"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Client-ID')
        super().end_headers()
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        if self.path.startswith('/api/sync/'):
            self.handle_sync_api()
        else:
            super().do_POST()
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path.startswith('/api/sync/'):
            self.handle_sync_api()
        else:
            super().do_GET()
    
    def handle_sync_api(self):
        """معالجة API المزامنة"""
        try:
            # استخراج معرف العميل
            client_id = self.headers.get('X-Client-ID', f'client_{int(time.time())}')
            
            # قراءة البيانات
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = {}
            if content_length > 0:
                post_data = json.loads(self.rfile.read(content_length).decode('utf-8'))
            
            # معالجة المسارات المختلفة
            if self.path == '/api/sync/register':
                response = self.handle_register(client_id, post_data)
            elif self.path == '/api/sync/data':
                if self.command == 'POST':
                    response = self.handle_sync_data(client_id, post_data)
                else:
                    response = self.handle_get_data(client_id, post_data)
            elif self.path == '/api/sync/status':
                response = self.handle_get_status()
            elif self.path == '/api/sync/stats':
                response = self.handle_get_stats()
            else:
                response = {'error': 'Unknown endpoint'}
            
            # إرسال الاستجابة
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f'Server Error: {str(e)}')
    
    def handle_register(self, client_id, data):
        """معالجة تسجيل العميل"""
        client_info = data.get('client_info', {})
        success = self.sync_server.register_client(client_id, client_info)
        return {
            'success': success,
            'client_id': client_id,
            'message': 'Client registered successfully' if success else 'Registration failed'
        }
    
    def handle_sync_data(self, client_id, data):
        """معالجة مزامنة البيانات"""
        data_type = data.get('data_type', 'default')
        sync_data = data.get('data', {})
        operation = data.get('operation', 'update')
        
        success = self.sync_server.sync_data(client_id, data_type, sync_data, operation)
        return {
            'success': success,
            'message': 'Data synced successfully' if success else 'Sync failed',
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_get_data(self, client_id, params):
        """معالجة طلب البيانات"""
        data_type = params.get('data_type')
        data = self.sync_server.get_data(data_type)
        return {
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_get_status(self):
        """معالجة طلب حالة العملاء"""
        clients = self.sync_server.get_clients_status()
        return {
            'success': True,
            'clients': clients,
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_get_stats(self):
        """معالجة طلب الإحصائيات"""
        stats = self.sync_server.get_sync_stats()
        return {
            'success': True,
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        }

def create_sync_server_handler(sync_server):
    """إنشاء معالج HTTP مع خادم المزامنة"""
    def handler(*args, **kwargs):
        return SyncHTTPHandler(*args, sync_server=sync_server, **kwargs)
    return handler

def main():
    """الوظيفة الرئيسية"""
    local_ip = get_local_ip()
    sync_port = 8001
    
    print("🔄 بدء خادم المزامنة للشبكة")
    print("=" * 50)
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print(f"🔌 منفذ المزامنة: {sync_port}")
    
    # إنشاء خادم المزامنة
    sync_server = NetworkSyncServer(port=sync_port)
    
    try:
        # إنشاء معالج HTTP
        handler = create_sync_server_handler(sync_server)
        
        # بدء خادم HTTP
        with socketserver.TCPServer(("0.0.0.0", sync_port), handler) as httpd:
            httpd.allow_reuse_address = True
            sync_server.running = True
            
            print(f"\n🚀 تم بدء خادم المزامنة بنجاح!")
            print(f"🌐 روابط API المزامنة:")
            print(f"   • تسجيل العميل: http://{local_ip}:{sync_port}/api/sync/register")
            print(f"   • مزامنة البيانات: http://{local_ip}:{sync_port}/api/sync/data")
            print(f"   • حالة العملاء: http://{local_ip}:{sync_port}/api/sync/status")
            print(f"   • الإحصائيات: http://{local_ip}:{sync_port}/api/sync/stats")
            
            print(f"\n💡 استخدام API المزامنة:")
            print(f"   • أضف header: X-Client-ID مع معرف فريد لكل جهاز")
            print(f"   • استخدم POST لإرسال البيانات")
            print(f"   • استخدم GET لاستقبال البيانات")
            
            print(f"\n✅ خادم المزامنة جاهز ويقبل الاتصالات...")
            print(f"🛑 اضغط Ctrl+C لإيقاف الخادم")
            print("-" * 50)
            
            # بدء الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n🛑 إيقاف خادم المزامنة...")
        sync_server.running = False
        sync_server.save_data()
        print("✅ تم إيقاف خادم المزامنة بنجاح")
    except Exception as e:
        print(f"❌ خطأ في خادم المزامنة: {e}")

def create_sync_client():
    """إنشاء عميل المزامنة"""

    client_code = '''
class NetworkSyncClient {
    constructor(serverUrl, clientId = null) {
        this.serverUrl = serverUrl;
        this.clientId = clientId || 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        this.isRegistered = false;
        this.syncInterval = null;
        this.lastSyncTime = null;
        this.localData = {};
        this.syncCallbacks = {};

        console.log('🔄 تهيئة عميل المزامنة:', this.clientId);
        this.init();
    }

    async init() {
        try {
            await this.register();
            this.startAutoSync();
            console.log('✅ تم تهيئة عميل المزامنة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة عميل المزامنة:', error);
        }
    }

    async register() {
        try {
            const clientInfo = {
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                screen: {
                    width: screen.width,
                    height: screen.height
                }
            };

            const response = await fetch(`${this.serverUrl}/api/sync/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Client-ID': this.clientId
                },
                body: JSON.stringify({
                    client_info: clientInfo
                })
            });

            const result = await response.json();
            if (result.success) {
                this.isRegistered = true;
                console.log('✅ تم تسجيل العميل بنجاح:', this.clientId);
                return true;
            } else {
                throw new Error(result.message || 'فشل في التسجيل');
            }
        } catch (error) {
            console.error('❌ خطأ في تسجيل العميل:', error);
            return false;
        }
    }

    async syncData(dataType, data, operation = 'update') {
        if (!this.isRegistered) {
            console.warn('⚠️ العميل غير مسجل، محاولة التسجيل...');
            await this.register();
        }

        try {
            const response = await fetch(`${this.serverUrl}/api/sync/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Client-ID': this.clientId
                },
                body: JSON.stringify({
                    data_type: dataType,
                    data: data,
                    operation: operation
                })
            });

            const result = await response.json();
            if (result.success) {
                this.lastSyncTime = new Date();
                console.log(`✅ تم مزامنة البيانات: ${dataType}`);

                // تحديث البيانات المحلية
                if (!this.localData[dataType]) {
                    this.localData[dataType] = {};
                }

                if (operation === 'update') {
                    Object.assign(this.localData[dataType], data);
                } else if (operation === 'replace') {
                    this.localData[dataType] = data;
                }

                // استدعاء callbacks
                this.triggerCallbacks(dataType, data, operation);

                return true;
            } else {
                throw new Error(result.message || 'فشل في المزامنة');
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة البيانات:', error);
            return false;
        }
    }

    async getData(dataType = null) {
        try {
            const url = `${this.serverUrl}/api/sync/data${dataType ? '?data_type=' + dataType : ''}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });

            const result = await response.json();
            if (result.success) {
                // تحديث البيانات المحلية
                if (dataType) {
                    this.localData[dataType] = result.data;
                } else {
                    this.localData = result.data;
                }

                console.log(`✅ تم استقبال البيانات: ${dataType || 'جميع البيانات'}`);
                return result.data;
            } else {
                throw new Error('فشل في استقبال البيانات');
            }
        } catch (error) {
            console.error('❌ خطأ في استقبال البيانات:', error);
            return null;
        }
    }

    async getStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/api/sync/status`, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });

            const result = await response.json();
            return result.success ? result.clients : null;
        } catch (error) {
            console.error('❌ خطأ في الحصول على الحالة:', error);
            return null;
        }
    }

    async getStats() {
        try {
            const response = await fetch(`${this.serverUrl}/api/sync/stats`, {
                method: 'GET',
                headers: {
                    'X-Client-ID': this.clientId
                }
            });

            const result = await response.json();
            return result.success ? result.stats : null;
        } catch (error) {
            console.error('❌ خطأ في الحصول على الإحصائيات:', error);
            return null;
        }
    }

    onDataChange(dataType, callback) {
        if (!this.syncCallbacks[dataType]) {
            this.syncCallbacks[dataType] = [];
        }
        this.syncCallbacks[dataType].push(callback);
    }

    triggerCallbacks(dataType, data, operation) {
        if (this.syncCallbacks[dataType]) {
            this.syncCallbacks[dataType].forEach(callback => {
                try {
                    callback(data, operation, dataType);
                } catch (error) {
                    console.error('❌ خطأ في callback:', error);
                }
            });
        }
    }

    startAutoSync(interval = 30000) {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        this.syncInterval = setInterval(async () => {
            try {
                // مزامنة البيانات المحلية مع الخادم
                await this.getData();
                console.log('🔄 مزامنة تلقائية مكتملة');
            } catch (error) {
                console.error('❌ خطأ في المزامنة التلقائية:', error);
            }
        }, interval);

        console.log(`🔄 تم بدء المزامنة التلقائية كل ${interval/1000} ثانية`);
    }

    stopAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('🛑 تم إيقاف المزامنة التلقائية');
        }
    }

    // دوال مساعدة للاستخدام السهل
    async syncUsers(users) {
        return await this.syncData('users', users);
    }

    async syncSessions(sessions) {
        return await this.syncData('sessions', sessions);
    }

    async syncActivities(activities) {
        return await this.syncData('activities', activities);
    }

    async syncSettings(settings) {
        return await this.syncData('settings', settings);
    }

    async getUsers() {
        return await this.getData('users');
    }

    async getSessions() {
        return await this.getData('sessions');
    }

    async getActivities() {
        return await this.getData('activities');
    }

    async getSettings() {
        return await this.getData('settings');
    }
}

// تهيئة عميل المزامنة العام
window.networkSyncClient = null;

function initNetworkSync(serverUrl = null) {
    if (!serverUrl) {
        // محاولة تحديد عنوان الخادم تلقائياً
        const currentHost = window.location.hostname;
        serverUrl = `http://${currentHost}:8001`;
    }

    window.networkSyncClient = new NetworkSyncClient(serverUrl);
    return window.networkSyncClient;
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (!window.networkSyncClient) {
        initNetworkSync();
    }
});
'''

    return client_code

if __name__ == "__main__":
    main()

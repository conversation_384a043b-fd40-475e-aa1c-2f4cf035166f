<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المزامنة في الشبكة - نظام إدارة أمن المعلومات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .test-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .test-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
            color: #2c3e50;
        }

        .status-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .status-online {
            color: #27ae60;
            font-weight: bold;
        }

        .status-offline {
            color: #e74c3c;
            font-weight: bold;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .input-group {
            margin: 15px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }

        .input-group input,
        .input-group textarea,
        .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
        }

        .input-group textarea {
            height: 100px;
            resize: vertical;
        }

        .log-section {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }

        .sync-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
        }

        .sync-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .sync-status.connected {
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        .sync-status.disconnected {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .data-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }

        .client-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .client-item {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
        }

        .client-item.active {
            background: #c8e6c9;
        }

        .client-item.inactive {
            background: #ffcdd2;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .sync-indicator {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="sync-indicator">
        🔄 حالة المزامنة: <span id="syncStatusText">جاري الاتصال...</span>
        <span class="sync-status" id="syncStatusIndicator"></span>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔄 اختبار المزامنة في الشبكة</h1>
            <p>أداة شاملة لاختبار وإدارة مزامنة البيانات بين الأجهزة المختلفة</p>
            <p id="clientInfo">معرف العميل: جاري التحميل...</p>
        </div>

        <div class="test-grid">
            <!-- معلومات الاتصال -->
            <div class="test-card">
                <h3><i class="fas fa-network-wired"></i> معلومات الاتصال</h3>

                <div class="status-item">
                    <div class="status-label">خادم المزامنة:</div>
                    <div class="status-value" id="syncServerUrl">جاري التحديد...</div>
                </div>

                <div class="status-item">
                    <div class="status-label">حالة الاتصال:</div>
                    <div class="status-value" id="connectionStatus">جاري الفحص...</div>
                </div>

                <div class="status-item">
                    <div class="status-label">آخر مزامنة:</div>
                    <div class="status-value" id="lastSyncTime">لم تتم بعد</div>
                </div>

                <div class="status-item">
                    <div class="status-label">معرف العميل:</div>
                    <div class="status-value" id="clientId">جاري التحديد...</div>
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <button class="btn btn-success" onclick="reconnectSync()">إعادة الاتصال</button>
                    <button class="btn" onclick="refreshStatus()">تحديث الحالة</button>
                </div>
            </div>

            <!-- العملاء المتصلين -->
            <div class="test-card">
                <h3><i class="fas fa-users"></i> العملاء المتصلين</h3>

                <div class="status-item">
                    <div class="status-label">إجمالي العملاء:</div>
                    <div class="status-value" id="totalClients">0</div>
                </div>

                <div class="status-item">
                    <div class="status-label">العملاء النشطين:</div>
                    <div class="status-value" id="activeClients">0</div>
                </div>

                <div class="client-list" id="clientsList">
                    جاري تحميل قائمة العملاء...
                </div>

                <div style="text-align: center;">
                    <button class="btn" onclick="refreshClients()">تحديث القائمة</button>
                </div>
            </div>
        </div>

        <!-- اختبار إرسال البيانات -->
        <div class="test-card">
            <h3><i class="fas fa-paper-plane"></i> اختبار إرسال البيانات</h3>

            <div class="input-group">
                <label for="dataType">نوع البيانات:</label>
                <select id="dataType">
                    <option value="users">المستخدمين</option>
                    <option value="sessions">الجلسات</option>
                    <option value="activities">الأنشطة</option>
                    <option value="settings">الإعدادات</option>
                    <option value="custom">مخصص</option>
                </select>
            </div>

            <div class="input-group" id="customTypeGroup" style="display: none;">
                <label for="customType">نوع مخصص:</label>
                <input type="text" id="customType" placeholder="اكتب نوع البيانات المخصص">
            </div>

            <div class="input-group">
                <label for="dataContent">محتوى البيانات (JSON):</label>
                <textarea id="dataContent" placeholder='{"key": "value", "example": "data"}'></textarea>
            </div>

            <div class="input-group">
                <label for="operation">العملية:</label>
                <select id="operation">
                    <option value="update">تحديث</option>
                    <option value="replace">استبدال</option>
                    <option value="delete">حذف</option>
                </select>
            </div>

            <div style="text-align: center;">
                <button class="btn btn-success" onclick="sendTestData()">إرسال البيانات</button>
                <button class="btn btn-warning" onclick="clearDataForm()">مسح النموذج</button>
            </div>

            <div id="sendResult" style="margin-top: 15px;"></div>
        </div>

        <!-- عرض البيانات المتزامنة -->
        <div class="test-card">
            <h3><i class="fas fa-database"></i> البيانات المتزامنة</h3>

            <div style="text-align: center; margin-bottom: 15px;">
                <button class="btn" onclick="loadAllData()">تحميل جميع البيانات</button>
                <button class="btn" onclick="loadUsers()">المستخدمين</button>
                <button class="btn" onclick="loadSessions()">الجلسات</button>
                <button class="btn" onclick="loadActivities()">الأنشطة</button>
                <button class="btn" onclick="loadSettings()">الإعدادات</button>
            </div>

            <div class="data-display" id="syncedData">
                اضغط على أحد الأزرار أعلاه لتحميل البيانات...
            </div>
        </div>

        <!-- إحصائيات المزامنة -->
        <div class="test-card">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات المزامنة</h3>

            <div class="status-item">
                <div class="status-label">إجمالي المزامنات:</div>
                <div class="status-value" id="totalSyncs">0</div>
            </div>

            <div class="status-item">
                <div class="status-label">أنواع البيانات:</div>
                <div class="status-value" id="dataTypes">لا يوجد</div>
            </div>

            <div class="status-item">
                <div class="status-label">آخر مزامنة عامة:</div>
                <div class="status-value" id="lastGlobalSync">لا يوجد</div>
            </div>

            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="refreshStats()">تحديث الإحصائيات</button>
                <button class="btn btn-warning" onclick="clearSyncData()">مسح البيانات</button>
            </div>
        </div>

        <!-- سجل المزامنة -->
        <div class="test-card">
            <h3><i class="fas fa-list"></i> سجل المزامنة</h3>

            <div class="log-section" id="syncLog">
                جاري تحميل سجل المزامنة...
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="refreshLog()">تحديث السجل</button>
                <button class="btn btn-warning" onclick="clearLog()">مسح السجل</button>
                <button class="btn btn-danger" onclick="downloadLog()">تحميل السجل</button>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="test-card">
            <h3><i class="fas fa-bolt"></i> اختبارات سريعة</h3>

            <div style="text-align: center;">
                <button class="btn btn-success" onclick="quickTestUsers()">اختبار المستخدمين</button>
                <button class="btn btn-success" onclick="quickTestSessions()">اختبار الجلسات</button>
                <button class="btn btn-success" onclick="quickTestActivities()">اختبار الأنشطة</button>
                <button class="btn btn-warning" onclick="stressTest()">اختبار الضغط</button>
            </div>

            <div id="quickTestResults" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script src="network-sync-client.js"></script>
    <script>
        // متغيرات عامة
        let syncClient = null;
        let syncLog = [];

        // تحميل الصفحة
        window.onload = async function() {
            console.log('🔄 تحميل صفحة اختبار المزامنة...');

            // تهيئة عميل المزامنة
            await initSyncClient();

            // تحديث الواجهة
            updateUI();

            // بدء التحديث الدوري
            startPeriodicUpdate();

            // إعداد مستمعي الأحداث
            setupEventListeners();

            console.log('✅ تم تحميل صفحة اختبار المزامنة بنجاح');
        };

        // تهيئة عميل المزامنة
        async function initSyncClient() {
            try {
                const currentHost = window.location.hostname;
                const syncServerUrl = `http://${currentHost}:8001`;

                document.getElementById('syncServerUrl').textContent = syncServerUrl;

                // إنشاء عميل المزامنة
                syncClient = new NetworkSyncClient(syncServerUrl);

                // إعداد callbacks للتغييرات
                syncClient.onDataChange('users', (data, operation) => {
                    logSync(`تم تحديث المستخدمين: ${operation}`, data);
                });

                syncClient.onDataChange('sessions', (data, operation) => {
                    logSync(`تم تحديث الجلسات: ${operation}`, data);
                });

                syncClient.onDataChange('activities', (data, operation) => {
                    logSync(`تم تحديث الأنشطة: ${operation}`, data);
                });

                // انتظار التهيئة
                await new Promise(resolve => setTimeout(resolve, 2000));

                // تحديث معلومات العميل
                document.getElementById('clientId').textContent = syncClient.clientId;
                document.getElementById('clientInfo').textContent = `معرف العميل: ${syncClient.clientId}`;

                // تحديث حالة الاتصال
                updateConnectionStatus(true);

            } catch (error) {
                console.error('❌ خطأ في تهيئة عميل المزامنة:', error);
                updateConnectionStatus(false);
            }
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(connected) {
            const statusText = document.getElementById('syncStatusText');
            const statusIndicator = document.getElementById('syncStatusIndicator');
            const connectionStatus = document.getElementById('connectionStatus');

            if (connected) {
                statusText.textContent = 'متصل';
                statusIndicator.className = 'sync-status connected';
                connectionStatus.innerHTML = '<span class="status-online">متصل</span>';
            } else {
                statusText.textContent = 'غير متصل';
                statusIndicator.className = 'sync-status disconnected';
                connectionStatus.innerHTML = '<span class="status-offline">غير متصل</span>';
            }
        }

        // تحديث الواجهة
        async function updateUI() {
            if (!syncClient) return;

            try {
                // تحديث حالة العملاء
                await refreshClients();

                // تحديث الإحصائيات
                await refreshStats();

                // تحديث وقت آخر مزامنة
                if (syncClient.lastSyncTime) {
                    document.getElementById('lastSyncTime').textContent =
                        syncClient.lastSyncTime.toLocaleString('ar-SA');
                }

            } catch (error) {
                console.error('❌ خطأ في تحديث الواجهة:', error);
            }
        }

        // بدء التحديث الدوري
        function startPeriodicUpdate() {
            setInterval(async () => {
                await updateUI();
            }, 10000); // كل 10 ثوان
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تغيير نوع البيانات
            document.getElementById('dataType').addEventListener('change', function() {
                const customGroup = document.getElementById('customTypeGroup');
                if (this.value === 'custom') {
                    customGroup.style.display = 'block';
                } else {
                    customGroup.style.display = 'none';
                }
            });
        }

        // إعادة الاتصال
        async function reconnectSync() {
            try {
                updateConnectionStatus(false);
                await initSyncClient();
                showMessage('تم إعادة الاتصال بنجاح', 'success');
            } catch (error) {
                showMessage('فشل في إعادة الاتصال: ' + error.message, 'error');
            }
        }

        // تحديث الحالة
        async function refreshStatus() {
            await updateUI();
            showMessage('تم تحديث الحالة', 'success');
        }

        // تحديث العملاء
        async function refreshClients() {
            if (!syncClient) return;

            try {
                const clients = await syncClient.getStatus();
                if (clients) {
                    const totalClients = Object.keys(clients).length;
                    const activeClients = Object.values(clients).filter(c => c.status === 'connected').length;

                    document.getElementById('totalClients').textContent = totalClients;
                    document.getElementById('activeClients').textContent = activeClients;

                    // عرض قائمة العملاء
                    const clientsList = document.getElementById('clientsList');
                    let clientsHtml = '';

                    for (const [clientId, clientInfo] of Object.entries(clients)) {
                        const isActive = clientInfo.status === 'connected';
                        const className = isActive ? 'client-item active' : 'client-item inactive';
                        const statusIcon = isActive ? '🟢' : '🔴';

                        clientsHtml += `
                            <div class="${className}">
                                ${statusIcon} ${clientId.substring(0, 12)}...
                                <br><small>${new Date(clientInfo.last_seen).toLocaleTimeString('ar-SA')}</small>
                            </div>
                        `;
                    }

                    clientsList.innerHTML = clientsHtml || 'لا يوجد عملاء متصلين';
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث العملاء:', error);
            }
        }

        // تحديث الإحصائيات
        async function refreshStats() {
            if (!syncClient) return;

            try {
                const stats = await syncClient.getStats();
                if (stats) {
                    document.getElementById('totalSyncs').textContent = stats.total_syncs || 0;
                    document.getElementById('dataTypes').textContent =
                        stats.data_types ? stats.data_types.join(', ') : 'لا يوجد';
                    document.getElementById('lastGlobalSync').textContent =
                        stats.last_sync ? new Date(stats.last_sync).toLocaleString('ar-SA') : 'لا يوجد';
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث الإحصائيات:', error);
            }
        }

        // إرسال بيانات اختبار
        async function sendTestData() {
            if (!syncClient) {
                showMessage('عميل المزامنة غير متاح', 'error');
                return;
            }

            try {
                const dataTypeSelect = document.getElementById('dataType');
                const customType = document.getElementById('customType');
                const dataContent = document.getElementById('dataContent');
                const operation = document.getElementById('operation');

                let dataType = dataTypeSelect.value;
                if (dataType === 'custom') {
                    dataType = customType.value.trim();
                    if (!dataType) {
                        showMessage('يرجى إدخال نوع البيانات المخصص', 'error');
                        return;
                    }
                }

                let data;
                try {
                    data = JSON.parse(dataContent.value || '{}');
                } catch (e) {
                    showMessage('تنسيق JSON غير صحيح', 'error');
                    return;
                }

                const success = await syncClient.syncData(dataType, data, operation.value);

                if (success) {
                    showMessage(`تم إرسال البيانات بنجاح: ${dataType}`, 'success');
                    logSync(`إرسال بيانات: ${dataType}`, data);
                } else {
                    showMessage('فشل في إرسال البيانات', 'error');
                }

            } catch (error) {
                showMessage('خطأ في إرسال البيانات: ' + error.message, 'error');
            }
        }

        // مسح نموذج البيانات
        function clearDataForm() {
            document.getElementById('dataContent').value = '';
            document.getElementById('customType').value = '';
            document.getElementById('dataType').value = 'users';
            document.getElementById('operation').value = 'update';
            document.getElementById('customTypeGroup').style.display = 'none';
        }

        // تحميل جميع البيانات
        async function loadAllData() {
            if (!syncClient) return;

            try {
                const data = await syncClient.getData();
                displayData(data);
            } catch (error) {
                showMessage('خطأ في تحميل البيانات: ' + error.message, 'error');
            }
        }

        // تحميل المستخدمين
        async function loadUsers() {
            if (!syncClient) return;

            try {
                const users = await syncClient.getUsers();
                displayData(users, 'المستخدمين');
            } catch (error) {
                showMessage('خطأ في تحميل المستخدمين: ' + error.message, 'error');
            }
        }

        // تحميل الجلسات
        async function loadSessions() {
            if (!syncClient) return;

            try {
                const sessions = await syncClient.getSessions();
                displayData(sessions, 'الجلسات');
            } catch (error) {
                showMessage('خطأ في تحميل الجلسات: ' + error.message, 'error');
            }
        }

        // تحميل الأنشطة
        async function loadActivities() {
            if (!syncClient) return;

            try {
                const activities = await syncClient.getActivities();
                displayData(activities, 'الأنشطة');
            } catch (error) {
                showMessage('خطأ في تحميل الأنشطة: ' + error.message, 'error');
            }
        }

        // تحميل الإعدادات
        async function loadSettings() {
            if (!syncClient) return;

            try {
                const settings = await syncClient.getSettings();
                displayData(settings, 'الإعدادات');
            } catch (error) {
                showMessage('خطأ في تحميل الإعدادات: ' + error.message, 'error');
            }
        }

        // عرض البيانات
        function displayData(data, title = 'البيانات') {
            const dataDisplay = document.getElementById('syncedData');
            const jsonString = JSON.stringify(data, null, 2);
            dataDisplay.innerHTML = `<strong>${title}:</strong><br><pre>${jsonString}</pre>`;
        }

        // مسح البيانات المتزامنة
        async function clearSyncData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المتزامنة؟')) {
                // هذه وظيفة افتراضية - يمكن تطويرها حسب الحاجة
                showMessage('تم مسح البيانات المحلية', 'success');
                document.getElementById('syncedData').innerHTML = 'تم مسح البيانات...';
            }
        }

        // تسجيل المزامنة
        function logSync(message, data = null) {
            const timestamp = new Date().toLocaleString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;

            syncLog.unshift(logEntry);
            if (syncLog.length > 100) {
                syncLog = syncLog.slice(0, 100); // الاحتفاظ بآخر 100 سجل
            }

            updateLogDisplay();
        }

        // تحديث عرض السجل
        function updateLogDisplay() {
            const logSection = document.getElementById('syncLog');
            logSection.innerHTML = syncLog.join('<br>') || 'لا يوجد سجل متاح';
        }

        // تحديث السجل
        function refreshLog() {
            updateLogDisplay();
            showMessage('تم تحديث السجل', 'success');
        }

        // مسح السجل
        function clearLog() {
            syncLog = [];
            updateLogDisplay();
            showMessage('تم مسح السجل', 'success');
        }

        // تحميل السجل
        function downloadLog() {
            const logContent = syncLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sync-log-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // اختبارات سريعة
        async function quickTestUsers() {
            const testUsers = {
                'test_user_1': {
                    username: 'test1',
                    fullName: 'مستخدم اختبار 1',
                    role: 'user',
                    timestamp: new Date().toISOString()
                },
                'test_user_2': {
                    username: 'test2',
                    fullName: 'مستخدم اختبار 2',
                    role: 'admin',
                    timestamp: new Date().toISOString()
                }
            };

            const success = await syncClient.syncUsers(testUsers);
            showTestResult('اختبار المستخدمين', success);
        }

        async function quickTestSessions() {
            const testSessions = {
                'session_1': {
                    userId: 'test_user_1',
                    loginTime: new Date().toISOString(),
                    ipAddress: '*************'
                }
            };

            const success = await syncClient.syncSessions(testSessions);
            showTestResult('اختبار الجلسات', success);
        }

        async function quickTestActivities() {
            const testActivities = {
                'activity_1': {
                    userId: 'test_user_1',
                    action: 'login',
                    timestamp: new Date().toISOString(),
                    details: 'تسجيل دخول اختبار'
                }
            };

            const success = await syncClient.syncActivities(testActivities);
            showTestResult('اختبار الأنشطة', success);
        }

        async function stressTest() {
            const results = document.getElementById('quickTestResults');
            results.innerHTML = '<div class="status-value">جاري تشغيل اختبار الضغط...</div>';

            let successCount = 0;
            const totalTests = 10;

            for (let i = 0; i < totalTests; i++) {
                const testData = {
                    [`stress_test_${i}`]: {
                        index: i,
                        timestamp: new Date().toISOString(),
                        data: `بيانات اختبار الضغط ${i}`
                    }
                };

                const success = await syncClient.syncData('stress_test', testData);
                if (success) successCount++;

                // تأخير قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            const successRate = (successCount / totalTests * 100).toFixed(1);
            results.innerHTML = `
                <div class="status-value">
                    اختبار الضغط مكتمل:<br>
                    النجاح: ${successCount}/${totalTests} (${successRate}%)
                </div>
            `;
        }

        // عرض نتيجة الاختبار
        function showTestResult(testName, success) {
            const results = document.getElementById('quickTestResults');
            const status = success ? '✅ نجح' : '❌ فشل';
            const className = success ? 'status-online' : 'status-offline';

            results.innerHTML = `
                <div class="status-value">
                    <span class="${className}">${testName}: ${status}</span>
                </div>
            `;
        }

        // عرض رسالة
        function showMessage(message, type = 'info') {
            const colors = {
                success: '#d4edda',
                error: '#f8d7da',
                info: '#d1ecf1'
            };

            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${colors[type] || colors.info};
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1001;
                max-width: 300px;
                font-size: 14px;
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 3000);
        }
    </script>
</body>
</html>
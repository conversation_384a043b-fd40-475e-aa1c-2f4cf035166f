#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحل النهائي لمشاكل المزامنة
Final Sync Solution
"""

import subprocess
import time
import sys
import os
import socket
import webbrowser
import json
import threading
from datetime import datetime

class FinalSyncSolution:
    def __init__(self):
        self.local_ip = self.get_local_ip()
        self.main_server_process = None
        self.sync_server_process = None
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def print_header(self):
        """طباعة رأس النظام"""
        print("=" * 70)
        print("الحل النهائي لمشاكل المزامنة")
        print("Final Sync Solution")
        print("=" * 70)
        print(f"عنوان IP المحلي: {self.local_ip}")
        print()
    
    def setup_data_directory(self):
        """إعداد مجلد البيانات"""
        print("إعداد مجلد البيانات...")
        
        # إنشاء المجلدات
        os.makedirs("data", exist_ok=True)
        os.makedirs("data/backups", exist_ok=True)
        
        # إنشاء الملفات الأساسية
        files = {
            "data/users.json": {},
            "data/events.json": [],
            "data/activities.json": [],
            "data/sync_log.json": []
        }
        
        for file_path, default_content in files.items():
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, ensure_ascii=False, indent=2)
        
        print("تم إعداد مجلد البيانات بنجاح")
        print()
    
    def kill_existing_processes(self):
        """إنهاء العمليات الموجودة"""
        print("إنهاء العمليات الموجودة...")
        
        import platform
        
        ports_to_check = [8000, 8001]
        
        for port in ports_to_check:
            try:
                if platform.system() == "Windows":
                    # Windows
                    result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
                    lines = result.stdout.split('\n')
                    
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                try:
                                    subprocess.run(['taskkill', '/F', '/PID', pid], 
                                                 capture_output=True, shell=True)
                                    print(f"   تم إنهاء العملية على المنفذ {port}")
                                except:
                                    pass
                else:
                    # Linux/Mac
                    try:
                        result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                              capture_output=True, text=True)
                        if result.stdout.strip():
                            pids = result.stdout.strip().split('\n')
                            for pid in pids:
                                subprocess.run(['kill', '-9', pid], capture_output=True)
                                print(f"   تم إنهاء العملية على المنفذ {port}")
                    except:
                        pass
            except Exception as e:
                print(f"   خطأ في فحص المنفذ {port}: {e}")
        
        time.sleep(2)
        print()
    
    def start_main_server(self):
        """تشغيل الخادم الرئيسي"""
        print("تشغيل الخادم الرئيسي...")
        
        try:
            self.main_server_process = subprocess.Popen([
                sys.executable, 'start-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print("   تم تشغيل الخادم الرئيسي على المنفذ 8000")
            return True
            
        except Exception as e:
            print(f"   فشل في تشغيل الخادم الرئيسي: {e}")
            return False
    
    def start_sync_server(self):
        """تشغيل خادم المزامنة المبسط"""
        print("تشغيل خادم المزامنة...")
        
        try:
            self.sync_server_process = subprocess.Popen([
                sys.executable, 'sync-server-simple.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print("   تم تشغيل خادم المزامنة على المنفذ 8001")
            return True
            
        except Exception as e:
            print(f"   فشل في تشغيل خادم المزامنة: {e}")
            return False
    
    def wait_for_servers(self):
        """انتظار تشغيل الخوادم"""
        print("انتظار تشغيل الخوادم...")
        
        def wait_for_port(port, name):
            for i in range(15):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(1)
                        result = s.connect_ex(('localhost', port))
                        if result == 0:
                            print(f"   {name} جاهز")
                            return True
                except:
                    pass
                time.sleep(1)
            print(f"   {name} قد لا يكون جاهزاً")
            return False
        
        main_ready = wait_for_port(8000, "الخادم الرئيسي")
        sync_ready = wait_for_port(8001, "خادم المزامنة")
        
        print()
        return main_ready, sync_ready
    
    def test_sync_functionality(self):
        """اختبار وظائف المزامنة"""
        print("اختبار وظائف المزامنة...")
        
        try:
            import requests
            
            # اختبار حالة خادم المزامنة
            response = requests.get(f"http://localhost:8001/api/sync/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print("   خادم المزامنة يستجيب بنجاح")
                print(f"   المستخدمين: {status.get('users_count', 0)}")
                print(f"   الأحداث: {status.get('events_count', 0)}")
                print(f"   الأنشطة: {status.get('activities_count', 0)}")
                
                # اختبار إرسال البيانات
                test_data = {
                    "users": {},
                    "events": [{
                        "id": f"test-{int(time.time())}",
                        "title": "اختبار المزامنة",
                        "timestamp": datetime.now().isoformat()
                    }],
                    "activities": []
                }
                
                sync_response = requests.post(
                    f"http://localhost:8001/api/sync/full",
                    json=test_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
                
                if sync_response.status_code == 200:
                    print("   اختبار إرسال البيانات نجح")
                    return True
                else:
                    print(f"   فشل في إرسال البيانات: {sync_response.status_code}")
                    return False
            else:
                print(f"   خطأ في خادم المزامنة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   خطأ في اختبار المزامنة: {e}")
            return False
    
    def create_access_links(self):
        """إنشاء ملف روابط الوصول"""
        content = f"""# روابط الوصول لنظام إدارة أمن المعلومات
# تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## الوصول المحلي
الصفحة الرئيسية: http://localhost:8000
تسجيل الدخول: http://localhost:8000/login.html
خادم المزامنة: http://localhost:8001/api/sync/status

## الوصول من الشبكة
الصفحة الرئيسية: http://{self.local_ip}:8000
تسجيل الدخول: http://{self.local_ip}:8000/login.html
خادم المزامنة: http://{self.local_ip}:8001/api/sync/status

## بيانات الدخول
اسم المستخدم: admin
كلمة المرور: admin123

## مستخدمون إضافيون
• analyst / analyst123 (محلل أمني)
• operator / operator123 (مشغل)

## اختبار المزامنة
صفحة الاختبار: http://localhost:8000/sync-test.html

## أدوات التشخيص
python sync-diagnosis.py      # تشخيص شامل
python sync-fix-advanced.py   # إصلاح متقدم
python test-network-sync.py   # اختبار الشبكة

## إعدادات جدار الحماية (Windows)
netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport=8001

## إعدادات جدار الحماية (Linux)
sudo ufw allow 8000/tcp comment "ISMS Main Server"
sudo ufw allow 8001/tcp comment "ISMS Sync Server"
"""
        
        try:
            with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
                f.write(content)
            print("تم إنشاء ملف ACCESS-LINKS.txt")
        except Exception as e:
            print(f"خطأ في إنشاء ملف الروابط: {e}")
    
    def open_browser(self):
        """فتح المتصفح"""
        print("فتح المتصفح...")
        
        try:
            webbrowser.open(f"http://localhost:8000/login.html")
            print("تم فتح المتصفح بنجاح")
        except Exception as e:
            print(f"فشل في فتح المتصفح: {e}")
        
        print()
    
    def show_final_info(self, sync_working):
        """عرض المعلومات النهائية"""
        print("=" * 70)
        print("تم تشغيل النظام بنجاح!")
        print("=" * 70)
        
        print("روابط الوصول:")
        print(f"   محلي: http://localhost:8000/login.html")
        print(f"   شبكة: http://{self.local_ip}:8000/login.html")
        
        print("\nبيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        print("\nحالة المزامنة:")
        if sync_working:
            print("   المزامنة تعمل بنجاح!")
            print("   يمكن للأجهزة الأخرى في الشبكة الوصول والمزامنة")
        else:
            print("   المزامنة تحتاج لإعداد إضافي")
            print("   تحقق من جدار الحماية وإعدادات الشبكة")
        
        print("\nميزات النظام:")
        print("   مزامنة تلقائية كل 30 ثانية")
        print("   مشاركة البيانات عبر الشبكة")
        print("   نسخ احتياطي تلقائي")
        print("   تتبع الحركات والأنشطة")
        
        print("\nملفات مهمة:")
        print("   ACCESS-LINKS.txt - جميع روابط الوصول")
        print("   sync-test.html - صفحة اختبار المزامنة")
        print("   data/ - مجلد البيانات والنسخ الاحتياطية")
        
        print("\nللإيقاف: اضغط Ctrl+C")
        print("=" * 70)
    
    def cleanup(self):
        """تنظيف العمليات"""
        print("\nإيقاف النظام...")
        
        if self.main_server_process:
            try:
                self.main_server_process.terminate()
                print("تم إيقاف الخادم الرئيسي")
            except:
                pass
        
        if self.sync_server_process:
            try:
                self.sync_server_process.terminate()
                print("تم إيقاف خادم المزامنة")
            except:
                pass
        
        print("تم إيقاف النظام بنجاح")
    
    def run_complete_solution(self):
        """تشغيل الحل الكامل"""
        self.print_header()
        
        try:
            # إعداد النظام
            self.setup_data_directory()
            self.kill_existing_processes()
            
            # تشغيل الخوادم
            main_started = self.start_main_server()
            sync_started = self.start_sync_server()
            
            if not (main_started and sync_started):
                print("فشل في تشغيل بعض الخوادم")
                return False
            
            # انتظار تشغيل الخوادم
            main_ready, sync_ready = self.wait_for_servers()
            
            # اختبار المزامنة
            sync_working = False
            if sync_ready:
                sync_working = self.test_sync_functionality()
            
            # إنشاء ملفات الوصول
            self.create_access_links()
            
            # فتح المتصفح
            self.open_browser()
            
            # عرض المعلومات النهائية
            self.show_final_info(sync_working)
            
            # انتظار الإيقاف
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"خطأ في تشغيل النظام: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """الدالة الرئيسية"""
    solution = FinalSyncSolution()
    
    try:
        success = solution.run_complete_solution()
        return 0 if success else 1
    except Exception as e:
        print(f"خطأ عام: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم بسيط لنظام إدارة أمن المعلومات
Simple Server for ISMS
"""

import http.server
import socketserver
import webbrowser
import socket
import os
import sys

def find_free_port(start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

class SimpleHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(301)
            self.send_header('Location', '/login.html')
            self.end_headers()
            return
        super().do_GET()

def main():
    # البحث عن منفذ متاح
    port = find_free_port(8000)
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        sys.exit(1)
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    
    print("🔐 نظام إدارة أمن المعلومات")
    print("=" * 50)
    print(f"🌐 الخادم يعمل على المنفذ: {port}")
    print()
    print("🏠 الوصول المحلي:")
    print(f"   📍 http://localhost:{port}/login.html")
    print(f"   📍 http://127.0.0.1:{port}/login.html")
    print()
    print("🌍 الوصول من الشبكة:")
    print(f"   📍 http://{local_ip}:{port}/login.html")
    print()
    print("🔑 بيانات الدخول:")
    print("   👤 اسم المستخدم: admin")
    print("   🔒 كلمة المرور: admin123")
    print()
    print("=" * 50)
    print("⚠️  للإيقاف: اضغط Ctrl+C")
    print()
    
    # فتح المتصفح تلقائياً
    try:
        webbrowser.open(f'http://localhost:{port}/login.html')
        print("🌐 تم فتح المتصفح تلقائياً")
    except:
        print("⚠️  يرجى فتح المتصفح يدوياً")
    
    print()
    print("🚀 الخادم جاهز...")
    print()
    
    # تشغيل الخادم
    try:
        with socketserver.TCPServer(("", port), SimpleHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()

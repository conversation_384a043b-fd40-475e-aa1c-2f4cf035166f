#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بدء نظام المزامنة الشامل
Start Complete Sync System
"""

import os
import sys
import time
import socket
import subprocess
import webbrowser
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_port_available(port):
    """فحص إذا كان المنفذ متاح"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('0.0.0.0', port))
            return True
    except OSError:
        return False

def start_web_server(port=8000):
    """بدء خادم الويب"""
    try:
        if not check_port_available(port):
            print(f"⚠️  المنفذ {port} مستخدم بالفعل")
            return None
        
        print(f"🌐 بدء خادم الويب على المنفذ {port}...")
        cmd = [sys.executable, '-m', 'http.server', str(port), '--bind', '0.0.0.0']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(2)
        
        if process.poll() is None:
            print(f"✅ خادم الويب يعمل على المنفذ {port}")
            return process
        else:
            print(f"❌ فشل في بدء خادم الويب")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بدء خادم الويب: {e}")
        return None

def start_sync_server(port=8001):
    """بدء خادم المزامنة"""
    try:
        if not check_port_available(port):
            print(f"⚠️  المنفذ {port} مستخدم بالفعل")
            return None
        
        print(f"🔄 بدء خادم المزامنة على المنفذ {port}...")
        cmd = [sys.executable, 'simple-sync-server.py']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(3)
        
        if process.poll() is None:
            print(f"✅ خادم المزامنة يعمل على المنفذ {port}")
            return process
        else:
            print(f"❌ فشل في بدء خادم المزامنة")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بدء خادم المزامنة: {e}")
        return None

def create_sync_integration():
    """إنشاء تكامل المزامنة مع الصفحات الموجودة"""
    
    integration_code = '''
// تكامل المزامنة مع النظام الموجود
(function() {
    // تهيئة عميل المزامنة
    let syncClient = null;
    
    // تهيئة المزامنة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            const currentHost = window.location.hostname;
            const syncServerUrl = `http://${currentHost}:8001`;
            
            // إنشاء عميل المزامنة
            if (typeof NetworkSyncClient !== 'undefined') {
                syncClient = new NetworkSyncClient(syncServerUrl);
                console.log('✅ تم تهيئة المزامنة للصفحة');
                
                // مزامنة بيانات المستخدمين
                if (typeof systemUsers !== 'undefined') {
                    await syncClient.syncUsers(systemUsers);
                }
                
                // مزامنة الجلسات
                if (typeof userSessions !== 'undefined') {
                    await syncClient.syncSessions(userSessions);
                }
                
                // إعداد مراقبة التغييرات
                setupSyncMonitoring();
            }
        } catch (error) {
            console.warn('تعذر تهيئة المزامنة:', error);
        }
    });
    
    // مراقبة التغييرات في البيانات
    function setupSyncMonitoring() {
        if (!syncClient) return;
        
        // مراقبة تغييرات المستخدمين
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            originalSetItem.call(this, key, value);
            
            if (key === 'systemUsers') {
                try {
                    const users = JSON.parse(value);
                    syncClient.syncUsers(users);
                } catch (error) {
                    console.error('خطأ في مزامنة المستخدمين:', error);
                }
            } else if (key === 'userSessions') {
                try {
                    const sessions = JSON.parse(value);
                    syncClient.syncSessions(sessions);
                } catch (error) {
                    console.error('خطأ في مزامنة الجلسات:', error);
                }
            }
        };
        
        // مزامنة دورية
        setInterval(async () => {
            try {
                // الحصول على أحدث البيانات
                const users = await syncClient.getUsers();
                const sessions = await syncClient.getSessions();
                
                // تحديث البيانات المحلية
                if (users && Object.keys(users).length > 0) {
                    localStorage.setItem('systemUsers', JSON.stringify(users));
                }
                
                if (sessions && Object.keys(sessions).length > 0) {
                    localStorage.setItem('userSessions', JSON.stringify(sessions));
                }
                
            } catch (error) {
                console.error('خطأ في المزامنة الدورية:', error);
            }
        }, 30000); // كل 30 ثانية
    }
    
    // تصدير للاستخدام العام
    window.syncClient = syncClient;
})();
'''
    
    with open('sync-integration.js', 'w', encoding='utf-8') as f:
        f.write(integration_code)
    
    print("✅ تم إنشاء ملف تكامل المزامنة: sync-integration.js")

def update_existing_pages():
    """تحديث الصفحات الموجودة لتشمل المزامنة"""
    
    pages_to_update = [
        'index.html',
        'login-fixed.html',
        'enhanced-dashboard.html',
        'login-test.html',
        'network-test.html'
    ]
    
    updated_pages = []
    
    for page in pages_to_update:
        if os.path.exists(page):
            try:
                with open(page, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إضافة سكريبت المزامنة إذا لم يكن موجوداً
                if 'network-sync-client.js' not in content:
                    # البحث عن مكان إدراج السكريبت
                    if '</head>' in content:
                        content = content.replace('</head>', 
                            '    <script src="network-sync-client.js"></script>\n    <script src="sync-integration.js"></script>\n</head>')
                    elif '</body>' in content:
                        content = content.replace('</body>', 
                            '    <script src="network-sync-client.js"></script>\n    <script src="sync-integration.js"></script>\n</body>')
                
                # حفظ الملف المحدث
                with open(page, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                updated_pages.append(page)
                
            except Exception as e:
                print(f"❌ خطأ في تحديث {page}: {e}")
    
    if updated_pages:
        print(f"✅ تم تحديث {len(updated_pages)} صفحة لتشمل المزامنة:")
        for page in updated_pages:
            print(f"   • {page}")
    
    return updated_pages

def main():
    """الوظيفة الرئيسية"""
    
    local_ip = get_local_ip()
    
    print("🔄 بدء نظام المزامنة الشامل")
    print("=" * 60)
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء تكامل المزامنة
    create_sync_integration()
    
    # تحديث الصفحات الموجودة
    updated_pages = update_existing_pages()
    
    # بدء خادم الويب
    web_server = start_web_server(8000)
    if not web_server:
        print("❌ فشل في بدء خادم الويب")
        return
    
    # بدء خادم المزامنة
    sync_server = start_sync_server(8001)
    if not sync_server:
        print("❌ فشل في بدء خادم المزامنة")
        web_server.terminate()
        return
    
    print(f"\n🎉 تم بدء نظام المزامنة بنجاح!")
    print(f"\n🌐 الروابط المتاحة:")
    print(f"   • الصفحة الرئيسية: http://{local_ip}:8000/index.html")
    print(f"   • تسجيل الدخول المحسن: http://{local_ip}:8000/login-fixed.html")
    print(f"   • اختبار المزامنة: http://{local_ip}:8000/sync-test-page.html")
    print(f"   • لوحة التحكم: http://{local_ip}:8000/enhanced-dashboard.html")
    
    print(f"\n🔄 API المزامنة:")
    print(f"   • تسجيل العميل: http://{local_ip}:8001/api/sync/register")
    print(f"   • مزامنة البيانات: http://{local_ip}:8001/api/sync/data")
    print(f"   • حالة العملاء: http://{local_ip}:8001/api/sync/status")
    print(f"   • الإحصائيات: http://{local_ip}:8001/api/sync/stats")
    
    print(f"\n📱 للوصول من الأجهزة الأخرى:")
    print(f"   استخدم عنوان IP: {local_ip}")
    print(f"   مثال: http://{local_ip}:8000/sync-test-page.html")
    
    print(f"\n💡 الميزات الجديدة:")
    print(f"   ✅ مزامنة تلقائية للبيانات بين الأجهزة")
    print(f"   ✅ مراقبة حالة الاتصال في الوقت الفعلي")
    print(f"   ✅ تكامل مع النظام الموجود")
    print(f"   ✅ صفحة اختبار شاملة للمزامنة")
    
    # فتح المتصفح
    print(f"\n🌐 فتح صفحات الاختبار...")
    try:
        time.sleep(2)
        webbrowser.open(f'http://localhost:8000/sync-test-page.html')
        time.sleep(1)
        webbrowser.open(f'http://localhost:8000/login-fixed.html')
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")
    
    print(f"\n✅ النظام جاهز للاستخدام!")
    print(f"🛑 اضغط Ctrl+C لإيقاف النظام")
    print("-" * 60)
    
    try:
        # انتظار إيقاف النظام
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n🛑 إيقاف النظام...")
        
        # إيقاف الخوادم
        if web_server:
            web_server.terminate()
            print("✅ تم إيقاف خادم الويب")
        
        if sync_server:
            sync_server.terminate()
            print("✅ تم إيقاف خادم المزامنة")
        
        print("👋 تم إيقاف نظام المزامنة بنجاح")

if __name__ == "__main__":
    main()

// Security Events Management System
/**
 * Security Events Management System
 * Uses Gregorian Calendar (التقويم الإفرنجي) for all date operations
 */
class SecurityEventsManager {
    constructor() {
        try {
            this.events = JSON.parse(localStorage.getItem('securityEvents')) || [];
            this.eventTypes = JSON.parse(localStorage.getItem('eventTypes')) || [];
            this.currentEventId = parseInt(localStorage.getItem('currentEventId')) || 1;
            this.currentTheme = localStorage.getItem('theme') || 'light';
            this.authManager = null;
            this.currentUser = null;
            this.currentEditingEventTypeKey = null;

            this.domElements = {};

            this.initializeDefaultEventTypes();
            this.init();
        } catch (error) {
            console.error('Error initializing SecurityEventsManager:', error);
            // Reset to defaults if localStorage is corrupted
            this.events = [];
            this.eventTypes = [];
            this.currentEventId = 1;
            this.currentTheme = 'light';
            this.currentUser = null;
            this.initializeDefaultEventTypes();
            this.init();
        }
    }

    cacheDOMElements() {
        this.domElements = {
            // General
            themeToggle: document.getElementById('themeToggle'),
            userMenu: document.getElementById('userMenu'),
            currentUserName: document.getElementById('currentUserName'),
            // Event Form
            eventForm: document.getElementById('eventForm'),
            eventSerial: document.getElementById('eventSerial'),
            eventDate: document.getElementById('eventDate'),
            // Event Log
            eventsTableBody: document.getElementById('eventsTableBody'),
            searchEvents: document.getElementById('searchEvents'),
            filterSeverity: document.getElementById('filterSeverity'),
            // Modals
            eventModal: document.getElementById('eventModal'),
            modalBody: document.getElementById('modalBody'),
            modalClose: document.getElementById('modalClose'),
            closeModalBtn: document.getElementById('closeModalBtn'),
            deleteEventBtn: document.getElementById('deleteEventBtn'),
            // Analytics
            totalEvents: document.getElementById('totalEvents'),
            criticalEvents: document.getElementById('criticalEvents'),
            highEvents: document.getElementById('highEvents'),
            openEvents: document.getElementById('openEvents'),
        };
    }

    // Generate automatic serial number in format SEC-001
    generateSerialNumber() {
        const serialNumber = `SEC-${this.currentEventId.toString().padStart(3, '0')}`;
        return serialNumber;
    }

    // Get next available serial number
    getNextSerialNumber() {
        // Check if there are existing events to determine the next ID
        if (this.events.length > 0) {
            // Find the highest existing serial number
            let maxId = 0;
            this.events.forEach(event => {
                if (event.serial && event.serial.startsWith('SEC-')) {
                    const idPart = parseInt(event.serial.split('-')[1]);
                    if (!isNaN(idPart) && idPart > maxId) {
                        maxId = idPart;
                    }
                }
            });
            this.currentEventId = maxId + 1;
        }

        const nextSerial = this.generateSerialNumber();
        this.currentEventId++;
        localStorage.setItem('currentEventId', this.currentEventId.toString());
        return nextSerial;
    }

    // Migrate existing events to new serial number format
    migrateExistingEvents() {
        let hasChanges = false;
        let serialCounter = 1;

        // Sort events by creation date to maintain chronological order
        this.events.sort((a, b) => new Date(a.createdAt || 0) - new Date(b.createdAt || 0));

        this.events.forEach(event => {
            // If event doesn't have the new 'serial' field, create one
            if (!event.serial) {
                // Use existing serialNumber if available, otherwise generate new one
                if (event.serialNumber && event.serialNumber.startsWith('SEC-')) {
                    event.serial = event.serialNumber;
                } else {
                    event.serial = `SEC-${serialCounter.toString().padStart(3, '0')}`;
                    serialCounter++;
                }
                hasChanges = true;
            } else {
                // Extract number from existing serial to update counter
                const match = event.serial.match(/SEC-(\d+)/);
                if (match) {
                    const num = parseInt(match[1]);
                    if (num >= serialCounter) {
                        serialCounter = num + 1;
                    }
                }
            }
        });

        // Update currentEventId to continue from the highest number
        this.currentEventId = serialCounter;
        localStorage.setItem('currentEventId', this.currentEventId.toString());

        // Save changes if any were made
        if (hasChanges) {
            this.saveToStorage();
        }
    }

    // Initialize default event types if none exist
    initializeDefaultEventTypes() {
        if (this.eventTypes.length === 0) {
            this.eventTypes = [
                {
                    key: 'malware',
                    label: 'برمجية خبيثة',
                    description: 'فيروسات، أحصنة طروادة، برامج التجسس',
                    icon: 'fas fa-virus',
                    color: '#dc2626'
                },
                {
                    key: 'phishing',
                    label: 'تصيد إلكتروني',
                    description: 'محاولات سرقة البيانات عبر رسائل مزيفة',
                    icon: 'fas fa-fishing',
                    color: '#ea580c'
                },
                {
                    key: 'intrusion',
                    label: 'اختراق',
                    description: 'محاولات الوصول غير المصرح به للأنظمة',
                    icon: 'fas fa-user-secret',
                    color: '#7c3aed'
                },
                {
                    key: 'data-breach',
                    label: 'تسريب بيانات',
                    description: 'تسريب أو فقدان البيانات الحساسة',
                    icon: 'fas fa-database',
                    color: '#dc2626'
                },
                {
                    key: 'unauthorized-access',
                    label: 'وصول غير مصرح',
                    description: 'محاولات الوصول بدون تصريح',
                    icon: 'fas fa-shield-alt',
                    color: '#d97706'
                },
                {
                    key: 'system-failure',
                    label: 'فشل النظام',
                    description: 'أعطال في الأنظمة الأمنية',
                    icon: 'fas fa-server',
                    color: '#6b7280'
                }
            ];
            this.saveEventTypesToStorage();
        }
    }

    init() {
        // Check authentication first
        if (!this.checkAuthentication()) {
            // Add a small delay to avoid redirect loops
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 100);
            return;
        }

        this.ensureGregorianDates(); // Ensure all dates use Gregorian calendar
        this.migrateExistingEvents(); // Migrate existing events to new serial format
        this.cacheDOMElements();
        this.setupEventListeners();
        this.applyTheme();
        this.setupUserInterface();
        this.setupLogo();
        this.setupFloatingActionButton();
        this.updateEventTypeDropdown();
        this.updateIncidentTypeDropdown(); // Update incident types for post-incident review
        this.renderEvents();
        this.setCurrentDateTime();
        this.updateStatistics();
        this.initializeCharts();
        this.updateRiskAnalysis();
    }

    // Check if user is authenticated
    checkAuthentication() {
        // Skip authentication check if we're on login page
        if (window.location.pathname.includes('login.html')) {
            return true;
        }

        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        const session = userSessions[sessionId];

        if (!session) return false;

        // Check if session is expired
        if (new Date() > new Date(session.expiresAt)) {
            this.clearSession(sessionId);
            return false;
        }

        // Load current user
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        this.currentUser = users[session.username];

        return this.currentUser && this.currentUser.isActive;
    }

    // Clear expired session
    clearSession(sessionId) {
        const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
        delete userSessions[sessionId];
        localStorage.setItem('userSessions', JSON.stringify(userSessions));
        localStorage.removeItem('currentSession');
    }

    // Setup user interface based on permissions
    setupUserInterface() {
        if (!this.currentUser) {
            console.error('No current user found');
            return;
        }

        // Update user name in header
        if (this.domElements.currentUserName) {
            this.domElements.currentUserName.textContent = this.currentUser.fullName;
        }

        // Set initial serial number and current date/time
        this.setNextSerialNumber();
        this.setCurrentDateTime();

        // Show/hide sections based on permissions
        this.updateUIPermissions();
    }

    // Update UI based on user permissions
    updateUIPermissions() {
        const user = this.currentUser;
        if (!user) return;

        // Show admin-only sections
        if (user.permissions.includes('manage_users')) {
            document.querySelectorAll('.admin-only').forEach(el => {
                el.style.display = 'flex';
            });
        } else {
            document.querySelectorAll('.admin-only').forEach(el => {
                el.style.display = 'none';
            });
        }

        // Disable write operations if user doesn't have write permission
        if (!user.permissions.includes('write')) {
            const eventForm = document.getElementById('eventForm');
            if (eventForm) {
                eventForm.style.display = 'none';
            }
        }

        // Hide edit/delete buttons if user doesn't have permissions
        setTimeout(() => {
            if (!user.permissions.includes('write')) {
                document.querySelectorAll('.edit-btn').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
            if (!user.permissions.includes('delete')) {
                document.querySelectorAll('.delete-btn').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }, 100);

        // Hide analytics if user doesn't have permission
        if (!user.permissions.includes('view_analytics')) {
            const analyticsBtn = document.querySelector('[data-section="analytics"]');
            const riskBtn = document.querySelector('[data-section="risk-analysis"]');
            if (analyticsBtn) analyticsBtn.style.display = 'none';
            if (riskBtn) riskBtn.style.display = 'none';
        }
    }

    // Setup logo with fallback
    setupLogo() {
        const logoImages = document.querySelectorAll('.logo-image');
        logoImages.forEach(img => {
            img.addEventListener('error', function() {
                // Hide the image and show the icon fallback
                this.style.display = 'none';
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'inline-block';
                }
            });

            img.addEventListener('load', function() {
                // Hide the icon fallback when image loads successfully
                const icon = this.nextElementSibling;
                if (icon && icon.tagName === 'I') {
                    icon.style.display = 'none';
                }
            });
        });
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Theme toggle
        this.domElements.themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });

        // Event form submission
        this.domElements.eventForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addEvent();
        });

        // Search and filter
        this.domElements.searchEvents.addEventListener('input', (e) => {
            this.filterEvents();
        });

        this.domElements.filterSeverity.addEventListener('change', (e) => {
            this.filterEvents();
        });

        // Modal controls
        this.domElements.modalClose.addEventListener('click', () => {
            this.closeModal();
        });

        this.domElements.closeModalBtn.addEventListener('click', () => {
            this.closeModal();
        });

        this.domElements.deleteEventBtn.addEventListener('click', () => {
            this.deleteCurrentEvent();
        });

        // Close modal when clicking outside
        this.domElements.eventModal.addEventListener('click', (e) => {
            if (e.target.id === 'eventModal') {
                this.closeModal();
            }
        });

        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSection(e.target.closest('.nav-btn').dataset.section);
            });
        });

        // User management
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.showUserModal();
            });
        }

        const userModalClose = document.getElementById('userModalClose');
        if (userModalClose) {
            userModalClose.addEventListener('click', () => {
                this.closeUserModal();
            });
        }

        const cancelUserBtn = document.getElementById('cancelUserBtn');
        if (cancelUserBtn) {
            cancelUserBtn.addEventListener('click', () => {
                this.closeUserModal();
            });
        }

        const saveUserBtn = document.getElementById('saveUserBtn');
        if (saveUserBtn) {
            saveUserBtn.addEventListener('click', () => {
                this.saveUser();
            });
        }

        document.addEventListener('click', (e) => {
            const target = e.target;

            // Logout buttons
            if (target.closest('#logoutBtn') || target.closest('#logoutBtnFooter')) {
                e.preventDefault();
                e.stopPropagation();
                this.simpleLogout();
                return;
            }

            // User menu dropdown
            const userMenu = document.getElementById('userMenu');
            if (userMenu && !userMenu.contains(target)) {
                userMenu.classList.remove('active');
            }
        });

        // Close user menu with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const userMenu = document.getElementById('userMenu');
                if (userMenu) {
                    userMenu.classList.remove('active');
                }
            }
        });

        // Role change handler
        const userRole = document.getElementById('userRole');
        if (userRole) {
            userRole.addEventListener('change', () => {
                this.updatePermissionsByRole();
            });
        }

        // User profile functionality
        const userProfileBtn = document.getElementById('userProfileBtn');
        if (userProfileBtn) {
            userProfileBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showUserProfile();
            });
        }

        // Profile modal close
        const userProfileModalClose = document.getElementById('userProfileModalClose');
        if (userProfileModalClose) {
            userProfileModalClose.addEventListener('click', () => {
                this.closeUserProfile();
            });
        }

        // Profile form submission
        const profileInfoForm = document.getElementById('profileInfoForm');
        if (profileInfoForm) {
            profileInfoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateProfile();
            });
        }

        // Change password form submission
        const changePasswordForm = document.getElementById('changePasswordForm');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.changePassword();
            });
        }

        // Tab switching
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab(btn.dataset.tab);
            });
        });

        // Password strength checker
        const newPasswordInput = document.getElementById('newPassword');
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', () => {
                this.checkPasswordStrength(newPasswordInput.value);
            });
        }

        // Event Types Management
        const addEventTypeBtn = document.getElementById('addEventTypeBtn');
        if (addEventTypeBtn) {
            addEventTypeBtn.addEventListener('click', () => {
                this.showEventTypeModal();
            });
        }

        // Post Incident Review Form
        const postIncidentForm = document.getElementById('postIncidentForm');
        if (postIncidentForm) {
            postIncidentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.savePostIncidentReview();
            });
        }

        // Generate Report Button
        const generateReportBtn = document.getElementById('generateReportBtn');
        if (generateReportBtn) {
            generateReportBtn.addEventListener('click', () => {
                this.generatePostIncidentReport();
            });
        }

        // View Reviews Button
        const viewReviewsBtn = document.getElementById('viewReviewsBtn');
        if (viewReviewsBtn) {
            viewReviewsBtn.addEventListener('click', () => {
                this.showSavedReviews();
            });
        }

        // Back to Form Button
        const backToFormBtn = document.getElementById('backToFormBtn');
        if (backToFormBtn) {
            backToFormBtn.addEventListener('click', () => {
                this.showReviewForm();
            });
        }

        // Review Modal Events
        const reviewModalClose = document.getElementById('reviewModalClose');
        if (reviewModalClose) {
            reviewModalClose.addEventListener('click', () => {
                this.closeReviewModal();
            });
        }

        const closeReviewModalBtn = document.getElementById('closeReviewModalBtn');
        if (closeReviewModalBtn) {
            closeReviewModalBtn.addEventListener('click', () => {
                this.closeReviewModal();
            });
        }

        const printReviewBtn = document.getElementById('printReviewBtn');
        if (printReviewBtn) {
            printReviewBtn.addEventListener('click', () => {
                this.printReview();
            });
        }

        const editReviewFromModalBtn = document.getElementById('editReviewFromModalBtn');
        if (editReviewFromModalBtn) {
            editReviewFromModalBtn.addEventListener('click', () => {
                this.editReviewFromModal();
            });
        }

        // Excel Export Button
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', () => {
                this.exportToExcel();
            });
        }

        // Excel Import Button
        const importExcelBtn = document.getElementById('importExcelBtn');
        if (importExcelBtn) {
            importExcelBtn.addEventListener('click', () => {
                document.getElementById('excelFileInput').click();
            });
        }

        // Excel File Input
        const excelFileInput = document.getElementById('excelFileInput');
        if (excelFileInput) {
            excelFileInput.addEventListener('change', (e) => {
                this.importFromExcel(e.target.files[0]);
            });
        }

        // Import Event Button for Post Incident Review
        const importEventBtn = document.getElementById('importEventBtn');
        if (importEventBtn) {
            importEventBtn.addEventListener('click', () => {
                this.importEventToReview();
            });
        }

        // Enter key support for event serial import
        const eventSerialImport = document.getElementById('eventSerialImport');
        if (eventSerialImport) {
            eventSerialImport.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.importEventToReview();
                }
            });

            // Add input event for real-time validation
            eventSerialImport.addEventListener('input', (e) => {
                this.validateSerialInput(e.target);
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.import-input-container')) {
                    this.hideSuggestions();
                }
            });

            // Handle keyboard navigation in suggestions
            eventSerialImport.addEventListener('keydown', (e) => {
                this.handleSuggestionNavigation(e);
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }

    setCurrentDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        if (this.domElements.eventDate) {
            this.domElements.eventDate.value = localDateTime;
        }
    }

    // Set next serial number in the form field
    setNextSerialNumber() {
        if (this.domElements.eventSerial) {
            // Preview the next serial number without incrementing the counter
            const nextSerial = `SEC-${this.currentEventId.toString().padStart(3, '0')}`;
            this.domElements.eventSerial.value = nextSerial;
            this.domElements.eventSerial.readOnly = true; // Make it read-only since it's auto-generated
        }
    }



    addEvent() {
        // Check if user has write permission
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لإضافة الأحداث', 'error');
            return;
        }

        const formData = new FormData(this.domElements.eventForm);

        // Generate automatic serial number
        const serialNumber = this.getNextSerialNumber();

        const event = {
            id: Date.now(), // Use timestamp as unique ID
            serial: serialNumber, // Use 'serial' instead of 'serialNumber' for consistency
            title: formData.get('eventTitle'),
            severity: formData.get('eventSeverity'),
            type: formData.get('eventType'),
            date: formData.get('eventDate'),
            description: formData.get('eventDescription'),
            detailedDescription: formData.get('eventDetailedDescription') || '',
            affectedSystems: formData.get('affectedSystems'),
            location: formData.get('eventLocation') || '',
            notes: formData.get('eventNotes') || '',
            responsiblePerson: formData.get('responsiblePerson'),
            directCosts: formData.get('directCosts') || '',
            indirectCosts: formData.get('indirectCosts') || '',
            status: 'open',
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser.username
        };

        this.events.unshift(event);
        this.saveToStorage();
        this.renderEvents();
        this.updateStatistics();
        this.updateCharts();
        this.updateRiskAnalysis();
        this.logActivity('event_create', `إضافة حدث أمني: ${event.title} (${serialNumber})`);
        this.showNotification(`تم حفظ الحدث بنجاح برقم ${serialNumber}`, 'success');

        // Reset form and set new serial number
        document.getElementById('eventForm').reset();
        this.setCurrentDateTime();
        this.setNextSerialNumber();
    }

    renderEvents() {
        const searchTerm = this.domElements.searchEvents.value.toLowerCase();
        const severityFilter = this.domElements.filterSeverity.value;

        let filteredEvents = this.events;

        // Apply filters
        if (searchTerm) {
            filteredEvents = filteredEvents.filter(event =>
                event.title.toLowerCase().includes(searchTerm) ||
                event.description.toLowerCase().includes(searchTerm) ||
                (event.detailedDescription && event.detailedDescription.toLowerCase().includes(searchTerm)) ||
                (event.responsiblePerson && event.responsiblePerson.toLowerCase().includes(searchTerm)) ||
                (event.affectedSystems && event.affectedSystems.toLowerCase().includes(searchTerm))
            );
        }

        if (severityFilter) {
            filteredEvents = filteredEvents.filter(event => event.severity === severityFilter);
        }

        if (filteredEvents.length === 0) {
            this.domElements.eventsTableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد أحداث</h3>
                        <p>لم يتم العثور على أحداث تطابق معايير البحث</p>
                    </td>
                </tr>
            `;
            return;
        }

        this.domElements.eventsTableBody.innerHTML = filteredEvents.map(event => `
            <tr>
                <td>${event.serial || event.serialNumber || `SEC-${String(event.id).padStart(3, '0')}`}</td>
                <td>${event.title}</td>
                <td>${this.getTypeLabel(event.type)}</td>
                <td><span class="severity-badge severity-${event.severity}">${this.getSeverityLabel(event.severity)}</span></td>
                <td>${event.responsiblePerson || 'غير محدد'}</td>
                <td>${this.formatCostsForTable(event.directCosts, event.indirectCosts)}</td>
                <td>${this.formatDate(event.date)}</td>
                <td><span class="status-badge status-${event.status}">${this.getStatusLabel(event.status)}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-btn" onclick="securityManager.viewEvent(${event.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${this.currentUser && this.currentUser.permissions.includes('write') ?
                            `<button class="action-btn edit-btn" onclick="securityManager.editEvent(${event.id})">
                                <i class="fas fa-edit"></i>
                            </button>` : ''
                        }
                        ${this.currentUser && this.currentUser.permissions.includes('delete') ?
                            `<button class="action-btn delete-btn" onclick="securityManager.deleteEvent(${event.id})">
                                <i class="fas fa-trash"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    filterEvents() {
        this.renderEvents();
    }

    viewEvent(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (!event) return;

        this.domElements.modalBody.innerHTML = `
            <div class="event-detail">
                <label>رقم التسلسل:</label>
                <div class="value">${event.serial || event.serialNumber || `SEC-${String(event.id).padStart(3, '0')}`}</div>
            </div>
            <div class="event-detail">
                <label>عنوان الحدث:</label>
                <div class="value">${event.title}</div>
            </div>
            <div class="event-detail">
                <label>نوع الحدث:</label>
                <div class="value">${this.getTypeLabel(event.type)}</div>
            </div>
            <div class="event-detail">
                <label>مستوى الخطورة:</label>
                <div class="value">
                    <span class="severity-badge severity-${event.severity}">
                        ${this.getSeverityLabel(event.severity)}
                    </span>
                </div>
            </div>
            <div class="event-detail">
                <label>الشخص المسؤول عن الحدث:</label>
                <div class="value">${event.responsiblePerson || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>تاريخ ووقت الحدث:</label>
                <div class="value">${this.formatDate(event.date)}</div>
            </div>
            <div class="event-detail">
                <label>وصف الحدث مختصر:</label>
                <div class="value">${event.description}</div>
            </div>
            <div class="event-detail">
                <label>وصف الحدث بالتفصيل:</label>
                <div class="value detailed-description">${event.detailedDescription || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>الأنظمة المتأثرة:</label>
                <div class="value">${event.affectedSystems || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>موقع الحدث:</label>
                <div class="value">${event.location || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>التكاليف المباشرة:</label>
                <div class="value">${event.directCosts || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>التكاليف الغير مباشرة:</label>
                <div class="value">${event.indirectCosts || 'غير محدد'}</div>
            </div>
            <div class="event-detail">
                <label>الحالة:</label>
                <div class="value">
                    <span class="status-badge status-${event.status}">
                        ${this.getStatusLabel(event.status)}
                    </span>
                </div>
            </div>
            <div class="event-detail">
                <label>ملاحظات إضافية:</label>
                <div class="value">${event.notes || 'لا توجد ملاحظات'}</div>
            </div>
            <div class="event-detail">
                <label>تاريخ الإنشاء:</label>
                <div class="value">${this.formatDate(event.createdAt)}</div>
            </div>
        `;

        this.currentViewingEventId = eventId;
        this.showModal();
    }

    editEvent(eventId) {
        // Check if user has write permission
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لتعديل الأحداث', 'error');
            return;
        }

        const event = this.events.find(e => e.id === eventId);
        if (!event) return;

        // Fill form with event data
        this.domElements.eventSerial.value = event.serial || event.serialNumber || `SEC-${String(event.id).padStart(3, '0')}`;
        this.domElements.eventTitle.value = event.title;
        this.domElements.eventSeverity.value = event.severity;
        this.domElements.eventType.value = event.type;
        this.domElements.eventDate.value = event.date;
        this.domElements.eventDescription.value = event.description;
        this.domElements.eventDetailedDescription.value = event.detailedDescription || '';
        this.domElements.affectedSystems.value = event.affectedSystems || '';
        this.domElements.eventLocation.value = event.location || '';
        this.domElements.eventNotes.value = event.notes || '';
        this.domElements.responsiblePerson.value = event.responsiblePerson || '';
        this.domElements.directCosts.value = event.directCosts || '';
        this.domElements.indirectCosts.value = event.indirectCosts || '';

        // Remove the event from array (will be re-added when form is submitted)
        this.events = this.events.filter(e => e.id !== eventId);
        this.saveToStorage();
        this.renderEvents();
        this.updateStatistics();
        this.updateCharts();
        this.updateRiskAnalysis();
        this.logActivity('event_edit', `تعديل حدث أمني: ${event.title}`);

        // Scroll to form
        document.querySelector('.event-input-section').scrollIntoView({ behavior: 'smooth' });
        this.showNotification('تم تحميل بيانات الحدث للتعديل', 'info');
    }

    deleteEvent(eventId) {
        // Check if user has delete permission
        if (!this.currentUser || !this.currentUser.permissions.includes('delete')) {
            this.showNotification('ليس لديك صلاحية لحذف الأحداث', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا الحدث؟')) {
            const event = this.events.find(e => e.id === eventId);
            this.events = this.events.filter(e => e.id !== eventId);
            this.saveToStorage();
            this.renderEvents();
            this.updateStatistics();
            this.updateCharts();
            this.updateRiskAnalysis();
            if (event) {
                this.logActivity('event_delete', `حذف حدث أمني: ${event.title}`);
            }
            this.showNotification('تم حذف الحدث بنجاح', 'success');
        }
    }

    deleteCurrentEvent() {
        if (this.currentViewingEventId) {
            this.deleteEvent(this.currentViewingEventId);
            this.closeModal();
        }
    }

    showModal() {
        this.domElements.eventModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        this.domElements.eventModal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.currentViewingEventId = null;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)}"></i>
            ${message}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 4000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    getSeverityLabel(severity) {
        const labels = {
            low: 'منخفض',
            medium: 'متوسط',
            high: 'عالي',
            critical: 'حرج'
        };
        return labels[severity] || severity;
    }

    getTypeLabel(type) {
        const eventType = this.eventTypes.find(et => et.key === type);
        return eventType ? eventType.label : type;
    }

    getStatusLabel(status) {
        const labels = {
            open: 'مفتوح',
            investigating: 'قيد التحقيق',
            resolved: 'محلول',
            closed: 'مغلق'
        };
        return labels[status] || status;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        // استخدام التقويم الإفرنجي (الميلادي) بوضوح
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }

    // Format date for display (Gregorian calendar only)
    formatDateOnly(dateString) {
        const date = new Date(dateString);
        // عرض التاريخ بالتقويم الإفرنجي (الميلادي) فقط
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // Format date for charts (short format - Gregorian)
    formatDateShort(dateString) {
        const date = new Date(dateString);
        // تنسيق مختصر بالتقويم الإفرنجي
        const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        return `${date.getDate()} ${months[date.getMonth()]}`;
    }

    // Get current date in Gregorian format
    getCurrentDate() {
        return this.formatDateOnly(new Date());
    }

    // Get current date and time in Gregorian format
    getCurrentDateTime() {
        return this.formatDate(new Date());
    }

    // Convert any existing dates to ensure Gregorian format
    ensureGregorianDates() {
        // This function ensures all dates in the system use Gregorian calendar
        // It's called during initialization to maintain consistency

        // Update events dates if needed
        this.events.forEach(event => {
            if (event.date) {
                // Ensure the date is properly formatted
                const date = new Date(event.date);
                if (!isNaN(date.getTime())) {
                    event.date = date.toISOString();
                }
            }
            if (event.createdAt) {
                const date = new Date(event.createdAt);
                if (!isNaN(date.getTime())) {
                    event.createdAt = date.toISOString();
                }
            }
        });

        // Update reviews dates if needed
        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
        reviews.forEach(review => {
            if (review.incidentDate) {
                const date = new Date(review.incidentDate);
                if (!isNaN(date.getTime())) {
                    review.incidentDate = date.toISOString().split('T')[0];
                }
            }
            if (review.createdAt) {
                const date = new Date(review.createdAt);
                if (!isNaN(date.getTime())) {
                    review.createdAt = date.toISOString();
                }
            }
        });
        localStorage.setItem('postIncidentReviews', JSON.stringify(reviews));
    }

    formatCostsForTable(directCosts, indirectCosts) {
        const direct = directCosts && directCosts.trim() ? directCosts.trim() : '';
        const indirect = indirectCosts && indirectCosts.trim() ? indirectCosts.trim() : '';

        if (!direct && !indirect) {
            return '<span class="no-costs">غير محدد</span>';
        }

        let result = '';
        if (direct) {
            result += `<div class="cost-item direct"><strong>مباشرة:</strong> ${direct}</div>`;
        }
        if (indirect) {
            result += `<div class="cost-item indirect"><strong>غير مباشرة:</strong> ${indirect}</div>`;
        }

        return result;
    }

    showAlert(message, type = 'info') {
        // Create alert element if it doesn't exist
        let alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alertContainer';
            alertContainer.className = 'alert-container';
            document.body.appendChild(alertContainer);
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        alertContainer.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    saveToStorage() {
        localStorage.setItem('securityEvents', JSON.stringify(this.events));
        localStorage.setItem('currentEventId', this.currentEventId.toString());
    }

    // Navigation between sections
    switchSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Remove active class from all nav buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected section
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Add active class to clicked nav button
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update data when switching to analytics or risk analysis
        if (sectionName === 'analytics') {
            this.updateStatistics();
            this.updateCharts();
        } else if (sectionName === 'risk-analysis') {
            this.updateRiskAnalysis();
        } else if (sectionName === 'user-management') {
            this.renderUsers();
        } else if (sectionName === 'event-types') {
            this.renderEventTypes();
        } else if (sectionName === 'post-incident-review') {
            this.updateIncidentTypeDropdown(); // Update incident types when switching to post-incident review
        }
    }

    // Simple logout function
    simpleLogout() {
        console.log('Simple logout function called');
        try {
            // Show simple confirmation dialog
            const confirmLogout = confirm('هل أنت متأكد من أنك تريد تسجيل الخروج من النظام؟');
            if (!confirmLogout) {
                console.log('Logout cancelled by user');
                return;
            }
            console.log('User confirmed logout, redirecting...');
            // فقط امسح بيانات الجلسة وليس كل البيانات
            const sessionId = localStorage.getItem('currentSession');
            if (sessionId) {
                const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                delete userSessions[sessionId];
                localStorage.setItem('userSessions', JSON.stringify(userSessions));
                localStorage.removeItem('currentSession');
            }
            sessionStorage.clear();
            // إعادة التوجيه
            window.location.href = 'login.html';
        } catch (error) {
            console.error('Error during logout:', error);
            // Force redirect even if there's an error
            window.location.href = 'login.html';
        }
    }

    // Logout function
    logout() {
        console.log('Logout function called');

        try {
            // Show simple confirmation dialog
            const confirmLogout = confirm('هل أنت متأكد من أنك تريد تسجيل الخروج من النظام؟');

            if (!confirmLogout) {
                console.log('Logout cancelled by user');
                return;
            }

            console.log('User confirmed logout');

            // Log the logout activity
            if (this.currentUser) {
                this.logActivity('logout', `تسجيل خروج المستخدم ${this.currentUser.fullName}`);
            }

            // Clear session data
            const sessionId = localStorage.getItem('currentSession');
            if (sessionId) {
                this.clearSession(sessionId);
            }

            // Clear any temporary data
            sessionStorage.clear();
            localStorage.removeItem('currentSession');
            localStorage.removeItem('userSession');

            // Show logout success message
            this.showNotification('تم تسجيل الخروج بنجاح', 'success');

            // Clear current user
            this.currentUser = null;

            console.log('Redirecting to login page...');

            // Add a small delay before redirect for better UX
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);

        } catch (error) {
            console.error('Error during logout:', error);
            this.showNotification('حدث خطأ أثناء تسجيل الخروج', 'error');

            // Force redirect even if there's an error
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        }
    }

    // Show confirmation dialog
    showConfirmDialog(title, message, confirmText = 'تأكيد', cancelText = 'إلغاء') {
        return new Promise((resolve) => {
            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'confirm-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            // Create modal content
            const modal = document.createElement('div');
            modal.className = 'confirm-modal';
            modal.style.cssText = `
                background: var(--bg-primary);
                border-radius: 16px;
                padding: 2rem;
                max-width: 400px;
                width: 90%;
                box-shadow: var(--shadow-2xl);
                border: 1px solid var(--border-light);
                animation: slideInUp 0.3s ease;
                text-align: center;
            `;

            modal.innerHTML = `
                <div style="margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: var(--warning); margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--text-primary); margin-bottom: 0.5rem; font-size: 1.25rem;">${title}</h3>
                    <p style="color: var(--text-secondary); line-height: 1.5;">${message}</p>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button class="confirm-btn" style="
                        background: var(--gradient-danger);
                        color: white;
                        border: none;
                        padding: 0.75rem 1.5rem;
                        border-radius: 8px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        font-family: inherit;
                    ">${confirmText}</button>
                    <button class="cancel-btn" style="
                        background: var(--bg-secondary);
                        color: var(--text-primary);
                        border: 1px solid var(--border-color);
                        padding: 0.75rem 1.5rem;
                        border-radius: 8px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        font-family: inherit;
                    ">${cancelText}</button>
                </div>
            `;

            overlay.appendChild(modal);
            document.body.appendChild(overlay);

            // Add event listeners
            const confirmBtn = modal.querySelector('.confirm-btn');
            const cancelBtn = modal.querySelector('.cancel-btn');

            const cleanup = () => {
                overlay.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 300);
            };

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    cleanup();
                    resolve(false);
                }
            });

            // Add hover effects
            confirmBtn.addEventListener('mouseenter', () => {
                confirmBtn.style.transform = 'translateY(-2px)';
                confirmBtn.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
            });

            confirmBtn.addEventListener('mouseleave', () => {
                confirmBtn.style.transform = '';
                confirmBtn.style.boxShadow = '';
            });

            cancelBtn.addEventListener('mouseenter', () => {
                cancelBtn.style.transform = 'translateY(-2px)';
                cancelBtn.style.background = 'var(--bg-tertiary)';
            });

            cancelBtn.addEventListener('mouseleave', () => {
                cancelBtn.style.transform = '';
                cancelBtn.style.background = 'var(--bg-secondary)';
            });
        });
    }

    // Log activity
    logActivity(action, description) {
        try {
            const activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
            const activity = {
                id: Date.now(),
                userId: this.currentUser ? this.currentUser.id : 'anonymous',
                username: this.currentUser ? this.currentUser.username : 'anonymous',
                action: action,
                description: description,
                timestamp: new Date().toISOString(),
                ip: 'localhost',
                userAgent: navigator.userAgent
            };

            activityLog.unshift(activity);

            if (activityLog.length > 1000) {
                activityLog.splice(1000);
            }

            localStorage.setItem('activityLog', JSON.stringify(activityLog));
        } catch (error) {
            console.error('Error logging activity:', error);
        }
    }

    // User Management Functions
    showUserModal(user = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('userModalTitle');
        const form = document.getElementById('userForm');

        if (user) {
            title.textContent = 'تعديل المستخدم';
            this.fillUserForm(user);
        } else {
            title.textContent = 'إضافة مستخدم جديد';
            form.reset();
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeUserModal() {
        const modal = document.getElementById('userModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        document.getElementById('userForm').reset();
    }

    fillUserForm(user) {
        document.getElementById('userFullName').value = user.fullName;
        document.getElementById('userUsername').value = user.username;
        document.getElementById('userEmail').value = user.email;
        document.getElementById('userRole').value = user.role;

        // Set permissions
        document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
            checkbox.checked = user.permissions.includes(checkbox.value);
        });
    }

    updatePermissionsByRole() {
        const role = document.getElementById('userRole').value;
        const permissionCheckboxes = document.querySelectorAll('input[name="permissions"]');

        // Clear all permissions first
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Set permissions based on role
        const rolePermissions = {
            'admin': ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'manage_system'],
            'analyst': ['read', 'write', 'view_analytics'],
            'operator': ['read']
        };

        if (rolePermissions[role]) {
            rolePermissions[role].forEach(permission => {
                const checkbox = document.querySelector(`input[name="permissions"][value="${permission}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        }
    }

    saveUser() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);

        const username = formData.get('username').trim();
        const fullName = formData.get('fullName').trim();
        const email = formData.get('email').trim();
        const role = formData.get('role');
        const password = formData.get('password');

        if (!username || !fullName || !email || !role || !password) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        const permissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
            .map(checkbox => checkbox.value);

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};

        // Check if username already exists (for new users)
        const isEditing = users[username] && users[username].id;
        if (!isEditing && users[username]) {
            this.showNotification('اسم المستخدم موجود بالفعل', 'error');
            return;
        }

        const user = {
            id: isEditing ? users[username].id : username,
            username: username,
            password: this.hashPassword(password),
            fullName: fullName,
            email: email,
            role: role,
            permissions: permissions,
            isActive: true,
            createdAt: isEditing ? users[username].createdAt : new Date().toISOString(),
            lastLogin: isEditing ? users[username].lastLogin : null
        };

        users[username] = user;
        try {
            localStorage.setItem('systemUsers', JSON.stringify(users));
        } catch (error) {
            console.error('Error saving user data to localStorage:', error);
            this.showNotification('خطأ في حفظ بيانات المستخدم. قد تكون مساحة التخزين ممتلئة.', 'error');
            return;
        }

        this.logActivity('user_management',
            isEditing ? `تعديل المستخدم ${fullName}` : `إضافة المستخدم ${fullName}`);

        this.closeUserModal();
        this.renderUsers();
        this.showNotification(
            isEditing ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح',
            'success'
        );
    }

    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    renderUsers() {
        const usersGrid = document.getElementById('usersGrid');
        if (!usersGrid) return;

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const usersList = Object.values(users);

        if (usersList.length === 0) {
            usersGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد مستخدمين</h3>
                    <p>اضغط على "إضافة مستخدم جديد" لإضافة أول مستخدم</p>
                </div>
            `;
            return;
        }

        usersGrid.innerHTML = usersList.map(user => `
            <div class="user-card ${!user.isActive ? 'inactive' : ''}">
                <div class="user-header">
                    <div class="user-avatar">
                        ${user.fullName.charAt(0).toUpperCase()}
                    </div>
                    <div class="user-info">
                        <h4>${user.fullName}</h4>
                        <p>@${user.username}</p>
                    </div>
                </div>
                <div class="user-role role-${user.role}">
                    ${this.getRoleLabel(user.role)}
                </div>
                <div class="user-permissions">
                    <h5>الصلاحيات:</h5>
                    <div class="permission-tags">
                        ${user.permissions.map(permission =>
                            `<span class="permission-tag">${this.getPermissionLabel(permission)}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="user-actions">
                    <button class="user-action-btn edit-user-btn" onclick="securityManager.editUser('${user.username}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="user-action-btn toggle-user-btn" onclick="securityManager.toggleUser('${user.username}')">
                        <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                    </button>
                    ${user.username !== this.currentUser.username ?
                        `<button class="user-action-btn delete-user-btn" onclick="securityManager.deleteUser('${user.username}')">
                            <i class="fas fa-trash"></i>
                        </button>` : ''
                    }
                </div>
            </div>
        `).join('');
    }

    editUser(username) {
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const user = users[username];
        if (user) {
            this.showUserModal(user);
        }
    }

    toggleUser(username) {
        if (username === this.currentUser.username) {
            this.showNotification('لا يمكن تعطيل حسابك الخاص', 'error');
            return;
        }

        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        const user = users[username];

        if (user) {
            user.isActive = !user.isActive;
            users[username] = user;
            try {
                localStorage.setItem('systemUsers', JSON.stringify(users));
            } catch (error) {
                console.error('Error saving user data to localStorage:', error);
                this.showNotification('خطأ في حفظ بيانات المستخدم. قد تكون مساحة التخزين ممتلئة.', 'error');
                return;
            }

            this.logActivity('user_management',
                `${user.isActive ? 'تفعيل' : 'تعطيل'} المستخدم ${user.fullName}`);

            this.renderUsers();
            this.showNotification(
                `تم ${user.isActive ? 'تفعيل' : 'تعطيل'} المستخدم بنجاح`,
                'success'
            );
        }
    }

    deleteUser(username) {
        if (username === this.currentUser.username) {
            this.showNotification('لا يمكن حذف حسابك الخاص', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
            const user = users[username];

            if (user) {
                delete users[username];
                localStorage.setItem('systemUsers', JSON.stringify(users));

                this.logActivity('user_management', `حذف المستخدم ${user.fullName}`);

                this.renderUsers();
                this.showNotification('تم حذف المستخدم بنجاح', 'success');
            }
        }
    }

    getRoleLabel(role) {
        const labels = {
            'admin': 'مدير النظام',
            'analyst': 'محلل أمني',
            'operator': 'مشغل'
        };
        return labels[role] || role;
    }

    getPermissionLabel(permission) {
        const labels = {
            'read': 'قراءة',
            'write': 'كتابة',
            'delete': 'حذف',
            'view_analytics': 'عرض الإحصائيات',
            'manage_users': 'إدارة المستخدمين',
            'manage_system': 'إدارة النظام'
        };
        return labels[permission] || permission;
    }

    // Event Types Management Functions
    showEventTypeModal(eventTypeKey = null) {
        const modal = document.getElementById('eventTypeModal');
        const title = document.getElementById('eventTypeModalTitle');
        const form = document.getElementById('eventTypeForm');

        if (eventTypeKey) {
            const eventType = this.eventTypes.find(et => et.key === eventTypeKey);
            if (eventType) {
                title.textContent = 'تعديل نوع الحدث';
                this.fillEventTypeForm(eventType);
                this.currentEditingEventTypeKey = eventTypeKey;
                // Disable key field when editing
                document.getElementById('eventTypeKey').disabled = true;
            }
        } else {
            title.textContent = 'إضافة نوع حدث جديد';
            form.reset();
            this.currentEditingEventTypeKey = null;
            document.getElementById('eventTypeKey').disabled = false;
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeEventTypeModal() {
        const modal = document.getElementById('eventTypeModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        document.getElementById('eventTypeForm').reset();
        this.currentEditingEventTypeKey = null;
    }

    fillEventTypeForm(eventType) {
        document.getElementById('eventTypeKey').value = eventType.key;
        document.getElementById('eventTypeLabel').value = eventType.label;
        document.getElementById('eventTypeDescription').value = eventType.description || '';
        document.getElementById('eventTypeIcon').value = eventType.icon;
        document.getElementById('eventTypeColor').value = eventType.color;
    }

    saveEventType(event) {
        event.preventDefault();

        const formData = new FormData(document.getElementById('eventTypeForm'));
        const key = formData.get('eventTypeKey').trim();
        const label = formData.get('eventTypeLabel').trim();
        const description = formData.get('eventTypeDescription').trim();
        const icon = formData.get('eventTypeIcon');
        const color = formData.get('eventTypeColor');

        if (!key || !label || !icon || !color) {
            this.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // Check if key already exists (for new types)
        const isEditing = this.currentEditingEventTypeKey !== null;
        if (!isEditing && this.eventTypes.some(et => et.key === key)) {
            this.showAlert('مفتاح النوع موجود بالفعل، يرجى استخدام مفتاح مختلف', 'error');
            return;
        }

        const eventType = {
            key: key,
            label: label,
            description: description,
            icon: icon,
            color: color
        };

        if (isEditing) {
            const index = this.eventTypes.findIndex(et => et.key === this.currentEditingEventTypeKey);
            if (index !== -1) {
                this.eventTypes[index] = eventType;
            }
        } else {
            this.eventTypes.push(eventType);
        }

        this.saveEventTypesToStorage();
        this.updateEventTypeDropdown();
        this.updateIncidentTypeDropdown(); // Update incident types dropdown too
        this.renderEventTypes();
        this.closeEventTypeModal();

        this.showAlert(
            isEditing ? 'تم تحديث نوع الحدث بنجاح' : 'تم إضافة نوع الحدث بنجاح',
            'success'
        );

        this.logActivity('event_type_management',
            isEditing ? `تعديل نوع الحدث ${label}` : `إضافة نوع الحدث ${label}`);
    }

    renderEventTypes() {
        const grid = document.getElementById('eventTypesGrid');
        if (!grid) return;

        if (this.eventTypes.length === 0) {
            grid.innerHTML = `
                <div class="empty-event-types">
                    <i class="fas fa-tags"></i>
                    <h3>لا توجد أنواع أحداث</h3>
                    <p>اضغط على "إضافة نوع جديد" لإضافة أول نوع حدث</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.eventTypes.map(eventType => {
            const usageCount = this.events.filter(e => e.type === eventType.key).length;
            return `
                <div class="event-type-card">
                    <div class="event-type-usage">${usageCount} حدث</div>
                    <div class="event-type-header">
                        <div class="event-type-icon" style="background-color: ${eventType.color}">
                            <i class="${eventType.icon}"></i>
                        </div>
                        <div class="event-type-info">
                            <h3>${eventType.label}</h3>
                            <div class="event-type-key">${eventType.key}</div>
                        </div>
                    </div>
                    <div class="event-type-description">
                        ${eventType.description || 'لا يوجد وصف'}
                    </div>
                    <div class="event-type-actions">
                        <button class="action-btn edit-type-btn" onclick="securityManager.editEventType('${eventType.key}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        ${usageCount === 0 ?
                            `<button class="action-btn delete-type-btn" onclick="securityManager.deleteEventType('${eventType.key}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>` :
                            `<button class="action-btn delete-type-btn" disabled title="لا يمكن حذف نوع مستخدم في الأحداث">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    editEventType(eventTypeKey) {
        this.showEventTypeModal(eventTypeKey);
    }

    deleteEventType(eventTypeKey) {
        const eventType = this.eventTypes.find(et => et.key === eventTypeKey);
        if (!eventType) return;

        // Check if type is used in any events
        const usageCount = this.events.filter(e => e.type === eventTypeKey).length;
        if (usageCount > 0) {
            this.showAlert('لا يمكن حذف نوع الحدث لأنه مستخدم في الأحداث الموجودة', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف نوع الحدث "${eventType.label}"؟`)) {
            this.eventTypes = this.eventTypes.filter(et => et.key !== eventTypeKey);
            this.saveEventTypesToStorage();
            this.updateEventTypeDropdown();
            this.updateIncidentTypeDropdown(); // Update incident types dropdown too
            this.renderEventTypes();
            this.showAlert('تم حذف نوع الحدث بنجاح', 'success');
            this.logActivity('event_type_management', `حذف نوع الحدث ${eventType.label}`);
        }
    }

    updateEventTypeDropdown() {
        const dropdown = document.getElementById('eventType');
        if (!dropdown) return;

        // Save current selection
        const currentValue = dropdown.value;

        // Clear and rebuild options
        dropdown.innerHTML = '<option value="">اختر نوع الحدث</option>';

        this.eventTypes.forEach(eventType => {
            const option = document.createElement('option');
            option.value = eventType.key;
            option.textContent = eventType.label;
            dropdown.appendChild(option);
        });

        // Restore selection if still valid
        if (currentValue && this.eventTypes.some(et => et.key === currentValue)) {
            dropdown.value = currentValue;
        }
    }

    // Update incident type dropdown for post-incident review
    updateIncidentTypeDropdown() {
        const incidentTypeSelect = document.getElementById('incidentType');
        if (!incidentTypeSelect) return;

        // Save current selection
        const currentValue = incidentTypeSelect.value;

        // Clear existing options except the first one
        incidentTypeSelect.innerHTML = '<option value="">اختر نوع الحادث</option>';

        // Add event types from storage
        this.eventTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type.key;
            option.textContent = type.label;
            incidentTypeSelect.appendChild(option);
        });

        // Restore selection if still valid
        if (currentValue && this.eventTypes.some(et => et.key === currentValue)) {
            incidentTypeSelect.value = currentValue;
        }
    }

    saveEventTypesToStorage() {
        try {
            localStorage.setItem('eventTypes', JSON.stringify(this.eventTypes));
        } catch (error) {
            console.error('Error saving event types to localStorage:', error);
            this.showNotification('خطأ في حفظ أنواع الأحداث. قد تكون مساحة التخزين ممتلئة.', 'error');
        }
    }

    // Update statistics
    updateStatistics() {
        try {
            const totalEvents = this.events.length;
            const criticalEvents = this.events.filter(e => e.severity === 'critical').length;
            const highEvents = this.events.filter(e => e.severity === 'high').length;
            const openEvents = this.events.filter(e => e.status === 'open').length;

            if (this.domElements.totalEvents) this.domElements.totalEvents.textContent = totalEvents;
            if (this.domElements.criticalEvents) this.domElements.criticalEvents.textContent = criticalEvents;
            if (this.domElements.highEvents) this.domElements.highEvents.textContent = highEvents;
            if (this.domElements.openEvents) this.domElements.openEvents.textContent = openEvents;
        } catch (error) {
            console.error('Error updating statistics:', error);
        }
    }

    // Initialize charts
    initializeCharts() {
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded. Attempting to load fallback...');
            this.loadChartJSFallback();
            return;
        }

        this.charts = {};

        // Add delay to ensure DOM is ready
        setTimeout(() => {
            this.createSeverityChart();
            this.createTimelineChart();
            this.createTypeChart();
            this.createStatusChart();
            this.createRiskGauge();
        }, 100);
    }

    // Update all charts
    updateCharts() {
        try {
            if (this.charts && this.charts.severityChart) {
                this.updateSeverityChart();
            }
            if (this.charts && this.charts.timelineChart) {
                this.updateTimelineChart();
            }
            if (this.charts && this.charts.typeChart) {
                this.updateTypeChart();
            }
            if (this.charts && this.charts.statusChart) {
                this.updateStatusChart();
            }
        } catch (error) {
            console.error('Error updating charts:', error);
        }
    }

    // Load Chart.js fallback
    loadChartJSFallback() {
        console.log('Loading Chart.js fallback...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
        script.onload = () => {
            console.log('Chart.js loaded successfully');
            this.initializeCharts();
        };
        script.onerror = () => {
            console.error('Failed to load Chart.js fallback');
            this.showChartsUnavailableMessage();
        };
        document.head.appendChild(script);
    }

    // Show message when charts are unavailable
    showChartsUnavailableMessage() {
        const chartContainers = document.querySelectorAll('.chart-content');
        chartContainers.forEach(container => {
            container.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>الرسوم البيانية غير متاحة حالياً</p>
                    <small>تعذر تحميل مكتبة الرسوم البيانية</small>
                </div>
            `;
        });
    }

    // Create severity distribution chart
    createSeverityChart() {
        try {
            const canvas = document.getElementById('severityChart');
            if (!canvas) {
                console.error('Severity chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
            this.charts.severityChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['منخفض', 'متوسط', 'عالي', 'حرج'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#10b981',
                        '#f59e0b',
                        '#ef4444',
                        '#dc2626'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        this.updateSeverityChart();
        } catch (error) {
            console.error('Error creating severity chart:', error);
        }
    }

    // Update severity chart data
    updateSeverityChart() {
        if (!this.charts.severityChart) return;

        try {
            const severityCounts = {
                low: this.events.filter(e => e.severity === 'low').length,
                medium: this.events.filter(e => e.severity === 'medium').length,
                high: this.events.filter(e => e.severity === 'high').length,
                critical: this.events.filter(e => e.severity === 'critical').length
            };

            this.charts.severityChart.data.datasets[0].data = [
                severityCounts.low,
                severityCounts.medium,
                severityCounts.high,
                severityCounts.critical
            ];
            this.charts.severityChart.update();
        } catch (error) {
            console.error('Error updating severity chart:', error);
        }
    }

    // Create timeline chart
    createTimelineChart() {
        try {
            const canvas = document.getElementById('timelineChart');
            if (!canvas) {
                console.error('Timeline chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.timelineChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'عدد الأحداث',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        this.updateTimelineChart();
        } catch (error) {
            console.error('Error creating timeline chart:', error);
        }
    }

    // Update timeline chart data
    updateTimelineChart() {
        if (!this.charts.timelineChart) return;

        try {
            const last7Days = [];
            const eventCounts = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                last7Days.push(this.formatDateShort(date));

                const dayEvents = this.events.filter(event => {
                    const eventDate = new Date(event.date).toISOString().split('T')[0];
                    return eventDate === dateStr;
                }).length;

                eventCounts.push(dayEvents);
            }

            this.charts.timelineChart.data.labels = last7Days;
            this.charts.timelineChart.data.datasets[0].data = eventCounts;
            this.charts.timelineChart.update();
        } catch (error) {
            console.error('Error updating timeline chart:', error);
        }
    }

    // Create event type chart
    createTypeChart() {
        try {
            const canvas = document.getElementById('typeChart');
            if (!canvas) {
                console.error('Type chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.typeChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['اختراق', 'برمجيات خبيثة', 'تصيد', 'تسريب بيانات', 'وصول غير مصرح', 'فشل النظام', 'أخرى'],
                datasets: [{
                    label: 'عدد الأحداث',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#ef4444',
                        '#f59e0b',
                        '#06b6d4',
                        '#dc2626',
                        '#8b5cf6',
                        '#6b7280',
                        '#10b981'
                    ],
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        this.updateTypeChart();
        } catch (error) {
            console.error('Error creating type chart:', error);
        }
    }

    // Update type chart data
    updateTypeChart() {
        if (!this.charts.typeChart) return;

        try {
            const typeCounts = {
                intrusion: this.events.filter(e => e.type === 'intrusion').length,
                malware: this.events.filter(e => e.type === 'malware').length,
                phishing: this.events.filter(e => e.type === 'phishing').length,
                'data-breach': this.events.filter(e => e.type === 'data-breach').length,
                'unauthorized-access': this.events.filter(e => e.type === 'unauthorized-access').length,
                'system-failure': this.events.filter(e => e.type === 'system-failure').length,
                other: this.events.filter(e => e.type === 'other').length
            };

            this.charts.typeChart.data.datasets[0].data = [
                typeCounts.intrusion,
                typeCounts.malware,
                typeCounts.phishing,
                typeCounts['data-breach'],
                typeCounts['unauthorized-access'],
                typeCounts['system-failure'],
                typeCounts.other
            ];
            this.charts.typeChart.update();
        } catch (error) {
            console.error('Error updating type chart:', error);
        }
    }

    // Create status chart
    createStatusChart() {
        try {
            const canvas = document.getElementById('statusChart');
            if (!canvas) {
                console.error('Status chart canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.statusChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['مفتوح', 'قيد التحقيق', 'محلول', 'مغلق'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#2563eb',
                        '#f59e0b',
                        '#10b981',
                        '#6b7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        this.updateStatusChart();
        } catch (error) {
            console.error('Error creating status chart:', error);
        }
    }

    // Update status chart data
    updateStatusChart() {
        if (!this.charts.statusChart) return;

        try {
            const statusCounts = {
                open: this.events.filter(e => e.status === 'open').length,
                investigating: this.events.filter(e => e.status === 'investigating').length,
                resolved: this.events.filter(e => e.status === 'resolved').length,
                closed: this.events.filter(e => e.status === 'closed').length
            };

            this.charts.statusChart.data.datasets[0].data = [
                statusCounts.open,
                statusCounts.investigating,
                statusCounts.resolved,
                statusCounts.closed
            ];
            this.charts.statusChart.update();
        } catch (error) {
            console.error('Error updating status chart:', error);
        }
    }

    // Create risk gauge
    createRiskGauge() {
        try {
            const canvas = document.getElementById('riskGauge');
            if (!canvas) {
                console.error('Risk gauge canvas not found');
                return;
            }
            const ctx = canvas.getContext('2d');
        this.charts.riskGauge = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [65, 35],
                    backgroundColor: [
                        '#f59e0b',
                        '#e5e7eb'
                    ],
                    borderWidth: 0,
                    circumference: 180,
                    rotation: 270
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
        } catch (error) {
            console.error('Error creating risk gauge:', error);
        }
    }

    // Update risk analysis
    updateRiskAnalysis() {
        const totalEvents = this.events.length;
        const criticalEvents = this.events.filter(e => e.severity === 'critical').length;
        const highEvents = this.events.filter(e => e.severity === 'high').length;
        const openEvents = this.events.filter(e => e.status === 'open').length;

        // Calculate risk score (0-100)
        let riskScore = 0;
        if (totalEvents > 0) {
            riskScore = Math.min(100,
                (criticalEvents * 25) +
                (highEvents * 15) +
                (openEvents * 10) +
                (totalEvents * 2)
            );
        }

        // Update risk level and description
        let riskLevel, riskDescription, riskColor;
        if (riskScore >= 80) {
            riskLevel = 'حرج';
            riskDescription = 'مستوى مخاطر عالي جداً - يتطلب تدخل فوري';
            riskColor = '#dc2626';
        } else if (riskScore >= 60) {
            riskLevel = 'عالي';
            riskDescription = 'مستوى مخاطر عالي - يتطلب اهتماماً عاجلاً';
            riskColor = '#ef4444';
        } else if (riskScore >= 40) {
            riskLevel = 'متوسط';
            riskDescription = 'مستوى مخاطر متوسط - يتطلب مراقبة';
            riskColor = '#f59e0b';
        } else {
            riskLevel = 'منخفض';
            riskDescription = 'مستوى مخاطر منخفض - الوضع مستقر';
            riskColor = '#10b981';
        }

        const riskLevelEl = document.getElementById('riskLevel');
        const riskScoreEl = document.getElementById('riskScore');
        const riskDescEl = document.getElementById('riskDescription');

        if (riskLevelEl) {
            riskLevelEl.textContent = riskLevel;
            riskLevelEl.style.color = riskColor;
        }
        if (riskScoreEl) riskScoreEl.textContent = `${riskScore}/100`;
        if (riskDescEl) riskDescEl.textContent = riskDescription;

        // Update risk gauge
        try {
            if (this.charts && this.charts.riskGauge) {
                this.charts.riskGauge.data.datasets[0].data = [riskScore, 100 - riskScore];
                this.charts.riskGauge.data.datasets[0].backgroundColor[0] = riskColor;
                this.charts.riskGauge.update();
            }
        } catch (error) {
            console.error('Error updating risk gauge:', error);
        }

        // Generate recommendations
        this.generateRecommendations(riskScore, criticalEvents, highEvents, openEvents);
    }

    // Generate security recommendations
    generateRecommendations(riskScore, criticalEvents, openEvents) {
        const recommendations = [];

        if (criticalEvents > 0) {
            recommendations.push({
                title: 'معالجة الأحداث الحرجة فوراً',
                description: `يوجد ${criticalEvents} حدث حرج يتطلب معالجة فورية لتجنب تفاقم المخاطر الأمنية.`,
                priority: 'high'
            });
        }

        if (highEvents > 3) {
            recommendations.push({
                title: 'معالجة الأحداث عالية الخطورة',
                description: `يوجد ${highEvents} حدث عالي الخطورة يتطلب اهتماماً خاصاً.`,
                priority: 'high'
            });
        }

        if (openEvents > 5) {
            recommendations.push({
                title: 'مراجعة الأحداث المفتوحة',
                description: `يوجد ${openEvents} حدث مفتوح. يُنصح بمراجعة وإغلاق الأحداث المحلولة.`,
                priority: 'medium'
            });
        }

        if (riskScore > 70) {
            recommendations.push({
                title: 'تعزيز الإجراءات الأمنية',
                description: 'مستوى المخاطر مرتفع. يُنصح بتطبيق إجراءات أمنية إضافية ومراجعة السياسات.',
                priority: 'high'
            });
        }

        if (this.events.filter(e => e.type === 'unauthorized-access').length > 2) {
            recommendations.push({
                title: 'تقوية أنظمة المصادقة',
                description: 'تم رصد محاولات وصول غير مصرح متعددة. يُنصح بتطبيق المصادقة الثنائية.',
                priority: 'medium'
            });
        }

        if (recommendations.length === 0) {
            recommendations.push({
                title: 'الحفاظ على المراقبة المستمرة',
                description: 'الوضع الأمني مستقر حالياً. استمر في المراقبة والتحديث المنتظم للأنظمة.',
                priority: 'low'
            });
        }

        this.renderRecommendations(recommendations);
    }

    // Render recommendations
    renderRecommendations(recommendations) {
        const container = document.getElementById('recommendationsList');
        if (!container) return;

        try {
            container.innerHTML = recommendations.map(rec => `
                <div class="recommendation-item ${rec.priority}-priority">
                    <div class="recommendation-header">
                        <div class="recommendation-title">${rec.title}</div>
                        <span class="recommendation-priority ${rec.priority}">${this.getPriorityLabel(rec.priority)}</span>
                    </div>
                    <div class="recommendation-description">${rec.description}</div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error rendering recommendations:', error);
        }
    }

    // Get priority label
    getPriorityLabel(priority) {
        const labels = {
            high: 'عالي',
            medium: 'متوسط',
            low: 'منخفض'
        };
        return labels[priority] || priority;
    }

    // Save Post Incident Review
    savePostIncidentReview() {
        // Check if user has write permission
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لحفظ المراجعة', 'error');
            return;
        }

        const formData = new FormData(document.getElementById('postIncidentForm'));

        // Validate required fields
        const requiredFields = [
            'reviewerName', 'reviewerDepartment', 'reviewerManagement',
            'incidentDate', 'incidentTime', 'incidentLocation',
            'incidentDescription', 'incidentRecordNumber', 'incidentType', 'incidentResponsible',
            'employeePreparedness', 'employeeResponse',
            'apparentCauses', 'rootCauses', 'contributingFactors',
            'recoveryPlansActivated', 'recoveryPlansSuitability',
            'correctiveActions', 'actionResponsible', 'implementationPeriod',
            'followUpMechanism', 'followUpResponsible', 'followUpTiming',
            'lessonsLearned'
        ];

        for (const field of requiredFields) {
            if (!formData.get(field) || !formData.get(field).trim()) {
                this.showNotification(`يرجى ملء جميع الحقول المطلوبة`, 'error');
                return;
            }
        }

        const review = {
            id: Date.now(),
            reviewerName: formData.get('reviewerName'),
            reviewerDepartment: formData.get('reviewerDepartment'),
            reviewerManagement: formData.get('reviewerManagement'),
            incidentDate: formData.get('incidentDate'),
            incidentTime: formData.get('incidentTime'),
            incidentLocation: formData.get('incidentLocation'),
            incidentDescription: formData.get('incidentDescription'),
            incidentRecordNumber: formData.get('incidentRecordNumber'),
            incidentType: formData.get('incidentType'),
            incidentResponsible: formData.get('incidentResponsible'),
            employeePreparedness: formData.get('employeePreparedness'),
            employeeResponse: formData.get('employeeResponse'),
            apparentCauses: formData.get('apparentCauses'),
            rootCauses: formData.get('rootCauses'),
            contributingFactors: formData.get('contributingFactors'),
            recoveryPlansActivated: formData.get('recoveryPlansActivated'),
            recoveryPlansSuitability: formData.get('recoveryPlansSuitability'),
            correctiveActions: formData.get('correctiveActions'),
            actionResponsible: formData.get('actionResponsible'),
            implementationPeriod: formData.get('implementationPeriod'),
            followUpMechanism: formData.get('followUpMechanism'),
            followUpResponsible: formData.get('followUpResponsible'),
            followUpTiming: formData.get('followUpTiming'),
            lessonsLearned: formData.get('lessonsLearned'),
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser.username
        };

        // Save to localStorage
        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
        reviews.unshift(review);
        localStorage.setItem('postIncidentReviews', JSON.stringify(reviews));

        this.logActivity('post_incident_review_create', `إنشاء مراجعة ما بعد الحادث: ${review.incidentRecordNumber}`);
        this.showNotification('تم حفظ المراجعة بنجاح', 'success');

        // Reset form
        document.getElementById('postIncidentForm').reset();
    }

    // Generate Post Incident Report
    generatePostIncidentReport() {
        const formData = new FormData(document.getElementById('postIncidentForm'));

        // Check if form has data
        if (!formData.get('reviewerName') || !formData.get('incidentRecordNumber')) {
            this.showNotification('يرجى ملء البيانات أولاً قبل إنشاء التقرير', 'warning');
            return;
        }

        // Create report content
        const reportContent = this.generateReportHTML(formData);

        // Open report in new window for printing
        const reportWindow = window.open('', '_blank');
        reportWindow.document.open();
        reportWindow.document.body.innerHTML = reportContent;
        reportWindow.document.close();

        this.showNotification('تم إنشاء التقرير بنجاح', 'success');
    }

    // Show saved reviews
    showSavedReviews() {
        document.querySelector('.post-incident-form').style.display = 'none';
        document.getElementById('savedReviewsSection').style.display = 'block';
        this.renderSavedReviews();
    }

    // Show review form
    showReviewForm() {
        document.querySelector('.post-incident-form').style.display = 'block';
        document.getElementById('savedReviewsSection').style.display = 'none';
    }

    // Render saved reviews
    renderSavedReviews() {
        const tbody = document.getElementById('reviewsTableBody');
        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];

        if (reviews.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-clipboard-list"></i>
                            <p>لا توجد مراجعات محفوظة</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = reviews.map(review => `
            <tr>
                <td>${review.incidentRecordNumber}</td>
                <td>${review.reviewerName}</td>
                <td>${this.formatDateOnly(review.incidentDate)}</td>
                <td>${this.getIncidentTypeLabel(review.incidentType)}</td>
                <td>${this.formatDateOnly(review.createdAt)}</td>
                <td class="actions">
                    <button class="action-btn view-btn" onclick="securityManager.viewReview(${review.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" onclick="securityManager.editReview(${review.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="securityManager.deleteReview(${review.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Get incident type label
    getIncidentTypeLabel(type) {
        // First try to find in stored event types
        const eventType = this.eventTypes.find(et => et.key === type);
        if (eventType) {
            return eventType.label;
        }

        // Fallback to static labels for backward compatibility
        const labels = {
            'intrusion': 'محاولة اختراق',
            'malware': 'برمجيات خبيثة',
            'phishing': 'تصيد إلكتروني',
            'data-breach': 'تسريب بيانات',
            'unauthorized-access': 'وصول غير مصرح',
            'system-failure': 'فشل النظام',
            'other': 'أخرى'
        };
        return labels[type] || type;
    }

    // View review details
    viewReview(reviewId) {
        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
        const review = reviews.find(r => r.id === reviewId);

        if (!review) {
            this.showNotification('المراجعة غير موجودة', 'error');
            return;
        }

        // Create and show modal with review details
        this.showReviewModal(review, 'view');
    }

    // Edit review
    editReview(reviewId) {
        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لتعديل المراجعات', 'error');
            return;
        }

        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
        const review = reviews.find(r => r.id === reviewId);

        if (!review) {
            this.showNotification('المراجعة غير موجودة', 'error');
            return;
        }

        // Fill form with review data
        this.fillReviewForm(review);
        this.showReviewForm();
        this.showNotification('تم تحميل بيانات المراجعة للتعديل', 'info');
    }

    // Delete review
    deleteReview(reviewId) {
        if (!this.currentUser || !this.currentUser.permissions.includes('delete')) {
            this.showNotification('ليس لديك صلاحية لحذف المراجعات', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذه المراجعة؟')) {
            let reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
            const review = reviews.find(r => r.id === reviewId);

            reviews = reviews.filter(r => r.id !== reviewId);
            localStorage.setItem('postIncidentReviews', JSON.stringify(reviews));

            if (review) {
                this.logActivity('post_incident_review_delete', `حذف مراجعة ما بعد الحادث: ${review.incidentRecordNumber}`);
            }

            this.renderSavedReviews();
            this.showNotification('تم حذف المراجعة بنجاح', 'success');
        }
    }

    // Generate report for specific review
    generateReviewReport(reviewId) {
        const reviews = JSON.parse(localStorage.getItem('postIncidentReviews')) || [];
        const review = reviews.find(r => r.id === reviewId);

        if (!review) {
            this.showNotification('المراجعة غير موجودة', 'error');
            return;
        }

        // Create FormData-like object from review
        const formData = new Map();
        Object.keys(review).forEach(key => {
            formData.set(key, review[key]);
        });

        // Generate and open report
        const reportContent = this.generateReportHTML(formData);
        const reportWindow = window.open('', '_blank');
        reportWindow.document.open();
        reportWindow.document.body.innerHTML = reportContent;
        reportWindow.document.close();

        this.showNotification('تم إنشاء التقرير بنجاح', 'success');
    }

    // Show review modal with details
    showReviewModal(review, mode = 'view') {
        const modal = document.getElementById('reviewModal');
        const modalBody = document.getElementById('reviewModalBody');

        if (!modal || !modalBody) {
            this.showNotification('خطأ في عرض المراجعة', 'error');
            return;
        }

        // Store current review for modal actions
        this.currentViewingReview = review;

        // Generate review details HTML
        modalBody.innerHTML = this.generateReviewDetailsHTML(review);

        // Show modal
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close review modal
    closeReviewModal() {
        const modal = document.getElementById('reviewModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
            this.currentViewingReview = null;
        }
    }

    // Print review
    printReview() {
        if (!this.currentViewingReview) {
            this.showNotification('لا توجد مراجعة للطباعة', 'error');
            return;
        }

        // Create FormData-like object from review
        const formData = new Map();
        Object.keys(this.currentViewingReview).forEach(key => {
            formData.set(key, this.currentViewingReview[key]);
        });

        // Generate and open report for printing
        const reportContent = this.generateReportHTML(formData);
        const printWindow = window.open('', '_blank');
        printWindow.document.open();
        printWindow.document.write(reportContent);
        printWindow.document.close();

        // Trigger print dialog
        printWindow.onload = function() {
            printWindow.print();
        };

        this.showNotification('تم فتح نافذة الطباعة', 'success');
    }

    // Edit review from modal
    editReviewFromModal() {
        if (!this.currentViewingReview) {
            this.showNotification('لا توجد مراجعة للتعديل', 'error');
            return;
        }

        if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
            this.showNotification('ليس لديك صلاحية لتعديل المراجعات', 'error');
            return;
        }

        // Close modal first
        this.closeReviewModal();

        // Fill form with review data
        this.fillReviewForm(this.currentViewingReview);
        this.showReviewForm();
        this.showNotification('تم تحميل بيانات المراجعة للتعديل', 'info');
    }

    // Generate review details HTML
    generateReviewDetailsHTML(review) {
        return `
            <div class="review-details">
                <div class="review-section">
                    <h4><i class="fas fa-info-circle"></i> المعلومات الأساسية</h4>
                    <div class="review-detail">
                        <label>الاسم:</label>
                        <div class="value">${review.reviewerName || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>القسم:</label>
                        <div class="value">${review.reviewerDepartment || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>الإدارة:</label>
                        <div class="value">${review.reviewerManagement || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>تاريخ الحادث:</label>
                        <div class="value">${this.formatDateOnly(review.incidentDate)} في ${review.incidentTime || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>مكان الحادث:</label>
                        <div class="value">${review.incidentLocation || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-exclamation-triangle"></i> تفاصيل الحادث</h4>
                    <div class="review-detail">
                        <label>رقم الحادث في السجل:</label>
                        <div class="value">${review.incidentRecordNumber || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>نوع الحادث:</label>
                        <div class="value">${this.getIncidentTypeLabel(review.incidentType)}</div>
                    </div>
                    <div class="review-detail">
                        <label>المسؤول عن الحادث:</label>
                        <div class="value">${review.incidentResponsible || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>وصف الحادث:</label>
                        <div class="value description">${review.incidentDescription || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-user-check"></i> تقييم الموظف</h4>
                    <div class="review-detail">
                        <label>استعداد الموظف للحادث:</label>
                        <div class="value">${this.getEmployeePreparednessLabel(review.employeePreparedness)}</div>
                    </div>
                    <div class="review-detail">
                        <label>كيفية استجابته للحادث:</label>
                        <div class="value">${this.getEmployeeResponseLabel(review.employeeResponse)}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-search"></i> تحليل الحادث</h4>
                    <div class="review-detail">
                        <label>الأسباب الظاهرية للحادث:</label>
                        <div class="value description">${review.apparentCauses || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>الأسباب الجذرية للحادث:</label>
                        <div class="value description">${review.rootCauses || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>العوامل التي ساهمت في وقوع الحادث:</label>
                        <div class="value description">${review.contributingFactors || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-redo"></i> خطط الاسترداد</h4>
                    <div class="review-detail">
                        <label>خطط الاسترداد التي تم تفعيلها:</label>
                        <div class="value description">${review.recoveryPlansActivated || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>مدى ملائمة خطط الاسترداد:</label>
                        <div class="value description">${review.recoveryPlansSuitability || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-tools"></i> الإجراءات التصحيحية</h4>
                    <div class="review-detail">
                        <label>الإجراءات التصحيحية المتخذة:</label>
                        <div class="value description">${review.correctiveActions || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>المسؤول عن تنفيذ الإجراءات:</label>
                        <div class="value">${review.actionResponsible || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>فترة تنفيذ الإجراءات:</label>
                        <div class="value">${review.implementationPeriod || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-eye"></i> آلية المتابعة</h4>
                    <div class="review-detail">
                        <label>آلية متابعة الإجراءات التصحيحية:</label>
                        <div class="value description">${review.followUpMechanism || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>المسؤول عن المتابعة:</label>
                        <div class="value">${review.followUpResponsible || 'غير محدد'}</div>
                    </div>
                    <div class="review-detail">
                        <label>توقيت إجراء المتابعة:</label>
                        <div class="value">${review.followUpTiming || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-graduation-cap"></i> الدروس المستفادة</h4>
                    <div class="review-detail">
                        <label>الدروس المستفادة من هذا الحادث:</label>
                        <div class="value description">${review.lessonsLearned || 'غير محدد'}</div>
                    </div>
                </div>

                <div class="review-section">
                    <h4><i class="fas fa-calendar"></i> معلومات إضافية</h4>
                    <div class="review-detail">
                        <label>تاريخ إنشاء المراجعة:</label>
                        <div class="value">${this.formatDate(review.createdAt)}</div>
                    </div>
                </div>
            </div>
        `;
    }

    // Helper functions for review labels
    getEmployeePreparednessLabel(value) {
        const labels = {
            'prepared': 'مستعد',
            'partial': 'جزئي',
            'unprepared': 'غير مستعد'
        };
        return labels[value] || value || 'غير محدد';
    }

    getEmployeeResponseLabel(value) {
        const labels = {
            'poor': 'سيئ الاستجابة',
            'partial': 'جزئي',
            'complete': 'كامل'
        };
        return labels[value] || value || 'غير محدد';
    }

    getIncidentTypeLabel(value) {
        const eventType = this.eventTypes.find(et => et.key === value);
        if (eventType) {
            return eventType.label;
        }

        // Fallback to default types
        const defaultTypes = {
            'intrusion': 'محاولة اختراق',
            'malware': 'برمجيات خبيثة',
            'phishing': 'تصيد إلكتروني',
            'data-breach': 'تسريب بيانات',
            'unauthorized-access': 'وصول غير مصرح',
            'system-failure': 'فشل النظام',
            'other': 'أخرى'
        };
        return defaultTypes[value] || value || 'غير محدد';
    }

    // Fill form with review data for editing
    fillReviewForm(review) {
        document.getElementById('reviewerName').value = review.reviewerName || '';
        document.getElementById('reviewerDepartment').value = review.reviewerDepartment || '';
        document.getElementById('reviewerManagement').value = review.reviewerManagement || '';
        document.getElementById('incidentDate').value = review.incidentDate || '';
        document.getElementById('incidentTime').value = review.incidentTime || '';
        document.getElementById('incidentLocation').value = review.incidentLocation || '';
        document.getElementById('incidentDescription').value = review.incidentDescription || '';
        document.getElementById('incidentRecordNumber').value = review.incidentRecordNumber || '';
        document.getElementById('incidentType').value = review.incidentType || '';
        document.getElementById('incidentResponsible').value = review.incidentResponsible || '';
        document.getElementById('employeePreparedness').value = review.employeePreparedness || '';
        document.getElementById('employeeResponse').value = review.employeeResponse || '';
        document.getElementById('apparentCauses').value = review.apparentCauses || '';
        document.getElementById('rootCauses').value = review.rootCauses || '';
        document.getElementById('contributingFactors').value = review.contributingFactors || '';
        document.getElementById('recoveryPlansActivated').value = review.recoveryPlansActivated || '';
        document.getElementById('recoveryPlansSuitability').value = review.recoveryPlansSuitability || '';
        document.getElementById('correctiveActions').value = review.correctiveActions || '';
        document.getElementById('actionResponsible').value = review.actionResponsible || '';
        document.getElementById('implementationPeriod').value = review.implementationPeriod || '';
        document.getElementById('followUpMechanism').value = review.followUpMechanism || '';
        document.getElementById('followUpResponsible').value = review.followUpResponsible || '';
        document.getElementById('followUpTiming').value = review.followUpTiming || '';
        document.getElementById('lessonsLearned').value = review.lessonsLearned || '';
    }

    // Export events to Excel
    exportToExcel() {
        try {
            // Check if user has permission
            if (!this.currentUser || !this.currentUser.permissions.includes('read')) {
                this.showNotification('ليس لديك صلاحية لتصدير البيانات', 'error');
                return;
            }

            // Prepare data for export
            const exportData = this.events.map((event) => ({
                'رقم التسلسل': event.serial || event.serialNumber || `SEC-${String(event.id).padStart(3, '0')}`,
                'عنوان الحدث': event.title || '',
                'نوع الحدث': this.getEventTypeLabel(event.type),
                'الوصف المختصر': event.description,
                'الوصف المفصل': event.detailedDescription || '',
                'مستوى الخطورة': this.getSeverityLabel(event.severity),
                'الحالة': this.getStatusLabel(event.status),
                'التاريخ والوقت': this.formatDate(event.date),
                'الموقع': event.location || 'غير محدد',
                'الأنظمة المتأثرة': event.affectedSystems || 'غير محدد',
                'المسؤول': event.responsiblePerson || 'غير محدد',
                'التكاليف المباشرة': event.directCosts || 0,
                'التكاليف غير المباشرة': event.indirectCosts || 0,
                'إجمالي التكاليف': (parseFloat(event.directCosts) || 0) + (parseFloat(event.indirectCosts) || 0),
                'الملاحظات': event.notes || '',
                'تاريخ الإنشاء': this.formatDate(event.createdAt),
                'المنشئ': event.createdBy || 'غير محدد'
            }));

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Set column widths
            const colWidths = [
                { wch: 15 }, // رقم التسلسل
                { wch: 25 }, // عنوان الحدث
                { wch: 20 }, // نوع الحدث
                { wch: 30 }, // الوصف المختصر
                { wch: 40 }, // الوصف المفصل
                { wch: 15 }, // مستوى الخطورة
                { wch: 12 }, // الحالة
                { wch: 20 }, // التاريخ والوقت
                { wch: 20 }, // الموقع
                { wch: 25 }, // الأنظمة المتأثرة
                { wch: 20 }, // المسؤول
                { wch: 18 }, // التكاليف المباشرة
                { wch: 20 }, // التكاليف غير المباشرة
                { wch: 18 }, // إجمالي التكاليف
                { wch: 35 }, // الملاحظات
                { wch: 20 }, // تاريخ الإنشاء
                { wch: 15 }  // المنشئ
            ];
            ws['!cols'] = colWidths;

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'سجل الأحداث الأمنية');

            // Generate filename with current date
            const currentDate = new Date().toISOString().split('T')[0];
            const filename = `سجل_الأحداث_الأمنية_${currentDate}.xlsx`;

            // Save file
            XLSX.writeFile(wb, filename);

            // Log activity
            this.logActivity('excel_export', `تصدير ${this.events.length} حدث إلى ملف Excel`);
            this.showNotification(`تم تصدير ${this.events.length} حدث بنجاح`, 'success');

        } catch (error) {
            console.error('Error exporting to Excel:', error);
            this.showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
        }
    }

    // Import events from Excel
    importFromExcel(file) {
        if (!file) return;

        try {
            // Check if user has write permission
            if (!this.currentUser || !this.currentUser.permissions.includes('write')) {
                this.showNotification('ليس لديك صلاحية لاستيراد البيانات', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // Get first worksheet
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length === 0) {
                        this.showNotification('الملف فارغ أو لا يحتوي على بيانات صالحة', 'warning');
                        return;
                    }

                    // Show confirmation dialog
                    const confirmMessage = `تم العثور على ${jsonData.length} صف في الملف.\nهل تريد المتابعة مع الاستيراد؟\n\nملاحظة: سيتم إضافة الأحداث الجديدة إلى السجل الموجود.`;

                    if (confirm(confirmMessage)) {
                        // Validate and import data
                        this.processImportedData(jsonData);
                    }

                } catch (error) {
                    console.error('Error processing Excel file:', error);
                    this.showNotification('حدث خطأ أثناء معالجة الملف', 'error');
                }
            };

            reader.readAsArrayBuffer(file);

        } catch (error) {
            console.error('Error importing from Excel:', error);
            this.showNotification('حدث خطأ أثناء استيراد البيانات', 'error');
        }
    }

    // Process imported data from Excel
    processImportedData(jsonData) {
        let importedCount = 0;
        let errorCount = 0;
        const errors = [];

        jsonData.forEach((row, index) => {
            try {
                // Map Excel columns to event object
                const event = {
                    id: Date.now() + index, // Use timestamp + index for unique ID
                    serial: this.getNextSerialNumber(), // Generate automatic serial number
                    title: row['عنوان الحدث'] || '',
                    type: this.mapEventTypeFromLabel(row['نوع الحدث']),
                    description: row['الوصف المختصر'] || row['الوصف'] || '',
                    detailedDescription: row['الوصف المفصل'] || '',
                    severity: this.mapSeverityFromLabel(row['مستوى الخطورة']),
                    status: this.mapStatusFromLabel(row['الحالة']) || 'open',
                    location: row['الموقع'] || '',
                    affectedSystems: row['الأنظمة المتأثرة'] || '',
                    responsiblePerson: row['المسؤول'] || '',
                    directCosts: parseFloat(row['التكاليف المباشرة']) || 0,
                    indirectCosts: parseFloat(row['التكاليف غير المباشرة']) || 0,
                    notes: row['الملاحظات'] || '',
                    date: this.parseExcelDate(row['التاريخ والوقت']) || new Date().toISOString(),
                    createdAt: new Date().toISOString(),
                    createdBy: this.currentUser.username
                };

                // Validate required fields
                if (!event.type || !event.description || !event.severity || !event.title) {
                    errors.push(`الصف ${index + 2}: حقول مطلوبة مفقودة (عنوان الحدث، نوع الحدث، الوصف، مستوى الخطورة)`);
                    errorCount++;
                    return;
                }

                // Add event to array
                this.events.unshift(event);
                importedCount++;

            } catch (error) {
                errors.push(`الصف ${index + 2}: ${error.message}`);
                errorCount++;
            }
        });

        // Save to storage and update UI
        if (importedCount > 0) {
            this.saveToStorage();
            this.renderEvents();
            this.updateStatistics();
            this.updateCharts();
            this.logActivity('excel_import', `استيراد ${importedCount} حدث من ملف Excel`);
        }

        // Show results
        let message = `تم استيراد ${importedCount} حدث بنجاح`;
        if (errorCount > 0) {
            message += `، ${errorCount} صف يحتوي على أخطاء`;
            console.warn('Import errors:', errors);
        }

        this.showNotification(message, importedCount > 0 ? 'success' : 'warning');

        // Reset file input
        document.getElementById('excelFileInput').value = '';
    }

    // Helper functions for mapping Excel data
    mapEventTypeFromLabel(label) {
        const eventType = this.eventTypes.find(et => et.label === label);
        return eventType ? eventType.key : 'other';
    }

    mapSeverityFromLabel(label) {
        const severityMap = {
            'منخفض': 'low',
            'متوسط': 'medium',
            'عالي': 'high',
            'حرج': 'critical'
        };
        return severityMap[label] || 'medium';
    }

    mapStatusFromLabel(label) {
        const statusMap = {
            'مفتوح': 'open',
            'قيد التحقيق': 'investigating',
            'محلول': 'resolved',
            'مغلق': 'closed'
        };
        return statusMap[label] || 'open';
    }

    parseExcelDate(dateStr) {
        if (!dateStr) return null;

        try {
            // Try to parse the date string
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return date.toISOString();
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    // Get labels for export
    getEventTypeLabel(type) {
        const eventType = this.eventTypes.find(et => et.key === type);
        return eventType ? eventType.label : type;
    }

    getSeverityLabel(severity) {
        const severityMap = {
            'low': 'منخفض',
            'medium': 'متوسط',
            'high': 'عالي',
            'critical': 'حرج'
        };
        return severityMap[severity] || severity;
    }

    getStatusLabel(status) {
        const statusMap = {
            'open': 'مفتوح',
            'investigating': 'قيد التحقيق',
            'resolved': 'محلول',
            'closed': 'مغلق'
        };
        return statusMap[status] || status;
    }

    // Generate HTML content for the report
    generateReportHTML(formData) {
        const currentDate = this.formatDateOnly(new Date()); // التاريخ بالتقويم الإفرنجي

        // Helper function to get value from FormData or Map
        const getValue = (key) => {
            if (formData instanceof FormData) {
                return formData.get(key) || '';
            } else if (formData instanceof Map) {
                return formData.get(key) || '';
            } else {
                return formData[key] || '';
            }
        };

        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير المراجعة ما بعد الحادث</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    margin: 20px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    border-bottom: 3px solid #2563eb;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .header h1 {
                    color: #2563eb;
                    margin-bottom: 10px;
                }
                .section {
                    margin-bottom: 25px;
                    padding: 15px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                }
                .section h2 {
                    color: #2563eb;
                    border-bottom: 2px solid #dbeafe;
                    padding-bottom: 8px;
                    margin-bottom: 15px;
                }
                .field {
                    margin-bottom: 12px;
                }
                .field label {
                    font-weight: bold;
                    color: #374151;
                    display: inline-block;
                    min-width: 200px;
                }
                .field-value {
                    margin-top: 5px;
                    padding: 8px;
                    background-color: #f9fafb;
                    border-radius: 4px;
                    border: 1px solid #e5e7eb;
                }
                .row {
                    display: flex;
                    gap: 20px;
                    margin-bottom: 15px;
                }
                .row .field {
                    flex: 1;
                }
                @media print {
                    body { margin: 0; }
                    .section { break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير المراجعة ما بعد الحادث</h1>
                <p>تاريخ إنشاء التقرير: ${currentDate}</p>
            </div>

            <div class="section">
                <h2>المعلومات الأساسية</h2>
                <div class="row">
                    <div class="field">
                        <label>الاسم:</label>
                        <div class="field-value">${getValue('reviewerName')}</div>
                    </div>
                    <div class="field">
                        <label>القسم:</label>
                        <div class="field-value">${getValue('reviewerDepartment')}</div>
                    </div>
                    <div class="field">
                        <label>الإدارة:</label>
                        <div class="field-value">${getValue('reviewerManagement')}</div>
                    </div>
                </div>
                <div class="row">
                    <div class="field">
                        <label>التاريخ:</label>
                        <div class="field-value">${getValue('incidentDate')}</div>
                    </div>
                    <div class="field">
                        <label>الوقت:</label>
                        <div class="field-value">${getValue('incidentTime')}</div>
                    </div>
                </div>
                <div class="field">
                    <label>المكان:</label>
                    <div class="field-value">${getValue('incidentLocation')}</div>
                </div>
            </div>

            <div class="section">
                <h2>تفاصيل الحادث</h2>
                <div class="field">
                    <label>وصف الحادث:</label>
                    <div class="field-value">${getValue('incidentDescription')}</div>
                </div>
                <div class="row">
                    <div class="field">
                        <label>رقم الحادث في السجل:</label>
                        <div class="field-value">${getValue('incidentRecordNumber')}</div>
                    </div>
                    <div class="field">
                        <label>نوع الحادث:</label>
                        <div class="field-value">${this.getIncidentTypeLabel(getValue('incidentType'))}</div>
                    </div>
                </div>
                <div class="field">
                    <label>المسؤول عن الحادث:</label>
                    <div class="field-value">${getValue('incidentResponsible')}</div>
                </div>
            </div>

            <div class="section">
                <h2>تقييم الموظف</h2>
                <div class="row">
                    <div class="field">
                        <label>استعداد الموظف للحادث:</label>
                        <div class="field-value">${this.getPreparednessLabel(getValue('employeePreparedness'))}</div>
                    </div>
                    <div class="field">
                        <label>كيفية استجابته للحادث:</label>
                        <div class="field-value">${this.getResponseLabel(getValue('employeeResponse'))}</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>تحليل الحادث</h2>
                <div class="field">
                    <label>الأسباب الظاهرية للحادث:</label>
                    <div class="field-value">${getValue('apparentCauses')}</div>
                </div>
                <div class="field">
                    <label>الأسباب الجذرية للحادث:</label>
                    <div class="field-value">${getValue('rootCauses')}</div>
                </div>
                <div class="field">
                    <label>العوامل التي ساهمت في وقوع الحادث:</label>
                    <div class="field-value">${getValue('contributingFactors')}</div>
                </div>
            </div>

            <div class="section">
                <h2>خطط الاسترداد</h2>
                <div class="field">
                    <label>خطط الاسترداد التي تم تفعيلها:</label>
                    <div class="field-value">${getValue('recoveryPlansActivated')}</div>
                </div>
                <div class="field">
                    <label>مدى ملائمة خطط الاسترداد لمعالجة أثر الحادث:</label>
                    <div class="field-value">${getValue('recoveryPlansSuitability')}</div>
                </div>
            </div>

            <div class="section">
                <h2>الإجراءات التصحيحية</h2>
                <div class="field">
                    <label>الإجراءات التصحيحية التي تم اتخاذها:</label>
                    <div class="field-value">${getValue('correctiveActions')}</div>
                </div>
                <div class="row">
                    <div class="field">
                        <label>المسؤول عن تنفيذ الإجراءات:</label>
                        <div class="field-value">${getValue('actionResponsible')}</div>
                    </div>
                    <div class="field">
                        <label>فترة تنفيذ الإجراءات:</label>
                        <div class="field-value">${getValue('implementationPeriod')}</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>آلية المتابعة</h2>
                <div class="field">
                    <label>آلية متابعة الإجراءات التصحيحية:</label>
                    <div class="field-value">${getValue('followUpMechanism')}</div>
                </div>
                <div class="row">
                    <div class="field">
                        <label>المسؤول عن متابعة الإجراءات:</label>
                        <div class="field-value">${getValue('followUpResponsible')}</div>
                    </div>
                    <div class="field">
                        <label>توقيت إجراء المتابعة:</label>
                        <div class="field-value">${getValue('followUpTiming')}</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>الدروس المستفادة</h2>
                <div class="field">
                    <label>الدروس المستفادة من هذا الحادث:</label>
                    <div class="field-value">${getValue('lessonsLearned')}</div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Helper functions for report labels
    getPreparednessLabel(value) {
        const labels = {
            'prepared': 'مستعد',
            'partial': 'جزئي',
            'unprepared': 'غير مستعد'
        };
        return labels[value] || value;
    }

    getResponseLabel(value) {
        const labels = {
            'poor': 'سيئ الاستجابة',
            'partial': 'جزئي',
            'complete': 'كامل'
        };
        return labels[value] || value;
    }

    saveToStorage() {
        try {
            localStorage.setItem('securityEvents', JSON.stringify(this.events));
            localStorage.setItem('currentEventId', this.currentEventId.toString());
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            this.showNotification('خطأ في حفظ البيانات. قد تكون مساحة التخزين ممتلئة.', 'error');
        }
    }

    // Setup Floating Action Button
    setupFloatingActionButton() {
        const fab = document.getElementById('scrollToTopBtn');
        if (!fab) return;

        // Show/hide FAB based on scroll position
        const toggleFAB = () => {
            if (window.scrollY > 300) {
                fab.style.opacity = '1';
                fab.style.visibility = 'visible';
                fab.style.transform = 'scale(1)';
            } else {
                fab.style.opacity = '0';
                fab.style.visibility = 'hidden';
                fab.style.transform = 'scale(0.8)';
            }
        };

        // Initial state
        fab.style.opacity = '0';
        fab.style.visibility = 'hidden';
        fab.style.transform = 'scale(0.8)';
        fab.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

        // Listen for scroll events
        window.addEventListener('scroll', toggleFAB);

        // Handle click event
        fab.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Import event to post-incident review form
    importEventToReview() {
        const serialInput = document.getElementById('eventSerialImport');
        const serialNumber = serialInput.value.trim();

        if (!serialNumber) {
            this.showNotification('يرجى إدخال رقم التسلسل', 'warning');
            serialInput.focus();
            return;
        }

        // Find the event by serial number
        const event = this.events.find(e =>
            (e.serial && e.serial.toLowerCase() === serialNumber.toLowerCase()) ||
            (e.serialNumber && e.serialNumber.toLowerCase() === serialNumber.toLowerCase())
        );

        if (!event) {
            this.showNotification(`لم يتم العثور على حدث برقم التسلسل: ${serialNumber}`, 'error');
            serialInput.focus();
            return;
        }

        // Fill the post-incident review form with event data
        this.fillReviewFormWithEvent(event);

        // Clear the import input
        serialInput.value = '';

        // Show success message
        this.showNotification(`تم استيراد الحدث ${event.serial || event.serialNumber} بنجاح`, 'success');

        // Scroll to the form
        document.getElementById('incidentDescription').focus();
    }

    // Fill post-incident review form with event data
    fillReviewFormWithEvent(event) {
        try {
            // Basic incident details
            const incidentRecordNumber = document.getElementById('incidentRecordNumber');
            if (incidentRecordNumber) {
                incidentRecordNumber.value = event.serial || event.serialNumber || '';
            }

            // Build comprehensive description
            let description = event.title || '';
            if (event.description) {
                description += (description ? '\n\n' : '') + 'الوصف:\n' + event.description;
            }
            if (event.detailedDescription) {
                description += '\n\nتفاصيل إضافية:\n' + event.detailedDescription;
            }
            if (event.affectedSystems) {
                description += '\n\nالأنظمة المتأثرة:\n' + event.affectedSystems;
            }
            if (event.notes) {
                description += '\n\nملاحظات:\n' + event.notes;
            }

            const incidentDescription = document.getElementById('incidentDescription');
            if (incidentDescription) {
                incidentDescription.value = description;
            }

            // Set incident type if it exists in the dropdown
            const incidentTypeSelect = document.getElementById('incidentType');
            if (incidentTypeSelect && event.type) {
                // Try to find matching option
                const options = incidentTypeSelect.options;
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value === event.type) {
                        incidentTypeSelect.selectedIndex = i;
                        break;
                    }
                }
            }

            // Set responsible person
            const incidentResponsible = document.getElementById('incidentResponsible');
            if (incidentResponsible) {
                incidentResponsible.value = event.responsiblePerson || '';
            }

            // Set incident date and time
            if (event.date) {
                try {
                    const eventDate = new Date(event.date);
                    const incidentDate = document.getElementById('incidentDate');
                    const incidentTime = document.getElementById('incidentTime');

                    if (incidentDate) {
                        incidentDate.value = eventDate.toISOString().split('T')[0];
                    }
                    if (incidentTime) {
                        incidentTime.value = eventDate.toTimeString().split(' ')[0].substring(0, 5);
                    }
                } catch (dateError) {
                    console.warn('Error parsing event date:', dateError);
                }
            }

            // Set location if available
            const incidentLocation = document.getElementById('incidentLocation');
            if (incidentLocation && event.location) {
                incidentLocation.value = event.location;
            }

            // Set severity information in a field if available
            const severityInfo = this.getSeverityLabel(event.severity);
            if (severityInfo && severityInfo !== 'غير محدد') {
                const currentDesc = document.getElementById('incidentDescription').value;
                document.getElementById('incidentDescription').value =
                    currentDesc + '\n\nمستوى الخطورة: ' + severityInfo;
            }

            // Add cost information if available
            if (event.directCosts || event.indirectCosts) {
                const currentDesc = document.getElementById('incidentDescription').value;
                let costInfo = '\n\nمعلومات التكلفة:';
                if (event.directCosts) costInfo += '\nالتكاليف المباشرة: ' + event.directCosts;
                if (event.indirectCosts) costInfo += '\nالتكاليف غير المباشرة: ' + event.indirectCosts;
                document.getElementById('incidentDescription').value = currentDesc + costInfo;
            }

            // Add visual feedback with animation
            this.showImportSuccess();

        } catch (error) {
            console.error('Error filling review form:', error);
            this.showNotification('حدث خطأ أثناء ملء النموذج', 'error');
        }
    }

    // Show import success animation
    showImportSuccess() {
        const importSection = document.querySelector('.import-event-section');
        if (importSection) {
            // Add success class
            importSection.classList.add('success');
            importSection.style.background = 'linear-gradient(135deg, rgba(5, 150, 105, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%)';
            importSection.style.borderColor = '#10b981';
            importSection.style.transform = 'scale(1.02)';

            // Reset after animation
            setTimeout(() => {
                importSection.classList.remove('success');
                importSection.style.background = '';
                importSection.style.borderColor = '';
                importSection.style.transform = '';
            }, 2500);
        }
    }

    // Validate serial input in real-time
    validateSerialInput(input) {
        const serialNumber = input.value.trim();
        const importBtn = document.getElementById('importEventBtn');

        if (!serialNumber) {
            // Reset button state
            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-download"></i> استيراد الحدث';
                importBtn.className = 'btn btn-info';
            }
            input.style.borderColor = '';
            input.style.backgroundColor = '';
            this.hideSuggestions();
            return;
        }

        // Find matching events
        const matchingEvents = this.events.filter(e => {
            const eventSerial = e.serial || e.serialNumber || '';
            const eventTitle = e.title || '';
            return eventSerial.toLowerCase().includes(serialNumber.toLowerCase()) ||
                   eventTitle.toLowerCase().includes(serialNumber.toLowerCase());
        }).slice(0, 5); // Limit to 5 suggestions

        // Show suggestions if there are matches
        if (matchingEvents.length > 0) {
            this.showSuggestions(matchingEvents, serialNumber);
        } else {
            this.hideSuggestions();
        }

        // Check for exact match
        const exactMatch = this.events.find(e =>
            (e.serial && e.serial.toLowerCase() === serialNumber.toLowerCase()) ||
            (e.serialNumber && e.serialNumber.toLowerCase() === serialNumber.toLowerCase())
        );

        if (exactMatch) {
            // Event found - show success state
            input.style.borderColor = '#10b981';
            input.style.backgroundColor = 'rgba(16, 185, 129, 0.05)';

            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = `<i class="fas fa-check"></i> استيراد "${exactMatch.title.substring(0, 20)}${exactMatch.title.length > 20 ? '...' : ''}"`;
                importBtn.className = 'btn btn-info';
            }
        } else if (matchingEvents.length > 0) {
            // Partial matches found
            input.style.borderColor = '#3b82f6';
            input.style.backgroundColor = 'rgba(59, 130, 246, 0.05)';

            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = `<i class="fas fa-search"></i> ${matchingEvents.length} نتيجة متطابقة`;
                importBtn.className = 'btn btn-info';
            }
        } else {
            // No matches found
            input.style.borderColor = '#f59e0b';
            input.style.backgroundColor = 'rgba(245, 158, 11, 0.05)';

            if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> لا توجد نتائج';
                importBtn.className = 'btn btn-secondary';
            }
        }
    }

    showSuggestions(events, searchTerm) {
        const suggestionsDiv = document.getElementById('eventSuggestions');
        if (!suggestionsDiv) return;

        const suggestionsHTML = events.map(event => {
            const serial = event.serial || event.serialNumber || '';
            const title = event.title || 'بدون عنوان';
            const highlightedSerial = this.highlightText(serial, searchTerm);
            const highlightedTitle = this.highlightText(title, searchTerm);

            return `
                <div class="suggestion-item" data-serial="${serial}">
                    <div class="suggestion-title">${highlightedTitle}</div>
                    <div class="suggestion-serial">${highlightedSerial}</div>
                </div>
            `;
        }).join('');

        suggestionsDiv.innerHTML = suggestionsHTML;
        suggestionsDiv.classList.add('show');

        // Add click handlers
        suggestionsDiv.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const serial = item.dataset.serial;
                document.getElementById('eventSerialImport').value = serial;
                this.hideSuggestions();
                this.validateSerialInput(document.getElementById('eventSerialImport'));
            });
        });
    }

    // Hide suggestions
    hideSuggestions() {
        const suggestionsDiv = document.getElementById('eventSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.classList.remove('show');
        }
    }

    // Highlight matching text
    highlightText(text, searchTerm) {
        if (!searchTerm) return text;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // Handle keyboard navigation in suggestions
    handleSuggestionNavigation(e) {
        const suggestionsDiv = document.getElementById('eventSuggestions');
        if (!suggestionsDiv || !suggestionsDiv.classList.contains('show')) return;

        const suggestions = suggestionsDiv.querySelectorAll('.suggestion-item');
        const currentSelected = suggestionsDiv.querySelector('.suggestion-item.selected');
        let selectedIndex = currentSelected ? Array.from(suggestions).indexOf(currentSelected) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
                this.updateSelectedSuggestion(suggestions, selectedIndex);
                break;

            case 'ArrowUp':
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                this.updateSelectedSuggestion(suggestions, selectedIndex);
                break;

            case 'Enter':
                e.preventDefault();
                if (currentSelected) {
                    const serial = currentSelected.dataset.serial;
                    document.getElementById('eventSerialImport').value = serial;
                    this.hideSuggestions();
                    this.validateSerialInput(document.getElementById('eventSerialImport'));
                } else {
                    this.importEventToReview();
                }
                break;

            case 'Escape':
                e.preventDefault();
                this.hideSuggestions();
                break;
        }
    }

    // Update selected suggestion
    updateSelectedSuggestion(suggestions, selectedIndex) {
        suggestions.forEach((item, index) => {
            if (index === selectedIndex) {
                item.classList.add('selected');
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('selected');
            }
        });
    }

    // User Profile Functions
    showUserProfile() {
        const modal = document.getElementById('userProfileModal');
        if (!modal) return;

        // Close user menu
        if (this.domElements.userMenu) {
            this.domElements.userMenu.classList.remove('active');
        }

        // Load current user data
        this.loadProfileData();

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeUserProfile() {
        const modal = document.getElementById('userProfileModal');
        if (!modal) return;

        modal.classList.remove('active');
        document.body.style.overflow = 'auto';

        // Reset forms
        document.getElementById('profileInfoForm').reset();
        document.getElementById('changePasswordForm').reset();

        // Reset password strength indicator
        this.resetPasswordStrength();
    }

    loadProfileData() {
        if (!this.currentUser) return;

        // Fill profile form
        document.getElementById('profileFullName').value = this.currentUser.fullName || '';
        document.getElementById('profileUsername').value = this.currentUser.username || '';
        document.getElementById('profileEmail').value = this.currentUser.email || '';
        document.getElementById('profileRole').value = this.getRoleLabel(this.currentUser.role) || '';
    }

    switchTab(tabId) {
        // Remove active class from all tabs and content
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    updateProfile() {
        const form = document.getElementById('profileInfoForm');
        const formData = new FormData(form);

        const fullName = formData.get('fullName').trim();
        const email = formData.get('email').trim();

        if (!fullName || !email) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return;
        }

        // Update current user
        this.currentUser.fullName = fullName;
        this.currentUser.email = email;

        // Update in storage
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        users[this.currentUser.username] = this.currentUser;
        localStorage.setItem('systemUsers', JSON.stringify(users));

        // Update display name
        if (this.domElements.currentUserName) {
            this.domElements.currentUserName.textContent = fullName;
        }

        this.logActivity('profile_update', `تحديث الملف الشخصي للمستخدم ${fullName}`);
        this.showNotification('تم تحديث الملف الشخصي بنجاح', 'success');
    }

    changePassword() {
        const form = document.getElementById('changePasswordForm');
        const formData = new FormData(form);

        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        if (!currentPassword || !newPassword || !confirmPassword) {
            this.showNotification('يرجى ملء جميع الحقول', 'error');
            return;
        }

        // Verify current password
        const hashedCurrentPassword = this.hashPassword(currentPassword);
        if (hashedCurrentPassword !== this.currentUser.password) {
            this.showNotification('كلمة المرور الحالية غير صحيحة', 'error');
            return;
        }

        // Check password length
        if (newPassword.length < 6) {
            this.showNotification('يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل', 'error');
            return;
        }

        // Check password confirmation
        if (newPassword !== confirmPassword) {
            this.showNotification('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error');
            return;
        }

        // Update password
        this.currentUser.password = this.hashPassword(newPassword);

        // Update in storage
        const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
        users[this.currentUser.username] = this.currentUser;
        localStorage.setItem('systemUsers', JSON.stringify(users));

        // Clear form
        form.reset();
        this.resetPasswordStrength();

        this.logActivity('password_change', `تغيير كلمة المرور للمستخدم ${this.currentUser.fullName}`);
        this.showNotification('تم تغيير كلمة المرور بنجاح', 'success');
    }

    checkPasswordStrength(password) {
        const strengthBar = document.querySelector('.strength-fill');
        const strengthText = document.querySelector('.strength-text');

        if (!strengthBar || !strengthText) return;

        let strength = 0;
        let strengthLabel = 'ضعيف';
        let strengthClass = 'weak';

        if (password.length >= 6) strength += 1;
        if (password.length >= 8) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;

        // Reset classes
        strengthBar.className = 'strength-fill';
        strengthText.className = 'strength-text';

        if (strength <= 2) {
            strengthLabel = 'ضعيف';
            strengthClass = 'weak';
        } else if (strength <= 3) {
            strengthLabel = 'متوسط';
            strengthClass = 'fair';
        } else if (strength <= 4) {
            strengthLabel = 'جيد';
            strengthClass = 'good';
        } else {
            strengthLabel = 'قوي';
            strengthClass = 'strong';
        }

        strengthBar.classList.add(strengthClass);
        strengthText.classList.add(strengthClass);
        strengthText.textContent = `قوة كلمة المرور: ${strengthLabel}`;
    }

    resetPasswordStrength() {
        const strengthBar = document.querySelector('.strength-fill');
        const strengthText = document.querySelector('.strength-text');

        if (strengthBar) {
            strengthBar.className = 'strength-fill';
        }

        if (strengthText) {
            strengthText.className = 'strength-text';
            strengthText.textContent = 'قوة كلمة المرور';
        }
    }
}

// Initialize the application
const securityManager = new SecurityEventsManager();

// Add global logout function for testing
window.testLogout = () => {
    console.log('Test logout called');
    if (securityManager) {
        securityManager.simpleLogout();
    } else {
        console.error('Security manager not initialized');
    }
};

// Test logout button after DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, testing logout button...');

    setTimeout(() => {
        const logoutBtn = document.getElementById('logoutBtn');
        console.log('Testing logout button after delay:', logoutBtn);

        if (logoutBtn) {
            console.log('Logout button found, adding direct click listener');

            // Remove any existing listeners and add a new one
            logoutBtn.removeEventListener('click', handleLogoutClick);
            logoutBtn.addEventListener('click', handleLogoutClick);
        } else {
            console.error('Logout button still not found after delay');
        }
    }, 2000);
});

// Direct logout handler
function handleLogoutClick(e) {
    console.log('Direct logout handler called');
    e.preventDefault();
    e.stopPropagation();

    // Close user menu
    const userMenu = document.getElementById('userMenu');
    if (userMenu) {
        userMenu.classList.remove('active');
    }

    // Call logout
    if (securityManager) {
        securityManager.simpleLogout();
    } else {
        console.error('Security manager not available');
        // Fallback logout
        if (confirm('هل أنت متأكد من أنك تريد تسجيل الخروج من النظام؟')) {
            localStorage.clear();
            sessionStorage.clear();
            window.location.href = 'login.html';
        }
    }
}

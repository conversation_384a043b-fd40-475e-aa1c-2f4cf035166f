
// Enhanced Authentication Script
// سكريبت مصادقة محسن

class EnhancedAuthManager {
    constructor() {
        this.users = {};
        this.userSessions = {};
        this.currentUser = null;
        this.sessionCheckInterval = null;
        this.init();
    }
    
    init() {
        this.loadUsers();
        this.loadSessions();
        this.startSessionMonitoring();
        this.checkExistingSession();
    }
    
    // تحميل المستخدمين
    loadUsers() {
        try {
            const storedUsers = localStorage.getItem('systemUsers');
            if (storedUsers) {
                this.users = JSON.parse(storedUsers);
                console.log('تم تحميل المستخدمين من localStorage');
            } else {
                console.log('لا توجد بيانات مستخدمين في localStorage');
                this.createDefaultUsers();
            }
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
            this.createDefaultUsers();
        }
    }
    
    // إنشاء المستخدمين الافتراضيين
    createDefaultUsers() {
        this.users = {
            "admin": {
                "id": "admin",
                "username": "admin",
                "password": this.hashPassword("admin123"),
                "fullName": "مدير النظام",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null
            },
            "analyst": {
                "id": "analyst",
                "username": "analyst",
                "password": this.hashPassword("analyst123"),
                "fullName": "محلل أمني",
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": ["read", "write", "view_analytics"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null
            },
            "operator": {
                "id": "operator",
                "username": "operator",
                "password": this.hashPassword("operator123"),
                "fullName": "مشغل النظام",
                "email": "<EMAIL>",
                "role": "operator",
                "permissions": ["read"],
                "isActive": true,
                "createdAt": new Date().toISOString(),
                "lastLogin": null
            }
        };
        
        this.saveUsers();
        console.log('تم إنشاء المستخدمين الافتراضيين');
    }
    
    // حفظ المستخدمين
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('خطأ في حفظ المستخدمين:', error);
        }
    }
    
    // تحميل الجلسات
    loadSessions() {
        try {
            const storedSessions = localStorage.getItem('userSessions');
            if (storedSessions) {
                this.userSessions = JSON.parse(storedSessions);
            }
        } catch (error) {
            console.error('خطأ في تحميل الجلسات:', error);
            this.userSessions = {};
        }
    }
    
    // حفظ الجلسات
    saveSessions() {
        try {
            localStorage.setItem('userSessions', JSON.stringify(this.userSessions));
        } catch (error) {
            console.error('خطأ في حفظ الجلسات:', error);
        }
    }
    
    // تشفير كلمة المرور
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }
    
    // معالجة تسجيل الدخول
    async handleLogin(username, password, rememberMe = false) {
        try {
            console.log('بدء عملية تسجيل الدخول للمستخدم:', username);
            
            // التحقق من صحة البيانات
            if (!username || !password) {
                throw new Error('يرجى إدخال اسم المستخدم وكلمة المرور');
            }
            
            // البحث عن المستخدم
            const user = this.users[username];
            if (!user) {
                throw new Error('اسم المستخدم غير موجود');
            }
            
            // فحص حالة الحساب
            if (!user.isActive) {
                throw new Error('هذا الحساب معطل');
            }
            
            // التحقق من كلمة المرور
            const hashedPassword = this.hashPassword(password);
            if (user.password !== hashedPassword) {
                throw new Error('كلمة المرور غير صحيحة');
            }
            
            // إنشاء جلسة جديدة
            const sessionId = this.generateSessionId();
            const session = {
                sessionId: sessionId,
                userId: user.id,
                username: user.username,
                role: user.role,
                permissions: user.permissions,
                createdAt: new Date().toISOString(),
                expiresAt: rememberMe ? 
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : // 30 يوم
                    new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 ساعات
                rememberMe: rememberMe,
                loginMethod: 'standard'
            };
            
            // حفظ الجلسة
            this.userSessions[sessionId] = session;
            this.saveSessions();
            localStorage.setItem('currentSession', sessionId);
            
            // تحديث آخر دخول
            user.lastLogin = new Date().toISOString();
            this.users[username] = user;
            this.saveUsers();
            
            // تسجيل النشاط
            this.logActivity('login', `تسجيل دخول ناجح للمستخدم ${user.fullName}`, {
                username: user.username,
                sessionId: sessionId,
                loginMethod: 'standard'
            });
            
            // تعيين المستخدم الحالي
            this.currentUser = user;
            
            console.log('تم تسجيل الدخول بنجاح');
            
            // الانتقال للصفحة الرئيسية فوراً
            this.redirectToMainPage();
            
            return { success: true, user: user, session: session };
            
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            
            // تسجيل محاولة فاشلة
            this.logActivity('login_failed', `محاولة تسجيل دخول فاشلة`, {
                username: username,
                error: error.message
            });
            
            throw error;
        }
    }
    
    // فحص الجلسة الموجودة
    checkExistingSession() {
        try {
            const sessionId = localStorage.getItem('currentSession');
            if (!sessionId) {
                console.log('لا توجد جلسة حالية');
                return false;
            }
            
            const session = this.userSessions[sessionId];
            if (!session) {
                console.log('جلسة غير صالحة');
                localStorage.removeItem('currentSession');
                return false;
            }
            
            // فحص انتهاء الصلاحية
            const now = new Date();
            const expiresAt = new Date(session.expiresAt);
            
            if (now > expiresAt) {
                console.log('انتهت صلاحية الجلسة');
                this.logout();
                return false;
            }
            
            // الجلسة صالحة
            const user = this.users[session.username];
            if (user && user.isActive) {
                this.currentUser = user;
                console.log('تم العثور على جلسة صالحة للمستخدم:', user.username);
                
                // إذا كنا في صفحة تسجيل الدخول، انتقل للصفحة الرئيسية
                if (window.location.pathname.includes('login.html') || 
                    window.location.pathname.includes('instant-login.html')) {
                    this.redirectToMainPage();
                }
                
                return true;
            } else {
                console.log('المستخدم غير موجود أو معطل');
                this.logout();
                return false;
            }
            
        } catch (error) {
            console.error('خطأ في فحص الجلسة:', error);
            return false;
        }
    }
    
    // الانتقال للصفحة الرئيسية
    redirectToMainPage() {
        console.log('الانتقال للصفحة الرئيسية...');
        window.location.href = 'index.html';
    }
    
    // تسجيل الخروج
    logout() {
        try {
            const sessionId = localStorage.getItem('currentSession');
            
            if (sessionId && this.userSessions[sessionId]) {
                // تسجيل النشاط
                this.logActivity('logout', 'تسجيل خروج', {
                    username: this.currentUser ? this.currentUser.username : 'unknown',
                    sessionId: sessionId
                });
                
                // حذف الجلسة
                delete this.userSessions[sessionId];
                this.saveSessions();
            }
            
            // مسح البيانات المحلية
            localStorage.removeItem('currentSession');
            this.currentUser = null;
            
            console.log('تم تسجيل الخروج');
            
            // الانتقال لصفحة تسجيل الدخول
            window.location.href = 'login.html';
            
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        }
    }
    
    // إنشاء معرف جلسة
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // تسجيل النشاط
    logActivity(type, description, details = {}) {
        try {
            const activities = JSON.parse(localStorage.getItem('activityLog')) || [];
            
            const activity = {
                id: 'activity_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
                type: type,
                description: description,
                timestamp: new Date().toISOString(),
                details: details
            };
            
            activities.push(activity);
            
            // الاحتفاظ بآخر 1000 نشاط فقط
            if (activities.length > 1000) {
                activities.splice(0, activities.length - 1000);
            }
            
            localStorage.setItem('activityLog', JSON.stringify(activities));
            
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }
    
    // مراقبة الجلسات
    startSessionMonitoring() {
        // فحص الجلسة كل دقيقة
        this.sessionCheckInterval = setInterval(() => {
            this.checkExistingSession();
        }, 60000);
    }
    
    // إيقاف مراقبة الجلسات
    stopSessionMonitoring() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
            this.sessionCheckInterval = null;
        }
    }
    
    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }
    
    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }
}

// إنشاء مثيل عام من مدير المصادقة
window.enhancedAuthManager = new EnhancedAuthManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedAuthManager;
}

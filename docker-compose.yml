version: '3.8'

services:
  isms:
    build: .
    container_name: information-security-management-system
    ports:
      - "80:80"
      - "8080:80"  # منفذ بديل
    volumes:
      - ./data:/data  # لحفظ البيانات
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
      - TZ=Asia/Riyadh  # المنطقة الزمنية
    restart: unless-stopped
    networks:
      - isms-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.isms.rule=Host(`isms.local`)"
      - "traefik.http.services.isms.loadbalancer.server.port=80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/login.html"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # خدمة النسخ الاحتياطي (اختيارية)
  backup:
    image: alpine:latest
    container_name: isms-backup
    volumes:
      - ./data:/data
      - ./backups:/backups
    environment:
      - TZ=Asia/Riyadh
    command: |
      sh -c "
        echo 'إعداد النسخ الاحتياطي التلقائي...'
        while true; do
          DATE=$$(date +%Y%m%d_%H%M%S)
          tar -czf /backups/isms_backup_$$DATE.tar.gz -C /data .
          echo 'تم إنشاء نسخة احتياطية: isms_backup_'$$DATE'.tar.gz'
          # الاحتفاظ بآخر 7 نسخ احتياطية فقط
          ls -t /backups/isms_backup_*.tar.gz | tail -n +8 | xargs -r rm
          sleep 86400  # نسخة احتياطية كل 24 ساعة
        done
      "
    restart: unless-stopped
    networks:
      - isms-network
    depends_on:
      - isms

networks:
  isms-network:
    driver: bridge
    name: isms-network

volumes:
  isms-data:
    driver: local
    name: isms-data

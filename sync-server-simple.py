#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم مزامنة مبسط بدون رموز تعبيرية
Simple Sync Server without Emojis
"""

import http.server
import socketserver
import json
import os
import threading
import time
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import uuid

class SimpleSyncHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.data_store = SimpleDataStore()
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')
        self.send_header('Cache-Control', 'no-cache')
    
    def do_GET(self):
        """Handle GET requests"""
        self.send_cors_headers()
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        try:
            if path == '/api/sync/users':
                self.handle_get_users()
            elif path == '/api/sync/events':
                self.handle_get_events()
            elif path == '/api/sync/activities':
                self.handle_get_activities()
            elif path == '/api/sync/status':
                self.handle_get_status()
            elif path == '/api/sync/all':
                self.handle_get_all_data()
            else:
                self.send_error(404, "Endpoint not found")
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def do_POST(self):
        """Handle POST requests"""
        self.send_cors_headers()
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            if path == '/api/sync/users':
                self.handle_sync_users(data)
            elif path == '/api/sync/events':
                self.handle_sync_events(data)
            elif path == '/api/sync/activities':
                self.handle_sync_activities(data)
            elif path == '/api/sync/full':
                self.handle_full_sync(data)
            else:
                self.send_error(404, "Endpoint not found")
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def handle_get_users(self):
        """Get all users"""
        users = self.data_store.get_users()
        self.send_json_response(users)
    
    def handle_get_events(self):
        """Get all security events"""
        events = self.data_store.get_events()
        self.send_json_response(events)
    
    def handle_get_activities(self):
        """Get all activities"""
        activities = self.data_store.get_activities()
        self.send_json_response(activities)
    
    def handle_get_status(self):
        """Get server status"""
        status = {
            'status': 'online',
            'timestamp': datetime.now().isoformat(),
            'users_count': len(self.data_store.get_users()),
            'events_count': len(self.data_store.get_events()),
            'activities_count': len(self.data_store.get_activities()),
            'last_sync': self.data_store.get_last_sync()
        }
        self.send_json_response(status)
    
    def handle_get_all_data(self):
        """Get all data"""
        all_data = {
            'users': self.data_store.get_users(),
            'events': self.data_store.get_events(),
            'activities': self.data_store.get_activities(),
            'timestamp': datetime.now().isoformat()
        }
        self.send_json_response(all_data)
    
    def handle_sync_users(self, data):
        """Sync users data"""
        result = self.data_store.sync_users(data)
        self.send_json_response(result)
    
    def handle_sync_events(self, data):
        """Sync security events"""
        result = self.data_store.sync_events(data)
        self.send_json_response(result)
    
    def handle_sync_activities(self, data):
        """Sync activities"""
        result = self.data_store.sync_activities(data)
        self.send_json_response(result)
    
    def handle_full_sync(self, data):
        """Handle full data synchronization"""
        result = self.data_store.full_sync(data)
        self.send_json_response(result)
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

class SimpleDataStore:
    def __init__(self):
        self.data_dir = "data"
        self.users_file = os.path.join(self.data_dir, "users.json")
        self.events_file = os.path.join(self.data_dir, "events.json")
        self.activities_file = os.path.join(self.data_dir, "activities.json")
        self.sync_log_file = os.path.join(self.data_dir, "sync_log.json")
        
        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Initialize data files
        self.init_data_files()
    
    def init_data_files(self):
        """Initialize data files if they don't exist"""
        if not os.path.exists(self.users_file):
            self.save_json(self.users_file, {})
        
        if not os.path.exists(self.events_file):
            self.save_json(self.events_file, [])
        
        if not os.path.exists(self.activities_file):
            self.save_json(self.activities_file, [])
        
        if not os.path.exists(self.sync_log_file):
            self.save_json(self.sync_log_file, [])
    
    def load_json(self, file_path):
        """Load JSON data from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {} if 'users' in file_path else []
    
    def save_json(self, file_path, data):
        """Save JSON data to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving {file_path}: {e}")
            return False
    
    def get_users(self):
        """Get all users"""
        return self.load_json(self.users_file)
    
    def get_events(self):
        """Get all security events"""
        return self.load_json(self.events_file)
    
    def get_activities(self):
        """Get all activities"""
        return self.load_json(self.activities_file)
    
    def get_last_sync(self):
        """Get last sync timestamp"""
        sync_log = self.load_json(self.sync_log_file)
        if sync_log:
            return sync_log[-1].get('timestamp')
        return None
    
    def sync_users(self, new_users):
        """Sync users data"""
        current_users = self.get_users()
        
        # Merge users
        for username, user_data in new_users.items():
            if username not in current_users or \
               user_data.get('lastModified', 0) > current_users.get(username, {}).get('lastModified', 0):
                current_users[username] = user_data
        
        # Save updated users
        if self.save_json(self.users_file, current_users):
            self.log_sync('users', len(new_users))
            return {'status': 'success', 'message': 'Users synced successfully'}
        else:
            return {'status': 'error', 'message': 'Failed to save users'}
    
    def sync_events(self, new_events):
        """Sync security events"""
        current_events = self.get_events()
        current_ids = {event.get('id') for event in current_events}
        
        # Add new events
        added_count = 0
        for event in new_events:
            if event.get('id') not in current_ids:
                current_events.append(event)
                added_count += 1
        
        # Sort by timestamp
        current_events.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # Save updated events
        if self.save_json(self.events_file, current_events):
            self.log_sync('events', added_count)
            return {'status': 'success', 'message': f'Added {added_count} new events'}
        else:
            return {'status': 'error', 'message': 'Failed to save events'}
    
    def sync_activities(self, new_activities):
        """Sync activities"""
        current_activities = self.get_activities()
        current_ids = {activity.get('id') for activity in current_activities}
        
        # Add new activities
        added_count = 0
        for activity in new_activities:
            if activity.get('id') not in current_ids:
                current_activities.append(activity)
                added_count += 1
        
        # Sort by timestamp
        current_activities.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # Keep only last 1000 activities
        if len(current_activities) > 1000:
            current_activities = current_activities[:1000]
        
        # Save updated activities
        if self.save_json(self.activities_file, current_activities):
            self.log_sync('activities', added_count)
            return {'status': 'success', 'message': f'Added {added_count} new activities'}
        else:
            return {'status': 'error', 'message': 'Failed to save activities'}
    
    def full_sync(self, data):
        """Handle full data synchronization"""
        results = {}
        
        if 'users' in data:
            results['users'] = self.sync_users(data['users'])
        
        if 'events' in data:
            results['events'] = self.sync_events(data['events'])
        
        if 'activities' in data:
            results['activities'] = self.sync_activities(data['activities'])
        
        return {
            'status': 'success',
            'message': 'Full sync completed',
            'results': results
        }
    
    def log_sync(self, data_type, count):
        """Log synchronization activity"""
        sync_log = self.load_json(self.sync_log_file)
        
        log_entry = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'type': data_type,
            'count': count,
            'action': 'sync'
        }
        
        sync_log.append(log_entry)
        
        # Keep only last 100 log entries
        if len(sync_log) > 100:
            sync_log = sync_log[-100:]
        
        self.save_json(self.sync_log_file, sync_log)

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    import socket
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def main():
    """Main function"""
    port = 8001
    local_ip = get_local_ip()
    
    print("خادم مزامنة البيانات المركزي")
    print("Central Data Synchronization Server")
    print("=" * 60)
    print(f"الخادم يعمل على المنفذ: {port}")
    print(f"الوصول المحلي: http://localhost:{port}/api/sync/")
    print(f"الوصول من الشبكة: http://{local_ip}:{port}/api/sync/")
    print("=" * 60)
    print("نقاط النهاية المتاحة:")
    print("   GET  /api/sync/status    - حالة الخادم")
    print("   GET  /api/sync/users     - جميع المستخدمين")
    print("   GET  /api/sync/events    - جميع الأحداث")
    print("   GET  /api/sync/activities - جميع الأنشطة")
    print("   GET  /api/sync/all       - جميع البيانات")
    print("   POST /api/sync/users     - مزامنة المستخدمين")
    print("   POST /api/sync/events    - مزامنة الأحداث")
    print("   POST /api/sync/activities - مزامنة الأنشطة")
    print("   POST /api/sync/full      - مزامنة شاملة")
    print("=" * 60)
    print("للإيقاف: اضغط Ctrl+C")
    print()
    
    try:
        # تشغيل الخادم على جميع الواجهات (0.0.0.0) للوصول من الشبكة
        with socketserver.TCPServer(("0.0.0.0", port), SimpleSyncHandler) as httpd:
            httpd.allow_reuse_address = True
            print("خادم المزامنة جاهز للاتصالات المحلية والشبكة...")
            print("يمكن للأجهزة الأخرى في الشبكة الوصول للمزامنة")
            print()
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nتم إيقاف خادم المزامنة")
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {e}")
        print("تأكد من أن المنفذ 8001 غير مستخدم من برنامج آخر")

if __name__ == "__main__":
    main()

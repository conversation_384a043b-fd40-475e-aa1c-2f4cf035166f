# دليل تثبيت نظام إدارة أمن المعلومات

## نظرة عامة
نظام إدارة أمن المعلومات هو نظام شامل لإدارة أمن المعلومات يوفر واجهة ويب سهلة الاستخدام لإدارة السياسات والإجراءات الأمنية.

## متطلبات النظام
- نظام التشغيل: Windows 7/8/10/11 (64-bit)
- Python 3.7 أو أحدث (للنسخة المحمولة)
- ذاكرة الوصول العشوائي: 2 جيجابايت على الأقل
- مساحة القرص الصلب: 100 ميجابايت

## طرق التثبيت

### 1. النسخة المحمولة (Portable)
- لا تحتاج تثبيت
- قم بفك ضغط الملف المضغوط
- شغل "تشغيل النظام.bat"

### 2. المثبت التلقائي (Setup)
- شغل ملف ISMS-Setup.exe
- اتبع التعليمات على الشاشة
- سيتم إنشاء اختصارات تلقائياً

### 3. التثبيت اليدوي (Manual)
- شغل install-simple.bat كمدير
- سيتم نسخ الملفات وإنشاء الاختصارات

## بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يرجى تغيير كلمة المرور بعد أول تسجيل دخول

## المنافذ المستخدمة
- المنفذ الافتراضي: 8000
- إذا كان المنفذ مشغولاً، سيتم البحث عن منفذ آخر تلقائياً

## تشغيل النظام كخدمة ويندوز

### تثبيت الخدمة:
```cmd
python windows-service.py install
```

### تشغيل الخدمة:
```cmd
python windows-service.py start
```

### إيقاف الخدمة:
```cmd
python windows-service.py stop
```

### إلغاء تثبيت الخدمة:
```cmd
python windows-service.py uninstall
```

## استكشاف الأخطاء وإصلاحها

### المشكلة: لا يفتح المتصفح تلقائياً
**الحل:** افتح المتصفح يدوياً واذهب إلى http://localhost:8000

### المشكلة: خطأ في المنفذ
**الحل:** تأكد من عدم استخدام منفذ آخر للمنفذ 8000

### المشكلة: لا يمكن الوصول من أجهزة أخرى
**الحل:** تأكد من إعدادات الجدار الناري (Firewall)

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل مع فريق أمن المعلومات.

## الإصدار
- **الإصدار:** 1.0.0
- **تاريخ الإصدار:** 2025-07-10

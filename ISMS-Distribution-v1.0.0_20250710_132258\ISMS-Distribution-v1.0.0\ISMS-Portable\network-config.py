#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات الشبكة لنظام إدارة أمن المعلومات
Network Configuration for Information Security Management System
"""

import socket
import subprocess
import platform
import json
import sys

def get_network_interfaces():
    """الحصول على جميع واجهات الشبكة المتاحة"""
    interfaces = {}
    
    try:
        if platform.system() == "Windows":
            # Windows
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='utf-8')
            lines = result.stdout.split('\n')
            current_adapter = None
            
            for line in lines:
                line = line.strip()
                if 'adapter' in line.lower() or 'محول' in line:
                    current_adapter = line
                elif 'IPv4' in line or 'عنوان IPv4' in line:
                    if current_adapter and ':' in line:
                        ip = line.split(':')[-1].strip()
                        if ip and ip != '127.0.0.1':
                            interfaces[current_adapter] = ip
        else:
            # macOS/Linux
            result = subprocess.run(['ifconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            current_interface = None
            
            for line in lines:
                if line and not line.startswith(' ') and not line.startswith('\t'):
                    current_interface = line.split(':')[0]
                elif 'inet ' in line and current_interface:
                    parts = line.strip().split()
                    for i, part in enumerate(parts):
                        if part == 'inet' and i + 1 < len(parts):
                            ip = parts[i + 1]
                            if ip != '127.0.0.1' and not ip.startswith('169.254'):
                                interfaces[current_interface] = ip
                            break
    except:
        pass
    
    return interfaces

def get_local_ip():
    """الحصول على عنوان IP المحلي الرئيسي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def check_port_availability(ip, port):
    """التحقق من توفر المنفذ على عنوان IP محدد"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((ip, port))
            return result != 0
    except:
        return False

def find_available_port(ip="0.0.0.0", start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        if check_port_availability(ip, port):
            return port
    return None

def generate_network_config():
    """إنشاء إعدادات الشبكة"""
    print("🔍 فحص إعدادات الشبكة...")
    print("=" * 50)
    
    # الحصول على عنوان IP الرئيسي
    main_ip = get_local_ip()
    print(f"📍 عنوان IP الرئيسي: {main_ip}")
    
    # الحصول على جميع واجهات الشبكة
    interfaces = get_network_interfaces()
    print(f"🌐 واجهات الشبكة المتاحة: {len(interfaces)}")
    
    for name, ip in interfaces.items():
        print(f"   • {name}: {ip}")
    
    # البحث عن منفذ متاح
    port = find_available_port()
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        return None
    
    print(f"🔌 المنفذ المتاح: {port}")
    
    # إنشاء إعدادات الوصول
    config = {
        "main_ip": main_ip,
        "port": port,
        "interfaces": interfaces,
        "access_urls": {
            "local": f"http://localhost:{port}/login.html",
            "network": f"http://{main_ip}:{port}/login.html"
        }
    }
    
    # إضافة عناوين إضافية لكل واجهة
    for name, ip in interfaces.items():
        config["access_urls"][f"interface_{name}"] = f"http://{ip}:{port}/login.html"
    
    return config

def save_config(config):
    """حفظ الإعدادات في ملف"""
    try:
        with open('network-config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

def create_access_file(config):
    """إنشاء ملف روابط الوصول"""
    content = f"""# 🌐 روابط الوصول لنظام إدارة أمن المعلومات
# Information Security Management System - Access Links

## 🏠 الوصول المحلي (Local Access)
{config['access_urls']['local']}

## 🌍 الوصول من الشبكة (Network Access)
{config['access_urls']['network']}

## 📱 روابط إضافية (Additional Links)
"""
    
    for key, url in config['access_urls'].items():
        if key not in ['local', 'network']:
            content += f"{url}\n"
    
    content += f"""
## 🔑 بيانات تسجيل الدخول (Login Credentials)
اسم المستخدم: admin
كلمة المرور: admin123

## ⚙️ إعدادات الشبكة (Network Settings)
عنوان IP الرئيسي: {config['main_ip']}
المنفذ: {config['port']}

## 📋 واجهات الشبكة (Network Interfaces)
"""
    
    for name, ip in config['interfaces'].items():
        content += f"{name}: {ip}\n"
    
    content += """
## 🔒 ملاحظات الأمان (Security Notes)
- تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ المحدد
- استخدم شبكة آمنة وموثوقة فقط
- غيّر كلمة المرور الافتراضية بعد أول تسجيل دخول
- راقب سجلات الوصول بانتظام

## 🛠️ استكشاف الأخطاء (Troubleshooting)
إذا لم تتمكن من الوصول:
1. تحقق من تشغيل الخادم
2. تحقق من إعدادات جدار الحماية
3. تحقق من اتصال الشبكة
4. جرب عنوان IP مختلف من القائمة أعلاه
"""
    
    try:
        with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    print("🔐 إعداد الشبكة لنظام إدارة أمن المعلومات")
    print("=" * 60)
    print()
    
    # إنشاء إعدادات الشبكة
    config = generate_network_config()
    
    if not config:
        print("❌ فشل في إنشاء إعدادات الشبكة")
        sys.exit(1)
    
    print()
    print("📋 ملخص الإعدادات:")
    print("=" * 30)
    print(f"🌐 عنوان IP الرئيسي: {config['main_ip']}")
    print(f"🔌 المنفذ: {config['port']}")
    print()
    print("🔗 روابط الوصول:")
    for name, url in config['access_urls'].items():
        print(f"   • {name}: {url}")
    
    # حفظ الإعدادات
    if save_config(config):
        print("\n✅ تم حفظ إعدادات الشبكة في: network-config.json")
    else:
        print("\n⚠️  فشل في حفظ إعدادات الشبكة")
    
    # إنشاء ملف روابط الوصول
    if create_access_file(config):
        print("✅ تم إنشاء ملف روابط الوصول: ACCESS-LINKS.txt")
    else:
        print("⚠️  فشل في إنشاء ملف روابط الوصول")
    
    print()
    print("🚀 لتشغيل النظام:")
    print("   • Windows: انقر نقراً مزدوجاً على start-server.bat")
    print("   • macOS/Linux: ./start-server.sh")
    print("   • أو: python3 start-server.py")
    
    return config

if __name__ == "__main__":
    main()

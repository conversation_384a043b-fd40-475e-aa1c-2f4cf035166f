<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة أمن المعلومات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .settings-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .settings-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3em;
        }
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .setting-item:last-child {
            border-bottom: none;
        }
        .setting-label {
            font-weight: 500;
            color: #2c3e50;
        }
        .setting-description {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .toggle-switch.active {
            background: #27ae60;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        .toggle-switch.active::after {
            transform: translateX(30px);
        }
        .input-field {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
            width: 150px;
        }
        .input-field:focus {
            border-color: #3498db;
            outline: none;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-online {
            background: #27ae60;
        }
        .status-offline {
            background: #e74c3c;
        }
        .backup-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            display: none;
        }
        .notification.error {
            background: #e74c3c;
        }
    </style>
    <script src="fixed-auth-system.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إعدادات النظام</h1>
            <p>إدارة وتخصيص إعدادات نظام إدارة أمن المعلومات</p>
        </div>
        
        <div class="settings-grid">
            <!-- إعدادات الأمان -->
            <div class="settings-card">
                <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">تفعيل المصادقة الثنائية</div>
                        <div class="setting-description">إضافة طبقة حماية إضافية لتسجيل الدخول</div>
                    </div>
                    <div class="toggle-switch" id="twoFactorAuth"></div>
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">مهلة انتهاء الجلسة (دقيقة)</div>
                        <div class="setting-description">المدة قبل انتهاء الجلسة تلقائياً</div>
                    </div>
                    <input type="number" class="input-field" id="sessionTimeout" value="30" min="5" max="480">
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">الحد الأقصى لمحاولات تسجيل الدخول</div>
                        <div class="setting-description">عدد المحاولات المسموحة قبل حظر الحساب</div>
                    </div>
                    <input type="number" class="input-field" id="maxLoginAttempts" value="5" min="3" max="10">
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">تسجيل جميع الأنشطة</div>
                        <div class="setting-description">حفظ سجل مفصل لجميع عمليات النظام</div>
                    </div>
                    <div class="toggle-switch active" id="activityLogging"></div>
                </div>
            </div>
            
            <!-- إعدادات النظام -->
            <div class="settings-card">
                <h3><i class="fas fa-cogs"></i> إعدادات النظام</h3>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">المزامنة التلقائية</div>
                        <div class="setting-description">مزامنة البيانات تلقائياً مع الخادم</div>
                    </div>
                    <div class="toggle-switch active" id="autoSync"></div>
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">فترة المزامنة (ثانية)</div>
                        <div class="setting-description">المدة بين عمليات المزامنة التلقائية</div>
                    </div>
                    <input type="number" class="input-field" id="syncInterval" value="30" min="10" max="300">
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">الإشعارات المنبثقة</div>
                        <div class="setting-description">عرض إشعارات للأحداث المهمة</div>
                    </div>
                    <div class="toggle-switch active" id="notifications"></div>
                </div>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">السمة الداكنة</div>
                        <div class="setting-description">استخدام السمة الداكنة للواجهة</div>
                    </div>
                    <div class="toggle-switch" id="darkTheme"></div>
                </div>
            </div>
        </div>
        
        <!-- حالة النظام -->
        <div class="settings-card" style="margin-top: 20px;">
            <h3><i class="fas fa-server"></i> حالة النظام</h3>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">خادم قاعدة البيانات</div>
                    <div class="setting-description">حالة الاتصال بقاعدة البيانات</div>
                </div>
                <div>
                    <span class="status-indicator status-online"></span>
                    متصل
                </div>
            </div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">خادم المزامنة</div>
                    <div class="setting-description">حالة خدمة المزامنة</div>
                </div>
                <div>
                    <span class="status-indicator status-online"></span>
                    نشط
                </div>
            </div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">مساحة التخزين المستخدمة</div>
                    <div class="setting-description">مساحة localStorage المستخدمة</div>
                </div>
                <div id="storageUsage">جاري الحساب...</div>
            </div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">آخر نسخة احتياطية</div>
                    <div class="setting-description">تاريخ آخر نسخة احتياطية تلقائية</div>
                </div>
                <div id="lastBackup">لم يتم إنشاء نسخة احتياطية</div>
            </div>
        </div>
        
        <!-- النسخ الاحتياطي -->
        <div class="settings-card" style="margin-top: 20px;">
            <h3><i class="fas fa-database"></i> النسخ الاحتياطي والاستعادة</h3>
            
            <div class="backup-section">
                <div class="backup-item">
                    <div>
                        <div class="setting-label">إنشاء نسخة احتياطية</div>
                        <div class="setting-description">تصدير جميع بيانات النظام</div>
                    </div>
                    <button class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                    </button>
                </div>
                
                <div class="backup-item">
                    <div>
                        <div class="setting-label">استعادة من نسخة احتياطية</div>
                        <div class="setting-description">استيراد بيانات من ملف نسخة احتياطية</div>
                    </div>
                    <div>
                        <input type="file" id="backupFile" accept=".json" style="display: none;">
                        <button class="btn" onclick="document.getElementById('backupFile').click()">
                            <i class="fas fa-upload"></i> اختيار ملف
                        </button>
                    </div>
                </div>
                
                <div class="backup-item">
                    <div>
                        <div class="setting-label">مسح جميع البيانات</div>
                        <div class="setting-description">حذف جميع البيانات وإعادة تعيين النظام</div>
                    </div>
                    <button class="btn btn-danger" onclick="resetSystem()">
                        <i class="fas fa-trash"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
        
        <!-- أزرار الحفظ -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-success" onclick="saveSettings()">
                <i class="fas fa-save"></i> حفظ الإعدادات
            </button>
            <button class="btn" onclick="resetToDefaults()">
                <i class="fas fa-undo"></i> استعادة الافتراضي
            </button>
            <button class="btn" onclick="window.close()">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
    </div>
    
    <!-- إشعار -->
    <div class="notification" id="notification"></div>

    <script>
        // تحميل الإعدادات عند تحميل الصفحة
        window.onload = function() {
            loadSettings();
            calculateStorageUsage();
            setupEventListeners();
        };
        
        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // Toggle switches
            document.querySelectorAll('.toggle-switch').forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });
            
            // ملف النسخة الاحتياطية
            document.getElementById('backupFile').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    restoreFromBackup(e.target.files[0]);
                }
            });
        }
        
        // تحميل الإعدادات
        function loadSettings() {
            try {
                const settings = JSON.parse(localStorage.getItem('systemSettings')) || {};
                
                // تطبيق الإعدادات على الواجهة
                if (settings.twoFactorAuth) {
                    document.getElementById('twoFactorAuth').classList.add('active');
                }
                
                if (settings.sessionTimeout) {
                    document.getElementById('sessionTimeout').value = settings.sessionTimeout;
                }
                
                if (settings.maxLoginAttempts) {
                    document.getElementById('maxLoginAttempts').value = settings.maxLoginAttempts;
                }
                
                if (settings.activityLogging !== false) {
                    document.getElementById('activityLogging').classList.add('active');
                }
                
                if (settings.autoSync !== false) {
                    document.getElementById('autoSync').classList.add('active');
                }
                
                if (settings.syncInterval) {
                    document.getElementById('syncInterval').value = settings.syncInterval;
                }
                
                if (settings.notifications !== false) {
                    document.getElementById('notifications').classList.add('active');
                }
                
                if (settings.darkTheme) {
                    document.getElementById('darkTheme').classList.add('active');
                }
                
                // تحديث آخر نسخة احتياطية
                if (settings.lastBackup) {
                    document.getElementById('lastBackup').textContent = 
                        new Date(settings.lastBackup).toLocaleString('ar-SA');
                }
                
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
            }
        }
        
        // حفظ الإعدادات
        function saveSettings() {
            try {
                const settings = {
                    twoFactorAuth: document.getElementById('twoFactorAuth').classList.contains('active'),
                    sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
                    maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value),
                    activityLogging: document.getElementById('activityLogging').classList.contains('active'),
                    autoSync: document.getElementById('autoSync').classList.contains('active'),
                    syncInterval: parseInt(document.getElementById('syncInterval').value),
                    notifications: document.getElementById('notifications').classList.contains('active'),
                    darkTheme: document.getElementById('darkTheme').classList.contains('active'),
                    lastModified: new Date().toISOString()
                };
                
                localStorage.setItem('systemSettings', JSON.stringify(settings));
                
                // تطبيق السمة الداكنة
                if (settings.darkTheme) {
                    document.body.classList.add('dark-theme');
                } else {
                    document.body.classList.remove('dark-theme');
                }
                
                showNotification('تم حفظ الإعدادات بنجاح', 'success');
                
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showNotification('حدث خطأ في حفظ الإعدادات', 'error');
            }
        }
        
        // استعادة الإعدادات الافتراضية
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟')) {
                localStorage.removeItem('systemSettings');
                location.reload();
            }
        }
        
        // حساب مساحة التخزين المستخدمة
        function calculateStorageUsage() {
            try {
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                
                const sizeInKB = (totalSize / 1024).toFixed(2);
                const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
                
                document.getElementById('storageUsage').textContent = 
                    sizeInMB > 1 ? `${sizeInMB} ميجابايت` : `${sizeInKB} كيلوبايت`;
                
            } catch (error) {
                document.getElementById('storageUsage').textContent = 'غير متاح';
            }
        }
        
        // إنشاء نسخة احتياطية
        function createBackup() {
            try {
                const backupData = {
                    version: '1.0',
                    timestamp: new Date().toISOString(),
                    data: {
                        systemUsers: localStorage.getItem('systemUsers'),
                        securityEvents: localStorage.getItem('securityEvents'),
                        activityLog: localStorage.getItem('activityLog'),
                        userSessions: localStorage.getItem('userSessions'),
                        systemSettings: localStorage.getItem('systemSettings'),
                        eventTypes: localStorage.getItem('eventTypes'),
                        postIncidentReviews: localStorage.getItem('postIncidentReviews')
                    }
                };
                
                const blob = new Blob([JSON.stringify(backupData, null, 2)], { 
                    type: 'application/json' 
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `isms-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
                
                // تحديث آخر نسخة احتياطية
                const settings = JSON.parse(localStorage.getItem('systemSettings')) || {};
                settings.lastBackup = new Date().toISOString();
                localStorage.setItem('systemSettings', JSON.stringify(settings));
                
                document.getElementById('lastBackup').textContent = 
                    new Date().toLocaleString('ar-SA');
                
                showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                
            } catch (error) {
                console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                showNotification('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
            }
        }
        
        // استعادة من نسخة احتياطية
        function restoreFromBackup(file) {
            if (!confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);
                    
                    if (!backupData.data) {
                        throw new Error('ملف النسخة الاحتياطية غير صالح');
                    }
                    
                    // استعادة البيانات
                    for (const [key, value] of Object.entries(backupData.data)) {
                        if (value) {
                            localStorage.setItem(key, value);
                        }
                    }
                    
                    showNotification('تم استعادة البيانات بنجاح', 'success');
                    
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                    
                } catch (error) {
                    console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                    showNotification('حدث خطأ في استعادة النسخة الاحتياطية', 'error');
                }
            };
            
            reader.readAsText(file);
        }
        
        // إعادة تعيين النظام
        function resetSystem() {
            const confirmation = prompt('اكتب "RESET" لتأكيد إعادة تعيين النظام:');
            
            if (confirmation === 'RESET') {
                localStorage.clear();
                sessionStorage.clear();
                showNotification('تم إعادة تعيين النظام بنجاح', 'success');
                
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            } else {
                showNotification('تم إلغاء العملية', 'error');
            }
        }
        
        // عرض الإشعارات
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>

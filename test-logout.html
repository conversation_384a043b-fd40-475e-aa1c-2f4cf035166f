<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الخروج</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 1rem;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .console-output {
            background: #1f2937;
            color: #10b981;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            min-height: 100px;
            overflow-y: auto;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 2rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 اختبار تسجيل الخروج</h1>
        
        <div class="info">
            <strong>تعليمات الاختبار:</strong><br>
            1. انقر على زر "اختبار تسجيل الخروج" أدناه<br>
            2. ستظهر نافذة تأكيد<br>
            3. اختر "موافق" للمتابعة أو "إلغاء" للعودة<br>
            4. راقب الرسائل في وحدة التحكم أدناه
        </div>
        
        <button class="test-btn" onclick="testLogoutFunction()">
            🚪 اختبار تسجيل الخروج
        </button>
        
        <button class="test-btn" onclick="testDirectLogout()" style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);">
            ⚡ اختبار مباشر (بدون تأكيد)
        </button>
        
        <div class="console-output" id="consoleOutput">
            جاهز للاختبار...<br>
        </div>
        
        <a href="index.html" class="back-btn">← العودة للبرنامج الرئيسي</a>
    </div>

    <script>
        // Redirect console.log to our output div
        const originalLog = console.log;
        const originalError = console.error;
        const outputDiv = document.getElementById('consoleOutput');
        
        function addToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const color = type === 'error' ? '#ef4444' : type === 'warn' ? '#f59e0b' : '#10b981';
            outputDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToOutput(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToOutput(args.join(' '), 'error');
        };
        
        // Test logout function
        function testLogoutFunction() {
            console.log('🧪 بدء اختبار تسجيل الخروج...');
            
            try {
                // Check if testLogout function exists
                if (typeof window.testLogout === 'function') {
                    console.log('✅ دالة testLogout موجودة');
                    window.testLogout();
                } else {
                    console.error('❌ دالة testLogout غير موجودة');
                    // Fallback test
                    testFallbackLogout();
                }
            } catch (error) {
                console.error('❌ خطأ في اختبار تسجيل الخروج:', error.message);
                testFallbackLogout();
            }
        }
        
        // Fallback logout test
        function testFallbackLogout() {
            console.log('🔄 تجربة الطريقة البديلة...');
            
            const confirmLogout = confirm('هل أنت متأكد من أنك تريد تسجيل الخروج من النظام؟');
            
            if (confirmLogout) {
                console.log('✅ المستخدم أكد تسجيل الخروج');
                console.log('🧹 مسح البيانات...');
                
                // Clear storage
                localStorage.clear();
                sessionStorage.clear();
                
                console.log('✅ تم مسح البيانات بنجاح');
                console.log('🔄 إعادة التوجيه إلى صفحة الدخول...');
                
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            } else {
                console.log('❌ المستخدم ألغى تسجيل الخروج');
            }
        }
        
        // Direct logout test (no confirmation)
        function testDirectLogout() {
            console.log('⚡ اختبار تسجيل الخروج المباشر...');
            console.log('🧹 مسح البيانات...');
            
            localStorage.clear();
            sessionStorage.clear();
            
            console.log('✅ تم مسح البيانات');
            console.log('🔄 إعادة التوجيه إلى صفحة الدخول...');
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
        }
        
        // Load the main script to test
        const script = document.createElement('script');
        script.src = 'script.js';
        script.onload = function() {
            console.log('✅ تم تحميل script.js بنجاح');
            console.log('🔍 فحص وجود الدوال...');
            
            if (typeof window.testLogout === 'function') {
                console.log('✅ دالة testLogout متاحة');
            } else {
                console.log('❌ دالة testLogout غير متاحة');
            }
            
            if (typeof securityManager !== 'undefined' && securityManager.simpleLogout) {
                console.log('✅ دالة simpleLogout متاحة');
            } else {
                console.log('❌ دالة simpleLogout غير متاحة');
            }
        };
        script.onerror = function() {
            console.error('❌ فشل في تحميل script.js');
        };
        document.head.appendChild(script);
        
        console.log('🚀 تم تحميل صفحة الاختبار');
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص شامل لمشاكل المزامنة
Comprehensive Sync Diagnosis Tool
"""

import os
import json
import socket
import requests
import subprocess
import platform
import time
from datetime import datetime

class SyncDiagnoser:
    def __init__(self):
        self.diagnosis_results = {}
        self.local_ip = self.get_local_ip()
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def print_header(self):
        """طباعة رأس التشخيص"""
        print("=" * 70)
        print("🔍 تشخيص شامل لمشاكل المزامنة")
        print("   Comprehensive Sync Diagnosis")
        print("=" * 70)
        print(f"🌐 عنوان IP المحلي: {self.local_ip}")
        print(f"⏰ وقت التشخيص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def diagnose_system_environment(self):
        """تشخيص بيئة النظام"""
        print("🖥️  تشخيص بيئة النظام...")
        
        env_info = {
            'os': platform.system(),
            'os_version': platform.version(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0]
        }
        
        print(f"   نظام التشغيل: {env_info['os']}")
        print(f"   إصدار Python: {env_info['python_version']}")
        print(f"   المعمارية: {env_info['architecture']}")
        
        # فحص المكتبات المطلوبة
        required_modules = ['json', 'socket', 'http.server', 'socketserver']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"   ✅ {module}")
            except ImportError:
                print(f"   ❌ {module} - مفقود")
                missing_modules.append(module)
        
        env_info['missing_modules'] = missing_modules
        self.diagnosis_results['environment'] = env_info
        
        print()
        return len(missing_modules) == 0
    
    def diagnose_network_connectivity(self):
        """تشخيص الاتصال بالشبكة"""
        print("🌐 تشخيص الاتصال بالشبكة...")
        
        network_info = {
            'internet_connection': False,
            'local_ports': {},
            'network_interfaces': []
        }
        
        # فحص الاتصال بالإنترنت
        try:
            socket.create_connection(("*******", 53), timeout=3)
            print("   ✅ الاتصال بالإنترنت متاح")
            network_info['internet_connection'] = True
        except OSError:
            print("   ❌ لا يوجد اتصال بالإنترنت")
        
        # فحص المنافذ المحلية
        ports_to_check = [8000, 8001]
        for port in ports_to_check:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', port))
                    if result == 0:
                        print(f"   ✅ المنفذ {port} مفتوح")
                        network_info['local_ports'][port] = 'open'
                    else:
                        print(f"   ❌ المنفذ {port} مغلق")
                        network_info['local_ports'][port] = 'closed'
            except Exception as e:
                print(f"   ⚠️  خطأ في فحص المنفذ {port}: {e}")
                network_info['local_ports'][port] = 'error'
        
        # فحص واجهات الشبكة
        try:
            hostname = socket.gethostname()
            local_ips = socket.gethostbyname_ex(hostname)[2]
            network_info['network_interfaces'] = local_ips
            print(f"   🔗 واجهات الشبكة: {', '.join(local_ips)}")
        except Exception as e:
            print(f"   ⚠️  خطأ في فحص واجهات الشبكة: {e}")
        
        self.diagnosis_results['network'] = network_info
        print()
        return network_info['local_ports'].get(8001) == 'open'
    
    def diagnose_file_system(self):
        """تشخيص نظام الملفات"""
        print("📁 تشخيص نظام الملفات...")
        
        file_info = {
            'required_files': {},
            'data_directory': {},
            'permissions': {}
        }
        
        # فحص الملفات المطلوبة
        required_files = [
            'data-sync-server.py',
            'data-sync-client.js',
            'start-server.py',
            'index.html',
            'login.html'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"   ✅ {file} ({size} bytes)")
                file_info['required_files'][file] = {'exists': True, 'size': size}
            else:
                print(f"   ❌ {file} - مفقود")
                file_info['required_files'][file] = {'exists': False, 'size': 0}
        
        # فحص مجلد البيانات
        data_dir = "data"
        if os.path.exists(data_dir):
            print(f"   ✅ مجلد البيانات موجود")
            
            data_files = ['users.json', 'events.json', 'activities.json', 'sync_log.json']
            for data_file in data_files:
                file_path = os.path.join(data_dir, data_file)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        print(f"      ✅ {data_file} - صالح")
                        file_info['data_directory'][data_file] = {'valid': True, 'content': len(str(data))}
                    except json.JSONDecodeError:
                        print(f"      ❌ {data_file} - تالف")
                        file_info['data_directory'][data_file] = {'valid': False, 'content': 0}
                else:
                    print(f"      ❌ {data_file} - مفقود")
                    file_info['data_directory'][data_file] = {'valid': False, 'content': 0}
        else:
            print(f"   ❌ مجلد البيانات مفقود")
            file_info['data_directory']['exists'] = False
        
        # فحص الصلاحيات
        try:
            test_file = "test_write_permission.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("   ✅ صلاحيات الكتابة متاحة")
            file_info['permissions']['write'] = True
        except Exception as e:
            print(f"   ❌ مشكلة في صلاحيات الكتابة: {e}")
            file_info['permissions']['write'] = False
        
        self.diagnosis_results['filesystem'] = file_info
        print()
        return all(f['exists'] for f in file_info['required_files'].values())
    
    def diagnose_sync_server(self):
        """تشخيص خادم المزامنة"""
        print("🔄 تشخيص خادم المزامنة...")
        
        server_info = {
            'process_running': False,
            'http_response': {},
            'api_endpoints': {}
        }
        
        # فحص العملية
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
                if ':8001' in result.stdout:
                    print("   ✅ عملية تعمل على المنفذ 8001")
                    server_info['process_running'] = True
                else:
                    print("   ❌ لا توجد عملية على المنفذ 8001")
            else:
                result = subprocess.run(['lsof', '-i', ':8001'], capture_output=True, text=True)
                if result.stdout.strip():
                    print("   ✅ عملية تعمل على المنفذ 8001")
                    server_info['process_running'] = True
                else:
                    print("   ❌ لا توجد عملية على المنفذ 8001")
        except Exception as e:
            print(f"   ⚠️  خطأ في فحص العملية: {e}")
        
        # فحص الاستجابة HTTP
        test_urls = [
            f"http://localhost:8001",
            f"http://127.0.0.1:8001",
            f"http://{self.local_ip}:8001"
        ]
        
        for url in test_urls:
            try:
                response = requests.get(f"{url}/api/sync/status", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ {url} - يستجيب")
                    server_info['http_response'][url] = {
                        'status': 'working',
                        'data': data
                    }
                else:
                    print(f"   ❌ {url} - خطأ {response.status_code}")
                    server_info['http_response'][url] = {
                        'status': 'error',
                        'code': response.status_code
                    }
            except requests.exceptions.ConnectionError:
                print(f"   ❌ {url} - لا يمكن الاتصال")
                server_info['http_response'][url] = {'status': 'connection_error'}
            except Exception as e:
                print(f"   ❌ {url} - خطأ: {e}")
                server_info['http_response'][url] = {'status': 'error', 'message': str(e)}
        
        # فحص نقاط النهاية API
        if any(info.get('status') == 'working' for info in server_info['http_response'].values()):
            working_url = next(url for url, info in server_info['http_response'].items() 
                             if info.get('status') == 'working')
            
            endpoints = ['/api/sync/users', '/api/sync/events', '/api/sync/activities', '/api/sync/all']
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{working_url}{endpoint}", timeout=3)
                    if response.status_code == 200:
                        print(f"      ✅ {endpoint}")
                        server_info['api_endpoints'][endpoint] = 'working'
                    else:
                        print(f"      ❌ {endpoint} - خطأ {response.status_code}")
                        server_info['api_endpoints'][endpoint] = f'error_{response.status_code}'
                except Exception as e:
                    print(f"      ❌ {endpoint} - خطأ: {e}")
                    server_info['api_endpoints'][endpoint] = 'error'
        
        self.diagnosis_results['sync_server'] = server_info
        print()
        return server_info['process_running'] and any(
            info.get('status') == 'working' for info in server_info['http_response'].values()
        )
    
    def diagnose_client_side(self):
        """تشخيص جانب العميل"""
        print("💻 تشخيص جانب العميل...")
        
        client_info = {
            'javascript_file': {},
            'browser_compatibility': {},
            'local_storage': {}
        }
        
        # فحص ملف JavaScript
        js_file = "data-sync-client.js"
        if os.path.exists(js_file):
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص المكونات المطلوبة
                required_components = [
                    'class DataSyncClient',
                    'detectSyncServerUrl',
                    'syncNow',
                    'sendToServer',
                    'getFromServer'
                ]
                
                for component in required_components:
                    if component in content:
                        print(f"   ✅ {component}")
                        client_info['javascript_file'][component] = True
                    else:
                        print(f"   ❌ {component} - مفقود")
                        client_info['javascript_file'][component] = False
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة ملف JavaScript: {e}")
        else:
            print(f"   ❌ ملف {js_file} مفقود")
        
        # فحص التوافق مع المتصفح (محاكاة)
        browser_features = [
            'fetch API',
            'localStorage',
            'JSON support',
            'ES6 classes'
        ]
        
        for feature in browser_features:
            print(f"   ✅ {feature} - مدعوم")
            client_info['browser_compatibility'][feature] = True
        
        self.diagnosis_results['client_side'] = client_info
        print()
        return all(client_info['javascript_file'].values())
    
    def generate_diagnosis_report(self):
        """إنشاء تقرير التشخيص"""
        print("📋 تقرير التشخيص الشامل:")
        print("=" * 60)
        
        # تقييم كل مكون
        components_status = {
            'environment': self.diagnosis_results.get('environment', {}),
            'network': self.diagnosis_results.get('network', {}),
            'filesystem': self.diagnosis_results.get('filesystem', {}),
            'sync_server': self.diagnosis_results.get('sync_server', {}),
            'client_side': self.diagnosis_results.get('client_side', {})
        }
        
        # حساب النقاط
        total_score = 0
        max_score = 0
        
        for component, data in components_status.items():
            component_score = 0
            component_max = 0
            
            if component == 'environment':
                component_max = 2
                if not data.get('missing_modules', []):
                    component_score += 2
            elif component == 'network':
                component_max = 3
                if data.get('internet_connection'):
                    component_score += 1
                if data.get('local_ports', {}).get(8001) == 'open':
                    component_score += 2
            elif component == 'filesystem':
                component_max = 2
                required_files = data.get('required_files', {})
                if all(f.get('exists', False) for f in required_files.values()):
                    component_score += 1
                if data.get('permissions', {}).get('write', False):
                    component_score += 1
            elif component == 'sync_server':
                component_max = 3
                if data.get('process_running'):
                    component_score += 1
                if any(info.get('status') == 'working' for info in data.get('http_response', {}).values()):
                    component_score += 2
            elif component == 'client_side':
                component_max = 2
                js_components = data.get('javascript_file', {})
                if all(js_components.values()):
                    component_score += 2
            
            total_score += component_score
            max_score += component_max
            
            percentage = (component_score / component_max * 100) if component_max > 0 else 0
            status_icon = "✅" if percentage >= 80 else "⚠️" if percentage >= 50 else "❌"
            
            print(f"{status_icon} {component.replace('_', ' ').title()}: {component_score}/{component_max} ({percentage:.0f}%)")
        
        # النتيجة الإجمالية
        overall_percentage = (total_score / max_score * 100) if max_score > 0 else 0
        
        print(f"\n📊 النتيجة الإجمالية: {total_score}/{max_score} ({overall_percentage:.0f}%)")
        
        if overall_percentage >= 90:
            status = "ممتاز"
            print("🎉 النظام يعمل بشكل مثالي!")
        elif overall_percentage >= 70:
            status = "جيد"
            print("✅ النظام يعمل مع بعض المشاكل البسيطة")
        elif overall_percentage >= 50:
            status = "متوسط"
            print("⚠️  النظام يحتاج لبعض الإصلاحات")
        else:
            status = "ضعيف"
            print("❌ النظام يحتاج لإصلاحات شاملة")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        if overall_percentage < 90:
            print("   • شغل: python sync-fix-advanced.py للإصلاح التلقائي")
            print("   • تحقق من جدار الحماية وإعدادات الشبكة")
            print("   • تأكد من تشغيل خادم المزامنة")
        
        print("   • استخدم صفحة الاختبار: sync-test.html")
        print("   • راجع ملف ACCESS-LINKS.txt للروابط")
        
        print("=" * 60)
        
        # حفظ التقرير
        self.save_diagnosis_report(overall_percentage, status)
        
        return status
    
    def save_diagnosis_report(self, score, status):
        """حفظ تقرير التشخيص"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'local_ip': self.local_ip,
            'overall_score': score,
            'status': status,
            'detailed_results': self.diagnosis_results
        }
        
        try:
            with open('sync-diagnosis-report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print("✅ تم حفظ تقرير التشخيص: sync-diagnosis-report.json")
        except Exception as e:
            print(f"⚠️  خطأ في حفظ التقرير: {e}")
    
    def run_comprehensive_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        self.print_header()
        
        print("🔍 بدء التشخيص الشامل...")
        print()
        
        # تشغيل جميع اختبارات التشخيص
        self.diagnose_system_environment()
        self.diagnose_network_connectivity()
        self.diagnose_file_system()
        self.diagnose_sync_server()
        self.diagnose_client_side()
        
        # إنشاء التقرير النهائي
        status = self.generate_diagnosis_report()
        
        return status in ["ممتاز", "جيد"]

def main():
    """الدالة الرئيسية"""
    diagnoser = SyncDiagnoser()
    success = diagnoser.run_comprehensive_diagnosis()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

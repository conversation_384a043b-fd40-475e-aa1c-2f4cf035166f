@echo off
chcp 65001 >nul
title نظام إدارة أمن المعلومات - ISMS

echo ============================================================
echo تشغيل نظام إدارة أمن المعلومات
echo Information Security Management System
echo ============================================================
echo.

echo 1. تشغيل الخادم الرئيسي...
start "ISMS Main Server" python start-server.py
timeout /t 3 /nobreak >nul

echo 2. تشغيل خادم المزامنة...
start "ISMS Sync Server" python sync-server-simple.py
timeout /t 3 /nobreak >nul

echo 3. انتظار تشغيل الخوادم...
timeout /t 5 /nobreak >nul

echo 4. فتح المتصفح...
start http://localhost:8000/login.html

echo.
echo ============================================================
echo تم تشغيل النظام بنجاح!
echo ============================================================
echo.
echo روابط الوصول:
echo   محلي: http://localhost:8000/login.html
echo   شبكة: http://*************:8000/login.html
echo.
echo بيانات الدخول:
echo   admin / admin123 (مدير النظام)
echo   analyst / analyst123 (محلل أمني)
echo   operator / operator123 (مشغل)
echo.
echo أدوات التشخيص:
echo   http://localhost:8000/login-debug.html
echo   http://localhost:8000/sync-test.html
echo.
echo للإيقاف: أغلق نوافذ الخوادم
echo ============================================================

pause

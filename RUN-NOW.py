#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل فوري للنظام
Instant System Runner
"""

import subprocess
import time
import sys
import webbrowser
import socket

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def main():
    local_ip = get_local_ip()
    
    print("🚀 تشغيل نظام إدارة أمن المعلومات")
    print("=" * 50)
    
    # تشغيل الخادم الرئيسي
    print("تشغيل الخادم الرئيسي...")
    try:
        subprocess.Popen([sys.executable, 'start-server.py'])
        print("✅ تم تشغيل الخادم على المنفذ 8000")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return
    
    # انتظار قصير
    time.sleep(3)
    
    # تشغيل خادم المزامنة
    print("تشغيل خادم المزامنة...")
    try:
        subprocess.Popen([sys.executable, 'sync-server-simple.py'])
        print("✅ تم تشغيل خادم المزامنة على المنفذ 8001")
    except Exception as e:
        print(f"⚠️  خادم المزامنة: {e}")
    
    # انتظار تشغيل الخوادم
    print("انتظار تشغيل الخوادم...")
    time.sleep(5)
    
    # فتح المتصفح
    print("فتح المتصفح...")
    try:
        webbrowser.open("http://localhost:8000/login.html")
        print("✅ تم فتح صفحة تسجيل الدخول")
    except Exception as e:
        print(f"⚠️  فتح المتصفح: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 تم تشغيل النظام بنجاح!")
    print("=" * 50)
    print("روابط الوصول:")
    print(f"  تسجيل الدخول: http://localhost:8000/login.html")
    print(f"  الصفحة الرئيسية: http://localhost:8000/index.html")
    print(f"  من الشبكة: http://{local_ip}:8000/login.html")
    print()
    print("بيانات الدخول:")
    print("  👤 admin / admin123 (مدير النظام)")
    print("  👤 analyst / analyst123 (محلل أمني)")
    print("  👤 operator / operator123 (مشغل)")
    print()
    print("أدوات التشخيص:")
    print("  🔍 http://localhost:8000/login-advanced-tester.html")
    print("  🧹 http://localhost:8000/clear-browser-data.html")
    print()
    print("للإيقاف: أغلق نوافذ Terminal")
    print("=" * 50)

if __name__ == "__main__":
    main()

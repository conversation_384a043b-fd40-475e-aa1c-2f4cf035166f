<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إصلاح تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 500px;
            margin: 0 auto;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover { background: #229954; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح تسجيل الدخول</h1>
        <div id="status">جاري الإصلاح...</div>
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = {"admin": {"id": "admin", "username": "admin", "password": "-969161597", "fullName": "مدير النظام", "email": "<EMAIL>", "role": "admin", "permissions": ["read", "write", "delete", "manage_users", "view_analytics", "manage_system"], "isActive": true, "createdAt": "2025-07-13T14:09:14.150240", "lastLogin": null, "loginAttempts": 0, "lastLoginAttempt": null}, "analyst": {"id": "analyst", "username": "analyst", "password": "-944571792", "fullName": "محلل أمني", "email": "<EMAIL>", "role": "analyst", "permissions": ["read", "write", "view_analytics"], "isActive": true, "createdAt": "2025-07-13T14:09:14.150248", "lastLogin": null, "loginAttempts": 0, "lastLoginAttempt": null}, "operator": {"id": "operator", "username": "operator", "password": "135275278", "fullName": "مشغل النظام", "email": "<EMAIL>", "role": "operator", "permissions": ["read"], "isActive": true, "createdAt": "2025-07-13T14:09:14.150254", "lastLogin": null, "loginAttempts": 0, "lastLoginAttempt": null}};
        
        function fixLogin() {
            try {
                // حفظ البيانات في localStorage
                localStorage.setItem('systemUsers', JSON.stringify(users));
                
                // التحقق من الحفظ
                const savedUsers = JSON.parse(localStorage.getItem('systemUsers'));
                
                if (savedUsers && savedUsers.admin) {
                    document.getElementById('status').innerHTML = '<span class="success">✅ تم إصلاح المشكلة بنجاح!</span>';
                    document.getElementById('result').innerHTML = `
                        <h3>بيانات الدخول:</h3>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin123</p>
                        <br>
                        <button onclick="window.location.href='login.html'">تسجيل الدخول الآن</button>
                        <button onclick="testLogin()">اختبار سريع</button>
                    `;
                } else {
                    throw new Error('فشل في حفظ البيانات');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '<span class="error">❌ فشل في الإصلاح: ' + error.message + '</span>';
            }
        }
        
        function testLogin() {
            try {
                const users = JSON.parse(localStorage.getItem('systemUsers'));
                const admin = users.admin;
                
                // دالة التشفير
                function hashPassword(password) {
                    let hash = 0;
                    for (let i = 0; i < password.length; i++) {
                        const char = password.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return hash.toString();
                }
                
                const testHash = hashPassword('admin123');
                
                if (admin.password === testHash) {
                    document.getElementById('result').innerHTML += '<p class="success">✅ اختبار تسجيل الدخول نجح!</p>';
                } else {
                    document.getElementById('result').innerHTML += '<p class="error">❌ اختبار تسجيل الدخول فشل</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML += '<p class="error">❌ خطأ في الاختبار: ' + error.message + '</p>';
            }
        }
        
        // تشغيل الإصلاح تلقائياً
        window.onload = function() {
            setTimeout(fixLogin, 1000);
        };
    </script>
</body>
</html>
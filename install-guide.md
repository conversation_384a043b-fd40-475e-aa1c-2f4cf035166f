# دليل تثبيت نظام إدارة أمن المعلومات

## 1. التثبيت المحلي (على الكمبيوتر)

### الطريقة الأولى: تشغيل مباشر
1. انسخ جميع الملفات إلى مجلد على سطح المكتب
2. افتح ملف `login.html` بالمتصفح
3. احفظ الرابط في المفضلة للوصول السريع

### الطريقة الثانية: خادم محلي
```bash
# إذا كان لديك Python مثبت
cd /path/to/your/project
python -m http.server 8000

# ثم افتح المتصفح على
http://localhost:8000/login.html
```

### الطريقة الثالثة: خادم Node.js
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
cd /path/to/your/project
http-server -p 8000

# افتح المتصفح على
http://localhost:8000/login.html
```

## 2. التثبيت على خادم ويب

### متطلبات الخادم:
- خادم ويب (Apache, Nginx, IIS)
- دعم HTML5, CSS3, JavaScript
- لا يحتاج قاعدة بيانات (يستخدم Local Storage)

### خطوات التثبيت:
1. ارفع جميع الملفات إلى مجلد الموقع على الخادم
2. تأكد من أن ملف `login.html` هو الصفحة الرئيسية
3. اضبط إعدادات الخادم للسماح بـ Local Storage

## 3. التثبيت كتطبيق ويب (PWA)

### إضافة ملف manifest.json:
```json
{
  "name": "نظام إدارة أمن المعلومات",
  "short_name": "أمن المعلومات",
  "description": "نظام إدارة وتتبع الأحداث الأمنية",
  "start_url": "/login.html",
  "display": "standalone",
  "background_color": "#1e40af",
  "theme_color": "#2563eb",
  "icons": [
    {
      "src": "logo.jpg",
      "sizes": "192x192",
      "type": "image/jpeg"
    }
  ]
}
```

## 4. النسخ الاحتياطي والأمان

### النسخ الاحتياطي:
- انسخ مجلد المشروع بالكامل
- احفظ نسخة من البيانات من Local Storage
- استخدم Git لتتبع التغييرات

### الأمان:
- استخدم HTTPS في البيئة الإنتاجية
- قم بتحديث كلمات المرور الافتراضية
- راجع صلاحيات المستخدمين بانتظام

## 5. الصيانة والتحديث

### التحديثات الدورية:
- تحديث المتصفحات المدعومة
- مراجعة الأمان والثغرات
- تحسين الأداء

### المراقبة:
- تتبع استخدام النظام
- مراقبة الأخطاء
- تحليل الأداء

## 6. الدعم الفني

### المشاكل الشائعة:
- البيانات لا تحفظ: تحقق من إعدادات Local Storage
- التصميم لا يظهر: تحقق من اتصال الإنترنت للخطوط والأيقونات
- بطء في التحميل: تحسين الصور والملفات

### متطلبات النظام:
- متصفح حديث (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- JavaScript مفعل
- Local Storage مدعوم
- اتصال إنترنت للخطوط والأيقونات

## 7. التخصيص

### تغيير الشعار:
- استبدل ملف `logo.jpg` بشعارك
- الحجم المفضل: 200x200 بكسل

### تخصيص الألوان:
- عدل متغيرات CSS في ملف `styles.css`
- ابحث عن `:root` لتغيير الألوان الأساسية

### إضافة ميزات جديدة:
- عدل ملف `script.js` لإضافة وظائف جديدة
- أضف أقسام جديدة في `index.html`
- اضبط التصميم في `styles.css`

## 8. التثبيت التلقائي

### لنظام Windows:
```cmd
install.bat
```

### لنظام Linux/Mac:
```bash
chmod +x install.sh
./install.sh
```

### باستخدام Docker:
```bash
# بناء وتشغيل الحاوية
docker-compose up -d

# أو باستخدام Docker مباشرة
docker build -t isms .
docker run -d -p 80:80 --name isms isms
```

## 9. التثبيت كتطبيق PWA

### على الهاتف المحمول:
1. افتح الموقع في المتصفح
2. اضغط على "إضافة إلى الشاشة الرئيسية"
3. سيتم تثبيت التطبيق كتطبيق أصلي

### على الكمبيوتر:
1. افتح الموقع في Chrome أو Edge
2. اضغط على أيقونة التثبيت في شريط العناوين
3. اتبع التعليمات لتثبيت التطبيق

## 10. الملفات المنشأة بعد التثبيت

- `start-server.sh/bat` - بدء تشغيل الخادم
- `stop-server.sh/bat` - إيقاف الخادم
- `manifest.json` - ملف PWA
- `sw.js` - Service Worker للعمل بدون اتصال
- `Dockerfile` - للتشغيل في حاوية Docker
- `docker-compose.yml` - للتشغيل المتقدم
- اختصار سطح المكتب

## 11. الوصول للنظام

### الروابط:
- **محلي:** http://localhost:8000/login.html
- **Docker:** http://localhost/login.html
- **PWA:** من قائمة التطبيقات المثبتة

### بيانات الدخول الافتراضية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** قم بتغيير كلمة المرور الافتراضية فور التثبيت!

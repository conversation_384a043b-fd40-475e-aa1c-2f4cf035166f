#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح مشاكل المزامنة
Sync Issues Fix Tool
"""

import os
import json
import shutil
import socket
import requests
import subprocess
import platform
from datetime import datetime

class SyncIssueFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 60)
        print("🔧 أداة إصلاح مشاكل المزامنة")
        print("   Sync Issues Fix Tool")
        print("=" * 60)
        print()
    
    def check_data_directory(self):
        """فحص مجلد البيانات"""
        print("📁 فحص مجلد البيانات...")
        
        data_dir = "data"
        required_files = [
            "users.json",
            "events.json", 
            "activities.json",
            "sync_log.json"
        ]
        
        issues = []
        
        # فحص وجود المجلد
        if not os.path.exists(data_dir):
            issues.append("مجلد البيانات غير موجود")
            try:
                os.makedirs(data_dir, exist_ok=True)
                print("   ✅ تم إنشاء مجلد البيانات")
                self.fixes_applied.append("إنشاء مجلد البيانات")
            except Exception as e:
                print(f"   ❌ فشل في إنشاء مجلد البيانات: {e}")
                return False
        
        # فحص الملفات المطلوبة
        for file in required_files:
            file_path = os.path.join(data_dir, file)
            if not os.path.exists(file_path):
                issues.append(f"ملف {file} مفقود")
                try:
                    # إنشاء ملف فارغ بالتنسيق الصحيح
                    if file == "users.json":
                        default_data = {}
                    else:
                        default_data = []
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(default_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"   ✅ تم إنشاء {file}")
                    self.fixes_applied.append(f"إنشاء {file}")
                except Exception as e:
                    print(f"   ❌ فشل في إنشاء {file}: {e}")
        
        # فحص صلاحيات الكتابة
        try:
            test_file = os.path.join(data_dir, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("   ✅ صلاحيات الكتابة متاحة")
        except Exception as e:
            issues.append("لا توجد صلاحيات كتابة في مجلد البيانات")
            print(f"   ❌ مشكلة في صلاحيات الكتابة: {e}")
        
        if issues:
            self.issues_found.extend(issues)
        
        return len(issues) == 0
    
    def check_network_connectivity(self):
        """فحص الاتصال بالشبكة"""
        print("\n🌐 فحص الاتصال بالشبكة...")
        
        # فحص الاتصال بالإنترنت
        try:
            socket.create_connection(("*******", 53), timeout=3)
            print("   ✅ الاتصال بالإنترنت متاح")
        except OSError:
            print("   ⚠️  لا يوجد اتصال بالإنترنت")
            self.issues_found.append("لا يوجد اتصال بالإنترنت")
        
        # فحص المنافذ المطلوبة
        ports_to_check = [8000, 8001]
        for port in ports_to_check:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', port))
                    if result == 0:
                        print(f"   ✅ المنفذ {port} يعمل")
                    else:
                        print(f"   ❌ المنفذ {port} لا يعمل")
                        self.issues_found.append(f"المنفذ {port} غير متاح")
            except Exception as e:
                print(f"   ❌ خطأ في فحص المنفذ {port}: {e}")
    
    def check_sync_server_status(self):
        """فحص حالة خادم المزامنة"""
        print("\n🔄 فحص خادم المزامنة...")
        
        sync_urls = [
            "http://localhost:8001",
            "http://127.0.0.1:8001"
        ]
        
        # إضافة IP المحلي
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            sync_urls.append(f"http://{local_ip}:8001")
        except:
            pass
        
        working_servers = []
        
        for url in sync_urls:
            try:
                response = requests.get(f"{url}/api/sync/status", timeout=3)
                if response.status_code == 200:
                    status = response.json()
                    print(f"   ✅ خادم المزامنة يعمل: {url}")
                    print(f"      📊 المستخدمين: {status.get('users_count', 0)}")
                    print(f"      📊 الأحداث: {status.get('events_count', 0)}")
                    print(f"      📊 الأنشطة: {status.get('activities_count', 0)}")
                    working_servers.append(url)
                else:
                    print(f"   ❌ خطأ في خادم المزامنة {url}: {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"   ❌ لا يمكن الاتصال بخادم المزامنة: {url}")
            except Exception as e:
                print(f"   ❌ خطأ في فحص {url}: {e}")
        
        if not working_servers:
            self.issues_found.append("لا يوجد خادم مزامنة يعمل")
            return False
        
        return True
    
    def fix_cors_issues(self):
        """إصلاح مشاكل CORS"""
        print("\n🔒 فحص وإصلاح مشاكل CORS...")
        
        # فحص ملف خادم المزامنة
        sync_server_file = "data-sync-server.py"
        
        if os.path.exists(sync_server_file):
            try:
                with open(sync_server_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التحقق من وجود headers CORS
                if "Access-Control-Allow-Origin" in content:
                    print("   ✅ إعدادات CORS موجودة")
                else:
                    print("   ⚠️  إعدادات CORS مفقودة")
                    self.issues_found.append("إعدادات CORS مفقودة")
                
            except Exception as e:
                print(f"   ❌ خطأ في فحص ملف خادم المزامنة: {e}")
        else:
            print("   ❌ ملف خادم المزامنة غير موجود")
            self.issues_found.append("ملف خادم المزامنة غير موجود")
    
    def fix_firewall_issues(self):
        """إصلاح مشاكل جدار الحماية"""
        print("\n🛡️  فحص جدار الحماية...")
        
        system = platform.system()
        
        if system == "Windows":
            try:
                result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'], 
                                      capture_output=True, text=True, shell=True)
                if "ON" in result.stdout.upper():
                    print("   🔴 جدار الحماية مفعل")
                    print("   💡 قد تحتاج لإضافة قواعد للمنافذ 8000 و 8001")
                    
                    # اقتراح الأوامر
                    commands = [
                        'netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport=8000',
                        'netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport=8001'
                    ]
                    
                    print("   📋 أوامر إضافة القواعد:")
                    for cmd in commands:
                        print(f"      {cmd}")
                else:
                    print("   🟢 جدار الحماية غير مفعل")
            except Exception as e:
                print(f"   ⚠️  خطأ في فحص جدار الحماية: {e}")
        
        elif system == "Linux":
            try:
                result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
                if result.returncode == 0 and "active" in result.stdout.lower():
                    print("   🔴 جدار الحماية مفعل")
                    print("   💡 قد تحتاج لإضافة قواعد للمنافذ 8000 و 8001")
                    
                    commands = [
                        'sudo ufw allow 8000/tcp comment "ISMS Main Server"',
                        'sudo ufw allow 8001/tcp comment "ISMS Sync Server"'
                    ]
                    
                    print("   📋 أوامر إضافة القواعد:")
                    for cmd in commands:
                        print(f"      {cmd}")
                else:
                    print("   🟢 جدار الحماية غير مفعل أو غير موجود")
            except Exception as e:
                print(f"   ⚠️  خطأ في فحص جدار الحماية: {e}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        print("\n💾 إنشاء نسخة احتياطية...")
        
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            if os.path.exists("data"):
                shutil.copytree("data", backup_dir)
                print(f"   ✅ تم إنشاء نسخة احتياطية: {backup_dir}")
                self.fixes_applied.append(f"إنشاء نسخة احتياطية: {backup_dir}")
                return True
        except Exception as e:
            print(f"   ❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        
        return False
    
    def reset_sync_data(self):
        """إعادة تعيين بيانات المزامنة"""
        print("\n🔄 إعادة تعيين بيانات المزامنة...")
        
        try:
            # إنشاء نسخة احتياطية أولاً
            if self.create_backup():
                # إعادة تعيين ملفات المزامنة
                data_dir = "data"
                sync_files = ["sync_log.json"]
                
                for file in sync_files:
                    file_path = os.path.join(data_dir, file)
                    if os.path.exists(file_path):
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump([], f, ensure_ascii=False, indent=2)
                        print(f"   ✅ تم إعادة تعيين {file}")
                        self.fixes_applied.append(f"إعادة تعيين {file}")
                
                return True
        except Exception as e:
            print(f"   ❌ فشل في إعادة تعيين البيانات: {e}")
        
        return False
    
    def generate_fix_report(self):
        """إنشاء تقرير الإصلاح"""
        print("\n📋 تقرير الإصلاح:")
        print("=" * 50)
        
        print(f"🔍 المشاكل المكتشفة: {len(self.issues_found)}")
        for issue in self.issues_found:
            print(f"   • {issue}")
        
        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   • {fix}")
        
        if len(self.issues_found) == 0:
            print("\n🎉 لم يتم العثور على مشاكل!")
            status = "excellent"
        elif len(self.fixes_applied) > 0:
            print("\n✅ تم إصلاح بعض المشاكل")
            status = "improved"
        else:
            print("\n⚠️  توجد مشاكل تحتاج لتدخل يدوي")
            status = "needs_attention"
        
        print("=" * 50)
        return status
    
    def run_comprehensive_fix(self):
        """تشغيل الإصلاح الشامل"""
        self.print_header()
        
        print("🔍 بدء فحص وإصلاح مشاكل المزامنة...")
        print()
        
        # فحص مجلد البيانات
        self.check_data_directory()
        
        # فحص الشبكة
        self.check_network_connectivity()
        
        # فحص خادم المزامنة
        self.check_sync_server_status()
        
        # فحص CORS
        self.fix_cors_issues()
        
        # فحص جدار الحماية
        self.fix_firewall_issues()
        
        # إعادة تعيين البيانات إذا لزم الأمر
        if len(self.issues_found) > 3:
            print("\n⚠️  عدد كبير من المشاكل. هل تريد إعادة تعيين بيانات المزامنة؟")
            response = input("اكتب 'yes' للموافقة: ")
            if response.lower() in ['yes', 'y', 'نعم']:
                self.reset_sync_data()
        
        # إنشاء التقرير
        status = self.generate_fix_report()
        
        # نصائح إضافية
        print("\n💡 نصائح إضافية:")
        print("   • أعد تشغيل النظام بعد الإصلاحات")
        print("   • تأكد من تشغيل خادم المزامنة")
        print("   • استخدم test-network-sync.py للاختبار")
        print("   • راجع ملف ACCESS-LINKS.txt للروابط")
        
        return status in ["excellent", "improved"]

def main():
    """الدالة الرئيسية"""
    fixer = SyncIssueFixer()
    success = fixer.run_comprehensive_fix()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

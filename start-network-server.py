#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بدء خادم الشبكة لنظام إدارة أمن المعلومات
Start Network Server for ISMS
"""

import os
import sys
import socket
import subprocess
import webbrowser
import time
from datetime import datetime

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_port_available(port):
    """فحص إذا كان المنفذ متاح"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('0.0.0.0', port))
            return True
    except OSError:
        return False

def start_server(port=8000):
    """بدء خادم HTTP"""
    try:
        # تغيير إلى مجلد المشروع
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # بدء الخادم
        cmd = [sys.executable, '-m', 'http.server', str(port), '--bind', '0.0.0.0']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # انتظار قصير للتأكد من بدء الخادم
        time.sleep(2)
        
        # فحص إذا كان الخادم يعمل
        if process.poll() is None:
            return process
        else:
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return None

def main():
    """الوظيفة الرئيسية"""
    
    # الحصول على IP المحلي
    local_ip = get_local_ip()
    port = 8000
    
    print("🌐 بدء خادم الشبكة لنظام إدارة أمن المعلومات")
    print("=" * 60)
    print(f"📍 عنوان IP المحلي: {local_ip}")
    print(f"🔌 المنفذ: {port}")
    print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص المنفذ
    if not check_port_available(port):
        print(f"❌ المنفذ {port} مستخدم بالفعل")
        print("💡 جرب إيقاف الخادم الآخر أو استخدم منفذ مختلف")
        input("اضغط Enter للخروج...")
        return
    
    # بدء الخادم
    print(f"\n🚀 بدء الخادم...")
    server_process = start_server(port)
    
    if server_process:
        print(f"✅ تم بدء الخادم بنجاح!")
        print(f"\n🌐 روابط الوصول:")
        print(f"   • المحلي: http://localhost:{port}")
        print(f"   • الشبكة: http://{local_ip}:{port}")
        print(f"   • تسجيل الدخول: http://{local_ip}:{port}/login-fixed.html")
        print(f"   • اختبار الشبكة: http://{local_ip}:{port}/network-test.html")
        
        print(f"\n📱 للوصول من الهواتف:")
        print(f"   1. تأكد من الاتصال بنفس شبكة WiFi")
        print(f"   2. افتح المتصفح واذهب إلى: http://{local_ip}:{port}")
        print(f"   3. استخدم بيانات الدخول:")
        print(f"      • admin / admin123 (مدير النظام)")
        print(f"      • analyst / analyst123 (محلل أمني)")
        print(f"      • operator / operator123 (مشغل)")
        print(f"      • emergency / emergency123 (طوارئ)")
        
        print(f"\n💡 نصائح:")
        print(f"   • إذا لم يعمل من الأجهزة الأخرى، شغل: fix-firewall.bat")
        print(f"   • استخدم صفحة اختبار الشبكة للتشخيص")
        print(f"   • تأكد من اتصال جميع الأجهزة بنفس شبكة WiFi")
        
        print(f"\n🔧 أدوات مفيدة:")
        print(f"   • صفحة اختبار الشبكة: network-test.html")
        print(f"   • دليل الوصول من الهواتف: mobile-access-guide.md")
        print(f"   • سكريبت إصلاح جدار الحماية: fix-firewall.bat")
        
        print(f"\n✅ الخادم جاهز ويقبل الاتصالات من الشبكة!")
        print(f"🛑 اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 60)
        
        # فتح المتصفح
        try:
            print("🌐 فتح المتصفح...")
            webbrowser.open(f'http://localhost:{port}/network-test.html')
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}/login-fixed.html')
        except Exception:
            pass
        
        try:
            # انتظار إيقاف الخادم
            server_process.wait()
        except KeyboardInterrupt:
            print(f"\n🛑 إيقاف الخادم...")
            server_process.terminate()
            print("✅ تم إيقاف الخادم بنجاح")
    else:
        print("❌ فشل في بدء الخادم")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

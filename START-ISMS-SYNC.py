#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة أمن المعلومات مع المزامنة - الإصدار النهائي
Final ISMS with Synchronization Startup
"""

import subprocess
import time
import sys
import os
import socket
import webbrowser
import json
from datetime import datetime

def print_header():
    """طباعة رأس النظام"""
    print("=" * 70)
    print("🚀 نظام إدارة أمن المعلومات مع المزامنة المحسنة")
    print("   Enhanced ISMS with Network Synchronization")
    print("=" * 70)
    print()

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def setup_data_directory():
    """إعداد مجلد البيانات"""
    print("📁 إعداد مجلد البيانات...")
    
    # إنشاء المجلدات
    os.makedirs("data", exist_ok=True)
    os.makedirs("data/backups", exist_ok=True)
    
    # إنشاء الملفات الأساسية
    files = {
        "data/users.json": {},
        "data/events.json": [],
        "data/activities.json": [],
        "data/sync_log.json": []
    }
    
    for file_path, default_content in files.items():
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(default_content, f, ensure_ascii=False, indent=2)
    
    print("✅ تم إعداد مجلد البيانات")
    print()

def check_port(port):
    """فحص توفر المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0
    except:
        return False

def start_servers():
    """تشغيل الخوادم"""
    processes = []
    
    print("🌐 تشغيل الخوادم...")
    
    # تشغيل الخادم الرئيسي
    if check_port(8000):
        try:
            main_process = subprocess.Popen([
                sys.executable, 'start-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            processes.append(('main_server', main_process))
            print("   ✅ الخادم الرئيسي يعمل على المنفذ 8000")
        except Exception as e:
            print(f"   ❌ فشل في تشغيل الخادم الرئيسي: {e}")
    else:
        print("   💡 الخادم الرئيسي يعمل بالفعل على المنفذ 8000")
    
    # تشغيل خادم المزامنة
    if check_port(8001):
        try:
            sync_process = subprocess.Popen([
                sys.executable, 'data-sync-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            processes.append(('sync_server', sync_process))
            print("   ✅ خادم المزامنة يعمل على المنفذ 8001")
        except Exception as e:
            print(f"   ❌ فشل في تشغيل خادم المزامنة: {e}")
    else:
        print("   💡 خادم المزامنة يعمل بالفعل على المنفذ 8001")
    
    print()
    return processes

def wait_for_servers():
    """انتظار تشغيل الخوادم"""
    print("⏳ انتظار تشغيل الخوادم...")
    
    for i in range(10):
        main_ready = not check_port(8000)
        sync_ready = not check_port(8001)
        
        if main_ready and sync_ready:
            print("✅ جميع الخوادم جاهزة")
            break
        
        time.sleep(1)
    else:
        print("⚠️  بعض الخوادم قد لا تكون جاهزة")
    
    print()

def test_sync_server():
    """اختبار خادم المزامنة"""
    print("🔄 اختبار خادم المزامنة...")
    
    try:
        import requests
        response = requests.get("http://localhost:8001/api/sync/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("   ✅ خادم المزامنة يستجيب")
            print(f"   📊 المستخدمين: {status.get('users_count', 0)}")
            print(f"   📊 الأحداث: {status.get('events_count', 0)}")
            print(f"   📊 الأنشطة: {status.get('activities_count', 0)}")
            return True
        else:
            print(f"   ❌ خطأ في خادم المزامنة: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ لا يمكن الاتصال بخادم المزامنة: {e}")
        return False

def create_access_file(local_ip):
    """إنشاء ملف روابط الوصول"""
    content = f"""# 🌐 روابط الوصول لنظام إدارة أمن المعلومات
# تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📍 الوصول المحلي
الصفحة الرئيسية: http://localhost:8000
تسجيل الدخول: http://localhost:8000/login.html
خادم المزامنة: http://localhost:8001/api/sync/status

## 🌍 الوصول من الشبكة
الصفحة الرئيسية: http://{local_ip}:8000
تسجيل الدخول: http://{local_ip}:8000/login.html
خادم المزامنة: http://{local_ip}:8001/api/sync/status

## 🔑 بيانات الدخول
اسم المستخدم: admin
كلمة المرور: admin123

## 📋 مستخدمون إضافيون
• analyst / analyst123 (محلل أمني)
• operator / operator123 (مشغل)

## 🔧 أدوات الاختبار
python test-network-sync.py    # اختبار المزامنة الشامل
python fix-sync-issues.py      # إصلاح مشاكل المزامنة
python network-setup.py        # إعداد الشبكة

## 🛡️ إعدادات جدار الحماية (Windows)
netsh advfirewall firewall add rule name="ISMS Main Server" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="ISMS Sync Server" dir=in action=allow protocol=TCP localport=8001

## 🛡️ إعدادات جدار الحماية (Linux)
sudo ufw allow 8000/tcp comment "ISMS Main Server"
sudo ufw allow 8001/tcp comment "ISMS Sync Server"
"""
    
    try:
        with open('ACCESS-LINKS.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء ملف ACCESS-LINKS.txt")
    except Exception as e:
        print(f"⚠️  خطأ في إنشاء ملف الروابط: {e}")

def show_final_info(local_ip):
    """عرض المعلومات النهائية"""
    print("🎉 تم تشغيل النظام بنجاح!")
    print("=" * 70)
    
    print("🔗 روابط الوصول:")
    print(f"   📍 محلي: http://localhost:8000/login.html")
    print(f"   🌍 شبكة: http://{local_ip}:8000/login.html")
    
    print("\n🔑 بيانات الدخول:")
    print("   👤 اسم المستخدم: admin")
    print("   🔒 كلمة المرور: admin123")
    
    print("\n🔧 ميزات المزامنة:")
    print("   ✅ مزامنة تلقائية كل 30 ثانية")
    print("   ✅ مشاركة البيانات عبر الشبكة")
    print("   ✅ نسخ احتياطي تلقائي")
    print("   ✅ تتبع الحركات والأنشطة")
    
    print("\n💡 نصائح:")
    print("   • شارك روابط الشبكة مع المستخدمين الآخرين")
    print("   • راجع ملف ACCESS-LINKS.txt للروابط الكاملة")
    print("   • استخدم أدوات التشخيص عند الحاجة")
    
    print("\n⚠️  ملاحظات مهمة:")
    print("   • تأكد من إعدادات جدار الحماية")
    print("   • النظام يعمل على الشبكة المحلية فقط")
    print("   • النسخ الاحتياطية تُحفظ في data/backups/")
    
    print("=" * 70)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # الحصول على عنوان IP
    local_ip = get_local_ip()
    print(f"🌐 عنوان IP المحلي: {local_ip}")
    print()
    
    # إعداد مجلد البيانات
    setup_data_directory()
    
    # تشغيل الخوادم
    processes = start_servers()
    
    # انتظار تشغيل الخوادم
    wait_for_servers()
    
    # اختبار خادم المزامنة
    sync_working = test_sync_server()
    print()
    
    # فتح المتصفح
    print("🌐 فتح المتصفح...")
    try:
        webbrowser.open(f"http://localhost:8000/login.html")
        print("✅ تم فتح المتصفح")
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")
    print()
    
    # إنشاء ملف الروابط
    create_access_file(local_ip)
    print()
    
    # عرض المعلومات النهائية
    show_final_info(local_ip)
    
    # رسالة النجاح
    if sync_working:
        print("\n🎉 النظام يعمل بكامل ميزات المزامنة!")
    else:
        print("\n⚠️  النظام يعمل ولكن المزامنة قد تحتاج لإعداد إضافي")
        print("💡 شغل: python fix-sync-issues.py لحل مشاكل المزامنة")
    
    print("\n🔄 النظام جاهز للاستخدام!")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

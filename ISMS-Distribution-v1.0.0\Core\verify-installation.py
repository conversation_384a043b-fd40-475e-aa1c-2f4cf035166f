#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من سلامة تثبيت نظام إدارة أمن المعلومات
Verify ISMS Installation Integrity
"""

import os
import sys
import subprocess
import socket
import json
from pathlib import Path

class InstallationVerifier:
    def __init__(self):
        self.required_files = [
            'index.html',
            'login.html', 
            'styles.css',
            'login-styles.css',
            'script.js',
            'login-script.js',
            'auth.js',
            'demo-data.js',
            'start-server.py',
            'windows-service.py',
            'cyber-security.ico',
            'logo.jpg',
            'manifest.json'
        ]
        
        self.optional_files = [
            'windows-app.py',
            'network-config.py',
            'sw.js'
        ]
        
        self.results = {
            'files': {'passed': 0, 'failed': 0, 'missing': []},
            'python': {'status': False, 'version': None},
            'dependencies': {'passed': 0, 'failed': 0, 'missing': []},
            'network': {'status': False, 'port': None},
            'service': {'status': False, 'installed': False},
            'overall': False
        }
    
    def print_header(self):
        """طباعة رأس التقرير"""
        print("=" * 60)
        print("🔍 التحقق من سلامة تثبيت نظام إدارة أمن المعلومات")
        print("   ISMS Installation Verification")
        print("=" * 60)
        print()
    
    def check_files(self):
        """التحقق من وجود الملفات المطلوبة"""
        print("📁 التحقق من الملفات...")
        
        for file in self.required_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
                self.results['files']['passed'] += 1
            else:
                print(f"   ❌ {file} - مفقود")
                self.results['files']['failed'] += 1
                self.results['files']['missing'].append(file)
        
        print(f"\n📊 الملفات: {self.results['files']['passed']} موجود، {self.results['files']['failed']} مفقود")
        
        # التحقق من الملفات الاختيارية
        optional_found = 0
        for file in self.optional_files:
            if os.path.exists(file):
                optional_found += 1
        
        print(f"📋 الملفات الاختيارية: {optional_found}/{len(self.optional_files)} موجود")
        print()
    
    def check_python(self):
        """التحقق من Python"""
        print("🐍 التحقق من Python...")
        
        try:
            result = subprocess.run([sys.executable, '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"   ✅ {version}")
                self.results['python']['status'] = True
                self.results['python']['version'] = version
            else:
                print("   ❌ خطأ في الحصول على إصدار Python")
        except Exception as e:
            print(f"   ❌ Python غير متوفر: {e}")
        
        print()
    
    def check_dependencies(self):
        """التحقق من المتطلبات"""
        print("📦 التحقق من المتطلبات...")
        
        dependencies = [
            ('http.server', 'مكتبة HTTP Server'),
            ('socketserver', 'مكتبة Socket Server'),
            ('webbrowser', 'مكتبة المتصفح'),
            ('json', 'مكتبة JSON')
        ]
        
        # إضافة متطلبات Windows
        if sys.platform == 'win32':
            dependencies.extend([
                ('win32serviceutil', 'PyWin32 - خدمات Windows'),
                ('win32service', 'PyWin32 - خدمات Windows'),
                ('win32event', 'PyWin32 - أحداث Windows')
            ])
        
        for module, description in dependencies:
            try:
                __import__(module)
                print(f"   ✅ {description}")
                self.results['dependencies']['passed'] += 1
            except ImportError:
                print(f"   ❌ {description} - غير مثبت")
                self.results['dependencies']['failed'] += 1
                self.results['dependencies']['missing'].append(module)
        
        print(f"\n📊 المتطلبات: {self.results['dependencies']['passed']} متوفر، {self.results['dependencies']['failed']} مفقود")
        print()
    
    def check_network(self):
        """التحقق من الشبكة"""
        print("🌐 التحقق من الشبكة...")
        
        # التحقق من المنافذ المتاحة
        ports_to_check = [8000, 8001, 8002, 8080]
        available_ports = []
        
        for port in ports_to_check:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    available_ports.append(port)
                    print(f"   ✅ المنفذ {port} متاح")
            except OSError:
                print(f"   ⚠️  المنفذ {port} مشغول")
        
        if available_ports:
            self.results['network']['status'] = True
            self.results['network']['port'] = available_ports[0]
            print(f"\n📊 الشبكة: متاحة، المنفذ المقترح: {available_ports[0]}")
        else:
            print("\n📊 الشبكة: جميع المنافذ مشغولة")
        
        print()
    
    def check_service(self):
        """التحقق من خدمة Windows"""
        if sys.platform != 'win32':
            print("🔧 خدمة Windows: غير متاحة (ليس Windows)")
            print()
            return
        
        print("🔧 التحقق من خدمة Windows...")
        
        try:
            # التحقق من إمكانية تشغيل ملف الخدمة
            if os.path.exists('windows-service.py'):
                result = subprocess.run([
                    sys.executable, 'windows-service.py', 'status'
                ], capture_output=True, text=True, timeout=10)
                
                if 'متوقفة' in result.stdout or 'تعمل' in result.stdout:
                    self.results['service']['installed'] = True
                    print("   ✅ الخدمة مثبتة")
                    
                    if 'تعمل' in result.stdout:
                        self.results['service']['status'] = True
                        print("   ✅ الخدمة تعمل")
                    else:
                        print("   ⚠️  الخدمة متوقفة")
                else:
                    print("   ⚠️  الخدمة غير مثبتة")
            else:
                print("   ❌ ملف الخدمة غير موجود")
                
        except Exception as e:
            print(f"   ⚠️  لا يمكن التحقق من الخدمة: {e}")
        
        print()
    
    def test_server(self):
        """اختبار تشغيل الخادم"""
        print("🚀 اختبار تشغيل الخادم...")
        
        if not os.path.exists('start-server.py'):
            print("   ❌ ملف الخادم غير موجود")
            return
        
        try:
            # محاولة تشغيل الخادم لثواني قليلة
            process = subprocess.Popen([
                sys.executable, 'start-server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # انتظار قصير
            import time
            time.sleep(3)
            
            # إنهاء العملية
            process.terminate()
            process.wait(timeout=5)
            
            print("   ✅ الخادم يمكن تشغيله")
            
        except Exception as e:
            print(f"   ⚠️  مشكلة في تشغيل الخادم: {e}")
        
        print()
    
    def generate_report(self):
        """إنشاء تقرير شامل"""
        print("📋 تقرير التحقق النهائي:")
        print("-" * 40)
        
        # حساب النتيجة الإجمالية
        total_checks = 0
        passed_checks = 0
        
        # الملفات
        if self.results['files']['failed'] == 0:
            print("   ✅ الملفات: مكتملة")
            passed_checks += 1
        else:
            print(f"   ❌ الملفات: {self.results['files']['failed']} مفقود")
        total_checks += 1
        
        # Python
        if self.results['python']['status']:
            print("   ✅ Python: متوفر")
            passed_checks += 1
        else:
            print("   ❌ Python: غير متوفر")
        total_checks += 1
        
        # المتطلبات
        if self.results['dependencies']['failed'] == 0:
            print("   ✅ المتطلبات: مكتملة")
            passed_checks += 1
        else:
            print(f"   ❌ المتطلبات: {self.results['dependencies']['failed']} مفقود")
        total_checks += 1
        
        # الشبكة
        if self.results['network']['status']:
            print("   ✅ الشبكة: متاحة")
            passed_checks += 1
        else:
            print("   ⚠️  الشبكة: مشاكل في المنافذ")
        total_checks += 1
        
        # النتيجة الإجمالية
        success_rate = (passed_checks / total_checks) * 100
        
        print("-" * 40)
        print(f"📊 معدل النجاح: {success_rate:.1f}% ({passed_checks}/{total_checks})")
        
        if success_rate >= 75:
            print("🎉 التثبيت سليم ويمكن تشغيل النظام!")
            self.results['overall'] = True
        else:
            print("⚠️  يحتاج التثبيت إلى إصلاحات")
        
        print()
    
    def provide_solutions(self):
        """تقديم حلول للمشاكل"""
        if self.results['overall']:
            return
        
        print("🔧 حلول المشاكل المكتشفة:")
        print("-" * 40)
        
        # الملفات المفقودة
        if self.results['files']['missing']:
            print("📁 الملفات المفقودة:")
            for file in self.results['files']['missing']:
                print(f"   - {file}")
            print("   💡 الحل: أعد تحميل/نسخ الملفات من المصدر")
            print()
        
        # Python
        if not self.results['python']['status']:
            print("🐍 Python غير متوفر:")
            print("   💡 الحل: حمل وثبت Python من https://python.org")
            print("   ⚠️  تأكد من تحديد 'Add Python to PATH'")
            print()
        
        # المتطلبات
        if self.results['dependencies']['missing']:
            print("📦 المتطلبات المفقودة:")
            for dep in self.results['dependencies']['missing']:
                print(f"   - {dep}")
            print("   💡 الحل: pip install pywin32 requests")
            print()
        
        # الشبكة
        if not self.results['network']['status']:
            print("🌐 مشاكل الشبكة:")
            print("   💡 الحل: أغلق البرامج التي تستخدم المنافذ")
            print("   💡 أو استخدم منفذ مختلف في الإعدادات")
            print()
    
    def run_verification(self):
        """تشغيل جميع فحوصات التحقق"""
        self.print_header()
        
        self.check_files()
        self.check_python()
        self.check_dependencies()
        self.check_network()
        self.check_service()
        self.test_server()
        
        self.generate_report()
        self.provide_solutions()
        
        return self.results['overall']

def main():
    """الدالة الرئيسية"""
    verifier = InstallationVerifier()
    success = verifier.run_verification()
    
    print("=" * 60)
    if success:
        print("🎉 التحقق مكتمل - النظام جاهز للتشغيل!")
        print("\n🚀 لتشغيل النظام:")
        print("   python start-server.py")
    else:
        print("⚠️  يرجى إصلاح المشاكل المذكورة أعلاه")
    
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

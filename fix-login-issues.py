#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص وإصلاح مشاكل تسجيل الدخول
Login Issues Diagnosis and Fix Tool
"""

import os
import sys
import json
import shutil
from datetime import datetime

class LoginIssueFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 60)
        print("🔧 أداة تشخيص وإصلاح مشاكل تسجيل الدخول")
        print("   Login Issues Diagnosis and Fix Tool")
        print("=" * 60)
        print()
    
    def check_required_files(self):
        """التحقق من الملفات المطلوبة"""
        print("📁 التحقق من الملفات المطلوبة...")
        
        required_files = {
            'login.html': 'صفحة تسجيل الدخول',
            'login-styles.css': 'ملف تنسيق تسجيل الدخول',
            'auth.js': 'ملف المصادقة',
            'index.html': 'الصفحة الرئيسية',
            'logo.jpg': 'شعار النظام'
        }
        
        missing_files = []
        for file, description in required_files.items():
            if os.path.exists(file):
                print(f"   ✅ {file} - {description}")
            else:
                print(f"   ❌ {file} - {description} (مفقود)")
                missing_files.append(file)
                self.issues_found.append(f"ملف مفقود: {file}")
        
        if missing_files:
            self.fix_missing_files(missing_files)
        
        print()
        return len(missing_files) == 0
    
    def fix_missing_files(self, missing_files):
        """إصلاح الملفات المفقودة"""
        print("🔧 إصلاح الملفات المفقودة...")
        
        for file in missing_files:
            if file == 'logo.jpg':
                self.create_default_logo()
            elif file == 'login.html':
                self.create_login_html()
            elif file == 'login-styles.css':
                self.create_login_css()
            elif file == 'auth.js':
                self.create_auth_js()
            elif file == 'index.html':
                self.create_index_html()
    
    def create_default_logo(self):
        """إنشاء شعار افتراضي"""
        print("   🎨 إنشاء شعار افتراضي...")
        
        # إنشاء ملف SVG بسيط كشعار
        svg_content = '''<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="40" cy="40" r="35" fill="url(#grad1)" />
  <path d="M25 35 L35 45 L55 25" stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="40" cy="40" r="35" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>'''
        
        try:
            with open('logo.svg', 'w', encoding='utf-8') as f:
                f.write(svg_content)
            print("   ✅ تم إنشاء logo.svg")
            self.fixes_applied.append("إنشاء شعار افتراضي")
        except Exception as e:
            print(f"   ❌ فشل في إنشاء الشعار: {e}")
    
    def check_login_functionality(self):
        """فحص وظائف تسجيل الدخول"""
        print("🔐 فحص وظائف تسجيل الدخول...")
        
        # فحص ملف auth.js
        if os.path.exists('auth.js'):
            try:
                with open('auth.js', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التحقق من وجود الوظائف الأساسية
                required_functions = [
                    'handleLogin',
                    'authenticateUser',
                    'loginSuccess',
                    'showError',
                    'hashPassword'
                ]
                
                missing_functions = []
                for func in required_functions:
                    if func not in content:
                        missing_functions.append(func)
                        print(f"   ❌ وظيفة مفقودة: {func}")
                        self.issues_found.append(f"وظيفة مفقودة في auth.js: {func}")
                    else:
                        print(f"   ✅ وظيفة موجودة: {func}")
                
                if missing_functions:
                    self.fix_auth_functions()
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة auth.js: {e}")
                self.issues_found.append("خطأ في قراءة ملف auth.js")
        
        print()
    
    def fix_auth_functions(self):
        """إصلاح وظائف المصادقة"""
        print("🔧 إصلاح وظائف المصادقة...")
        
        # إنشاء نسخة احتياطية
        if os.path.exists('auth.js'):
            shutil.copy2('auth.js', f'auth.js.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            print("   💾 تم إنشاء نسخة احتياطية من auth.js")
        
        # إعادة إنشاء ملف auth.js
        self.create_auth_js()
        self.fixes_applied.append("إصلاح وظائف المصادقة")
    
    def create_auth_js(self):
        """إنشاء ملف auth.js محسن"""
        print("   📝 إنشاء ملف auth.js محسن...")
        
        auth_content = '''// Enhanced Authentication System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.users = this.loadUsers();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeDefaultUsers();
        this.checkExistingSession();
    }

    initializeDefaultUsers() {
        if (Object.keys(this.users).length === 0) {
            this.users = {
                'admin': {
                    username: 'admin',
                    password: this.hashPassword('admin123'),
                    fullName: 'مدير النظام',
                    role: 'admin',
                    isActive: true
                }
            };
            this.saveUsers();
        }
    }

    setupEventListeners() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        const passwordToggle = document.getElementById('passwordToggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                this.togglePasswordVisibility();
            });
        }
    }

    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        this.showLoading(true);

        // Simulate network delay
        setTimeout(() => {
            const user = this.authenticateUser(username, password);
            if (user) {
                this.loginSuccess(user);
            } else {
                this.showError('اسم المستخدم أو كلمة المرور غير صحيح');
                this.showLoading(false);
            }
        }, 1000);
    }

    authenticateUser(username, password) {
        const user = this.users[username];
        if (!user || !user.isActive) {
            return null;
        }

        const hashedPassword = this.hashPassword(password);
        return user.password === hashedPassword ? user : null;
    }

    loginSuccess(user) {
        this.currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(user));
        localStorage.setItem('loginTime', new Date().toISOString());
        
        // Show success message
        this.showSuccess('تم تسجيل الدخول بنجاح');
        
        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }

    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    showLoading(show) {
        const loginBtn = document.getElementById('loginBtn');
        if (!loginBtn) return;

        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        if (show) {
            loginBtn.disabled = true;
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.style.display = 'block';
        } else {
            loginBtn.disabled = false;
            if (btnText) btnText.style.display = 'block';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.message-popup');
        existingMessages.forEach(msg => msg.remove());

        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-popup ${type}`;
        messageDiv.innerHTML = `
            <i class="fas ${type === 'error' ? 'fa-exclamation-triangle' : 'fa-check-circle'}"></i>
            <span>${message}</span>
        `;

        // Add styles
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#dc2626' : '#059669'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(messageDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => messageDiv.remove(), 300);
            }
        }, 5000);
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#passwordToggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    checkExistingSession() {
        const currentUser = localStorage.getItem('currentUser');
        const loginTime = localStorage.getItem('loginTime');
        
        if (currentUser && loginTime) {
            const loginDate = new Date(loginTime);
            const now = new Date();
            const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
            
            // Session valid for 8 hours
            if (hoursDiff < 8) {
                window.location.href = 'index.html';
                return;
            }
        }
        
        // Clear expired session
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
    }

    loadUsers() {
        try {
            return JSON.parse(localStorage.getItem('systemUsers')) || {};
        } catch (error) {
            console.error('Error loading users:', error);
            return {};
        }
    }

    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(this.users));
        } catch (error) {
            console.error('Error saving users:', error);
        }
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize auth manager
const authManager = new AuthManager();
'''
        
        try:
            with open('auth.js', 'w', encoding='utf-8') as f:
                f.write(auth_content)
            print("   ✅ تم إنشاء ملف auth.js محسن")
            self.fixes_applied.append("إنشاء ملف auth.js محسن")
        except Exception as e:
            print(f"   ❌ فشل في إنشاء auth.js: {e}")
    
    def check_css_issues(self):
        """فحص مشاكل CSS"""
        print("🎨 فحص مشاكل CSS...")
        
        if os.path.exists('login-styles.css'):
            try:
                with open('login-styles.css', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التحقق من وجود الفئات المهمة
                required_classes = [
                    '.login-container',
                    '.login-card',
                    '.login-form',
                    '.form-group',
                    '.login-btn'
                ]
                
                missing_classes = []
                for css_class in required_classes:
                    if css_class not in content:
                        missing_classes.append(css_class)
                        print(f"   ❌ فئة CSS مفقودة: {css_class}")
                        self.issues_found.append(f"فئة CSS مفقودة: {css_class}")
                    else:
                        print(f"   ✅ فئة CSS موجودة: {css_class}")
                
                if missing_classes:
                    self.fix_css_issues()
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة login-styles.css: {e}")
                self.issues_found.append("خطأ في قراءة ملف CSS")
        else:
            print("   ❌ ملف login-styles.css غير موجود")
            self.create_login_css()
        
        print()
    
    def fix_css_issues(self):
        """إصلاح مشاكل CSS"""
        print("🔧 إصلاح مشاكل CSS...")
        
        # إنشاء نسخة احتياطية
        if os.path.exists('login-styles.css'):
            shutil.copy2('login-styles.css', f'login-styles.css.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            print("   💾 تم إنشاء نسخة احتياطية من login-styles.css")
        
        # إعادة إنشاء ملف CSS
        self.create_login_css()
        self.fixes_applied.append("إصلاح مشاكل CSS")
    
    def create_login_css(self):
        """إنشاء ملف CSS محسن"""
        print("   📝 إنشاء ملف CSS محسن...")
        
        # سأضع CSS مبسط ولكن فعال
        css_content = '''/* Enhanced Login Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    direction: rtl;
}

.login-container {
    width: 100%;
    max-width: 400px;
    margin: 2rem;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.logo-container {
    margin-bottom: 1rem;
}

.logo-image, .logo-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
}

.logo-icon {
    font-size: 2rem;
    color: #1e40af;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #dbeafe;
    margin: 0 auto;
}

.system-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.system-subtitle {
    color: #6b7280;
    font-size: 0.9rem;
}

.login-form-section {
    padding: 2rem;
}

.form-header h2 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-header p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #374151;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-group input:focus {
    outline: none;
    border-color: #2563eb;
    background: white;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.forgot-password {
    color: #2563eb;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: none;
}

.error-message {
    background: #fef2f2;
    color: #dc2626;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: none;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid #fecaca;
}

.login-footer {
    padding: 1rem 2rem;
    text-align: center;
    background: #f9fafb;
    color: #6b7280;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        margin: 1rem;
    }
    
    .login-header, .login-form-section {
        padding: 1.5rem;
    }
}
'''
        
        try:
            with open('login-styles.css', 'w', encoding='utf-8') as f:
                f.write(css_content)
            print("   ✅ تم إنشاء ملف CSS محسن")
            self.fixes_applied.append("إنشاء ملف CSS محسن")
        except Exception as e:
            print(f"   ❌ فشل في إنشاء CSS: {e}")
    
    def generate_report(self):
        """إنشاء تقرير الإصلاح"""
        print("📋 تقرير الإصلاح:")
        print("-" * 40)
        
        if not self.issues_found:
            print("✅ لم يتم العثور على مشاكل!")
        else:
            print(f"🔍 تم العثور على {len(self.issues_found)} مشكلة:")
            for issue in self.issues_found:
                print(f"   • {issue}")
        
        print()
        
        if self.fixes_applied:
            print(f"🔧 تم تطبيق {len(self.fixes_applied)} إصلاح:")
            for fix in self.fixes_applied:
                print(f"   • {fix}")
        else:
            print("ℹ️  لم يتم تطبيق أي إصلاحات")
        
        print()
        print("💡 التوصيات:")
        print("   1. أعد تشغيل الخادم")
        print("   2. امسح cache المتصفح (Ctrl+F5)")
        print("   3. تأكد من تشغيل JavaScript")
        print("   4. جرب متصفح مختلف")
        print()
    
    def run_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        self.print_header()
        
        # فحص الملفات المطلوبة
        self.check_required_files()
        
        # فحص وظائف تسجيل الدخول
        self.check_login_functionality()
        
        # فحص مشاكل CSS
        self.check_css_issues()
        
        # إنشاء التقرير
        self.generate_report()
        
        print("=" * 60)
        print("🎉 انتهى التشخيص والإصلاح!")
        print("🚀 يمكنك الآن تشغيل الخادم واختبار تسجيل الدخول")
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    try:
        fixer = LoginIssueFixer()
        fixer.run_diagnosis()
    except KeyboardInterrupt:
        print("\n\n⚠️  تم إيقاف التشخيص بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التشخيص: {e}")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة أمن المعلومات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 450px;
            width: 100%;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
        }

        .logo h1 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .logo p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #7f8c8d;
            cursor: pointer;
            padding: 5px;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remember-me input[type="checkbox"] {
            width: auto;
        }

        .forgot-password {
            color: #3498db;
            text-decoration: none;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-btn {
            width: 100%;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-loading.active {
            display: flex;
        }

        .btn-text.loading {
            display: none;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
            text-align: center;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
            text-align: center;
        }

        .quick-login {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .quick-login h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .quick-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .quick-btn:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .system-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .quick-buttons {
                flex-direction: column;
            }

            .quick-btn {
                width: 100%;
            }
        }
    </style>
    <script src="network-sync-client.js"></script>
    <script src="sync-integration.js"></script>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <img src="logo.jpg" alt="شعار النظام" onerror="this.style.display='none'">
            <h1>🛡️ نظام إدارة أمن المعلومات</h1>
            <p>تسجيل الدخول الآمن</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <div class="input-container">
                    <input type="text" id="username" name="username" required autocomplete="username">
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <div class="input-container">
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                    <button type="button" class="password-toggle" id="passwordToggle">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <div class="form-options">
                <label class="remember-me">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    تذكرني
                </label>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span class="btn-text">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول
                </span>
                <div class="btn-loading">
                    <div class="spinner"></div>
                    جاري التحقق...
                </div>
            </button>
        </form>

        <div class="quick-login">
            <h3>تسجيل دخول سريع:</h3>
            <div class="quick-buttons">
                <button class="quick-btn" onclick="quickLogin('admin', 'admin123')">مدير النظام</button>
                <button class="quick-btn" onclick="quickLogin('analyst', 'analyst123')">محلل أمني</button>
                <button class="quick-btn" onclick="quickLogin('operator', 'operator123')">مشغل</button>
            </div>
        </div>

        <div class="system-info">
            <p><strong>معلومات النظام:</strong></p>
            <p>الإصدار: 2.0 | آخر تحديث: <span id="lastUpdate"></span></p>
            <p>حالة الخادم: <span id="serverStatus">🟢 متصل</span></p>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner" style="margin: 0 auto 15px;"></div>
            <p>جاري تسجيل الدخول...</p>
        </div>
    </div>

    <script src="fixed-auth-system.js"></script>
    <script>
        // متغيرات عامة
        let isLoading = false;

        // تحميل الصفحة
        window.onload = async function() {
            console.log('🔄 تحميل صفحة تسجيل الدخول...');

            // تحديث معلومات النظام
            document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('ar-SA');

            // انتظار تهيئة نظام المصادقة
            if (window.fixedAuthManager) {
                try {
                    await window.fixedAuthManager.waitForInit();
                    console.log('✅ تم تهيئة نظام المصادقة');

                    // فحص الجلسة الحالية
                    const hasSession = await window.fixedAuthManager.checkCurrentSession();
                    if (hasSession) {
                        console.log('🔄 يوجد جلسة نشطة، انتقال للصفحة الرئيسية...');
                        return; // سيتم الانتقال تلقائياً
                    }
                } catch (error) {
                    console.error('خطأ في تهيئة نظام المصادقة:', error);
                    showError('خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة.');
                }
            } else {
                console.error('نظام المصادقة غير متاح');
                showError('نظام المصادقة غير متاح. يرجى إعادة تحميل الصفحة.');
            }

            // إعداد مستمعي الأحداث
            setupEventListeners();

            console.log('✅ تم تحميل صفحة تسجيل الدخول بنجاح');
        };

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // نموذج تسجيل الدخول
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }

            // زر إظهار/إخفاء كلمة المرور
            const passwordToggle = document.getElementById('passwordToggle');
            if (passwordToggle) {
                passwordToggle.addEventListener('click', togglePasswordVisibility);
            }

            // مسح الأخطاء عند الكتابة
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');

            if (usernameField) {
                usernameField.addEventListener('input', clearMessages);
            }

            if (passwordField) {
                passwordField.addEventListener('input', clearMessages);
            }

            // Enter للتسجيل
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !isLoading) {
                    const activeElement = document.activeElement;
                    if (activeElement && (activeElement.id === 'username' || activeElement.id === 'password')) {
                        e.preventDefault();
                        handleLogin(e);
                    }
                }
            });
        }

        // معالج تسجيل الدخول
        async function handleLogin(event) {
            if (event) {
                event.preventDefault();
            }

            if (isLoading) {
                console.log('عملية تسجيل دخول جارية بالفعل...');
                return;
            }

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // التحقق من البيانات
            if (!username || !password) {
                showError('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            try {
                setLoading(true);
                clearMessages();

                console.log('🔐 محاولة تسجيل دخول...');

                // التأكد من وجود نظام المصادقة
                if (!window.fixedAuthManager) {
                    throw new Error('نظام المصادقة غير متاح');
                }

                // انتظار التهيئة
                await window.fixedAuthManager.waitForInit();

                // محاولة تسجيل الدخول
                const result = await window.fixedAuthManager.login(username, password, rememberMe);

                if (result.success) {
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...');
                    console.log('✅ تم تسجيل الدخول بنجاح');

                    // تأخير قصير لإظهار رسالة النجاح
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    throw new Error('فشل في تسجيل الدخول');
                }

            } catch (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                showError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
            } finally {
                setLoading(false);
            }
        }

        // تسجيل دخول سريع
        async function quickLogin(username, password) {
            if (isLoading) return;

            // ملء الحقول
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('rememberMe').checked = false;

            // تسجيل الدخول
            await handleLogin();
        }

        // تبديل إظهار كلمة المرور
        function togglePasswordVisibility() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.querySelector('#passwordToggle i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // تعيين حالة التحميل
        function setLoading(loading) {
            isLoading = loading;
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');
            const loadingOverlay = document.getElementById('loadingOverlay');

            if (loading) {
                loginBtn.disabled = true;
                btnText.classList.add('loading');
                btnLoading.classList.add('active');
                loadingOverlay.style.display = 'flex';
            } else {
                loginBtn.disabled = false;
                btnText.classList.remove('loading');
                btnLoading.classList.remove('active');
                loadingOverlay.style.display = 'none';
            }
        }

        // عرض رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            successDiv.style.display = 'none';
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // عرض رسالة نجاح
        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.style.display = 'none';
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // مسح الرسائل
        function clearMessages() {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
        }

        // نسيت كلمة المرور
        function showForgotPassword() {
            alert('للحصول على كلمة مرور جديدة، يرجى الاتصال بمدير النظام.\n\nبيانات الدخول الافتراضية:\nadmin / admin123\nanalyst / analyst123\noperator / operator123');
        }

        // معالج الأخطاء العام
        window.addEventListener('error', function(e) {
            console.error('خطأ في الصفحة:', e.error);
            if (!isLoading) {
                showError('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.');
            }
        });

        // معالج الأخطاء للوعود
        window.addEventListener('unhandledrejection', function(e) {
            console.error('خطأ في الوعد:', e.reason);
            if (!isLoading) {
                showError('حدث خطأ في النظام. يرجى المحاولة مرة أخرى.');
            }
            e.preventDefault();
        });
    </script>
</body>
</html>
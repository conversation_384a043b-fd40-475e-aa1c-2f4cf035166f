# 🔧 دليل حل مشاكل تسجيل الدخول

## نظرة عامة
هذا الدليل يوضح كيفية تشخيص وحل جميع مشاكل تسجيل الدخول في نظام إدارة أمن المعلومات.

## 🔍 التشخيص السريع

### 1. تشغيل أداة التشخيص التلقائي:
```bash
python fix-login-issues.py
```

### 2. تشغيل الخادم البسيط:
```bash
python simple-server.py
```

## 🔑 بيانات الدخول الصحيحة

### المستخدمون الافتراضيون:
- **المدير:**
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

- **المحلل:**
  - اسم المستخدم: `analyst`
  - كلمة المرور: `analyst123`

- **المشغل:**
  - اسم المستخدم: `operator`
  - كلمة المرور: `operator123`

## 🚨 المشاكل الشائعة والحلول

### المشكلة 1: صفحة تسجيل الدخول لا تظهر

#### الأعراض:
- صفحة فارغة أو خطأ 404
- لا تحمل CSS أو JavaScript

#### الحلول:
1. **تأكد من تشغيل الخادم:**
   ```bash
   python start-server.py
   # أو
   python simple-server.py
   ```

2. **تحقق من الملفات المطلوبة:**
   - `login.html`
   - `login-styles.css`
   - `auth.js`
   - `logo.jpg`

3. **امسح cache المتصفح:**
   - اضغط `Ctrl + F5` (Windows/Linux)
   - اضغط `Cmd + Shift + R` (macOS)

### المشكلة 2: رسالة "اسم المستخدم أو كلمة المرور غير صحيح"

#### الأعراض:
- رسالة خطأ حتى مع البيانات الصحيحة
- لا يتم قبول أي بيانات دخول

#### الحلول:
1. **تأكد من البيانات:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
   - تأكد من عدم وجود مسافات إضافية

2. **إعادة تعيين بيانات المستخدمين:**
   ```javascript
   // افتح Developer Tools (F12) واكتب:
   localStorage.removeItem('systemUsers');
   location.reload();
   ```

3. **تحقق من JavaScript:**
   - افتح Developer Tools (F12)
   - تحقق من وجود أخطاء في Console

### المشكلة 3: الصفحة تحمل ولكن لا تستجيب

#### الأعراض:
- الأزرار لا تعمل
- لا يحدث شيء عند الضغط على "دخول"
- JavaScript معطل

#### الحلول:
1. **تفعيل JavaScript:**
   - تأكد من تفعيل JavaScript في المتصفح
   - جرب متصفح مختلف

2. **تحقق من الأخطاء:**
   - افتح Developer Tools (F12)
   - انظر إلى تبويب Console
   - ابحث عن أخطاء حمراء

3. **إعادة تحميل ملف auth.js:**
   ```bash
   python fix-login-issues.py
   ```

### المشكلة 4: تسجيل الدخول ناجح ولكن لا يحدث انتقال

#### الأعراض:
- رسالة "تم تسجيل الدخول بنجاح"
- لا ينتقل إلى الصفحة الرئيسية
- يبقى في صفحة تسجيل الدخول

#### الحلول:
1. **تحقق من وجود index.html:**
   ```bash
   # تأكد من وجود الملف
   ls index.html
   ```

2. **انتقال يدوي:**
   ```
   http://localhost:8000/index.html
   ```

3. **مسح الجلسات المحفوظة:**
   ```javascript
   // في Developer Tools:
   localStorage.clear();
   sessionStorage.clear();
   location.reload();
   ```

### المشكلة 5: مشاكل في التصميم والألوان

#### الأعراض:
- صفحة بدون تنسيق
- ألوان غريبة أو خطوط غير صحيحة
- تخطيط مكسور

#### الحلول:
1. **إعادة إنشاء ملف CSS:**
   ```bash
   python fix-login-issues.py
   ```

2. **تحقق من اتصال الإنترنت:**
   - الخطوط من Google Fonts تحتاج إنترنت
   - أيقونات Font Awesome تحتاج إنترنت

3. **استخدام ملف CSS محلي:**
   - تحميل الخطوط والأيقونات محلياً

## 🛠️ أدوات التشخيص المتقدم

### 1. فحص الشبكة:
```bash
python test-network-access.py
```

### 2. فحص الملفات:
```bash
python verify-installation.py
```

### 3. إعادة إنشاء الملفات:
```bash
python fix-login-issues.py
```

## 🌐 مشاكل المتصفح

### Chrome/Edge:
- امسح البيانات: `Ctrl + Shift + Delete`
- وضع التصفح الخفي: `Ctrl + Shift + N`

### Firefox:
- امسح البيانات: `Ctrl + Shift + Delete`
- وضع التصفح الخاص: `Ctrl + Shift + P`

### Safari:
- امسح البيانات: `Safari > Clear History`
- وضع التصفح الخاص: `File > New Private Window`

## 📱 مشاكل الأجهزة المحمولة

### Android:
1. تأكد من الاتصال بنفس Wi-Fi
2. استخدم Chrome أو Firefox
3. فعّل JavaScript في إعدادات المتصفح

### iOS:
1. تأكد من الاتصال بنفس Wi-Fi
2. استخدم Safari أو Chrome
3. امسح cache Safari من الإعدادات

## 🔒 مشاكل الأمان

### جدار الحماية:
```cmd
# Windows - شغل كمدير
netsh advfirewall firewall add rule name="ISMS Server" dir=in action=allow protocol=TCP localport=8000
```

### مكافح الفيروسات:
- أضف مجلد النظام إلى الاستثناءات
- أوقف الحماية مؤقتاً للاختبار

## 🔄 إعادة التثبيت الكاملة

إذا فشلت جميع الحلول:

1. **احتفظ بنسخة احتياطية:**
   ```bash
   mkdir backup
   copy *.html backup/
   copy *.css backup/
   copy *.js backup/
   ```

2. **إعادة تحميل الملفات:**
   - احذف الملفات التالفة
   - أعد تحميل النظام

3. **تشغيل التشخيص:**
   ```bash
   python fix-login-issues.py
   ```

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم الفني:
- نظام التشغيل والإصدار
- نوع المتصفح والإصدار
- رسائل الخطأ الدقيقة
- لقطات شاشة للمشكلة

### ملفات السجلات:
- Developer Tools Console
- Network tab في Developer Tools
- ملفات السجل في مجلد النظام

## ✅ قائمة التحقق السريعة

- [ ] الخادم يعمل
- [ ] الملفات موجودة (HTML, CSS, JS)
- [ ] JavaScript مفعل
- [ ] بيانات الدخول صحيحة
- [ ] cache المتصفح ممسوح
- [ ] جدار الحماية مُعد
- [ ] الشبكة تعمل
- [ ] المنفذ 8000 متاح

---

## 🎯 نصائح للوقاية

1. **احتفظ بنسخة احتياطية** من الملفات
2. **اختبر النظام بانتظام** على متصفحات مختلفة
3. **راقب سجلات الأخطاء** في Developer Tools
4. **حدّث المتصفحات** بانتظام
5. **استخدم أدوات التشخيص** المدمجة

**تاريخ آخر تحديث:** 2024-12-10
